# Python 编码规范

## 1. 概述

本规范基于项目的实际需求制定，遵循 PEP 8 标准，并结合了项目的技术栈特点（FastAPI、SQLAlchemy、异步编程等）。

### 核心原则
- **简洁性**: 不要过度设计，保持简单，保持可维护性
- **可读性**: 代码是写给人看的，不是写给机器看的
- **一致性**: 保持代码风格的一致性
- **可维护性**: 优先考虑代码的可维护性和扩展性

### 项目技术栈
- **FastAPI**: 异步 Web 框架
- **SQLAlchemy**: 数据库 ORM
- **Pydantic**: 数据验证和设置管理
- **Structlog**: 结构化日志
- **Redis**: 缓存和会话管理
- **Crawl4AI**: 智能爬虫
- **Agno**: 多智能体框架

### 工程化工具栈
- **Ruff**: 极速 Linter 和 Formatter，替代 flake8、isort 等
- **Black**: 代码格式化工具
- **Mypy**: 静态类型检查
- **Pytest**: 测试框架

## 2. 工程化规范

### 2.1 pyproject.toml 统一配置

项目使用 `pyproject.toml` 作为配置中心，统一管理所有工具的配置：

### 2.2 FastAPI 特定风格
```python
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import Optional
import structlog

logger = structlog.get_logger(__name__)
router = APIRouter()

class UserResponse(BaseModel):
    """用户响应模型"""
    id: int
    name: str
    email: str

@router.get("/users/{user_id}", response_model=UserResponse)
async def get_user(
    user_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> UserResponse:
    """获取用户信息"""
    logger.info("Getting user", user_id=user_id)

    user = await db.execute(
        select(User).where(User.id == user_id)
    )
    user = user.scalar_one_or_none()

    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    return UserResponse.model_validate(user)
```

### 2.3 项目特定命名
```python
# API 路由器
user_router = APIRouter()
crawler_router = APIRouter()

# 数据模型
class UserModel(BaseModel):
    pass

# 数据库表
class User(Base):
    __tablename__ = "users"

# 异常类
class UserNotFoundError(BaseException):
    pass

# 配置类
class DatabaseSettings(BaseSettings):
    pass
```

### 2.4 导入规范

#### 2.4.1 导入顺序
```python
# 1. 标准库
import os
import sys
from typing import Optional, List
from contextlib import asynccontextmanager

# 2. 第三方库
from fastapi import FastAPI, Depends
from pydantic import BaseModel
from sqlalchemy import Column, String

# 3. 项目内部模块
from app.core.config import settings
from app.utils.logger import get_logger
from app.schemas.response import BaseResponse
```

#### 2.4.2 导入规则
```python
# 优先使用 from import
from app.core.config import settings

# 避免使用 import *
# 错误示例
from app.utils import *

# 正确示例
from app.utils.logger import get_logger
from app.utils.helpers import format_date
```

### 2.5 配置类定义
```python
from pydantic_settings import BaseSettings
from pydantic import Field, ConfigDict
from typing import Optional

class DatabaseSettings(BaseSettings):
    """数据库配置"""
    model_config = ConfigDict(env_prefix="DB_")

    host: str = Field(default="localhost", description="数据库主机")
    port: int = Field(default=3306, description="数据库端口")
    username: str = Field(..., description="数据库用户名")
    password: str = Field(..., description="数据库密码")
    database: str = Field(..., description="数据库名称")

    @property
    def url(self) -> str:
        return f"mysql+pymysql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"

class AppSettings(BaseSettings):
    """应用配置"""
    model_config = ConfigDict(
        env_file=".env",
        extra="ignore"
    )

    app_name: str = "ai-assistant"
    debug: bool = False
    log_level: str = "INFO"

    # 数据库配置
    database: DatabaseSettings = DatabaseSettings()

    # Redis 配置
    redis_url: str = "redis://localhost:6379/0"
```

### 2.6 配置使用
```python
from app.core.config import settings

# 使用配置
logger.info("Starting application", app_name=settings.app_name)

# 在依赖注入中使用
@lru_cache()
def get_settings():
    return settings
```


### 2.7 测试规范

#### 2.7.1 测试文件结构
```
tests/
├── conftest.py              # 测试配置和夹具
├── unit/                    # 单元测试
│   ├── test_services.py
│   ├── test_utils.py
│   └── test_models.py
├── integration/             # 集成测试
│   ├── test_api.py
│   ├── test_database.py
│   └── test_crawler.py
└── fixtures/                # 测试数据
    ├── users.json
    └── responses.json
```

### 2.8 FastAPI规范

#### 2.8.1 FastAPI 依赖注入
```python
# 推荐：使用依赖注入
@router.get("/users/{user_id}")
async def get_user(
    user_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    # 业务逻辑
    pass

# 不推荐：直接创建依赖
@router.get("/users/{user_id}")
async def get_user(user_id: int):
    db = create_db_session()  # 不推荐
    # 业务逻辑
    pass
```

#### 2.8.2 Pydantic 模型分离
```python
# 推荐：按用途分离模型
class UserCreate(BaseModel):
    name: str
    email: str

class UserUpdate(BaseModel):
    name: Optional[str] = None
    email: Optional[str] = None

class UserResponse(BaseModel):
    id: int
    name: str
    email: str
    created_at: datetime
```

#### 2.8.3 异步编程最佳实践
```python
# 推荐：使用 async/await
async def get_users() -> List[User]:
    async with AsyncSessionLocal() as db:
        result = await db.execute(select(User))
        return result.scalars().all()

# 不推荐：同步代码
def get_users() -> List[User]:
    db = SessionLocal()
    try:
        return db.query(User).all()
    finally:
        db.close()
```

#### 2.8.4 结构化日志
```python
import structlog

logger = structlog.get_logger(__name__)

# 推荐：结构化日志
logger.info("User created", user_id=user.id, email=user.email)

# 不推荐：字符串拼接
logger.info(f"User {user.id} created with email {user.email}")
```

### 2.9 代码审查

#### 2.9.1 审查清单
#### 代码质量
- [ ] 代码遵循 PEP 8 规范
- [ ] 变量和函数命名清晰有意义
- [ ] 无重复代码
- [ ] 函数职责单一
- [ ] 代码结构清晰

#### 性能
- [ ] 数据库查询优化
- [ ] 避免 N+1 查询问题
- [ ] 合理使用缓存
- [ ] 异步操作使用得当

#### 安全性
- [ ] 输入验证完整
- [ ] 无 SQL 注入风险
- [ ] 敏感信息不暴露
- [ ] 错误信息不泄露内部信息

#### 测试
- [ ] 单元测试覆盖率充分
- [ ] 集成测试覆盖主要流程
- [ ] 边界条件测试
- [ ] 异常情况测试

#### 文档
- [ ] 函数和类有清晰的文档字符串
- [ ] 复杂逻辑有必要的注释
- [ ] API 文档完整
- [ ] 变更日志更新


#### 2.9.2 自动化审查

通过工具自动化大部分审查工作：
```bash
# 运行完整的代码质量检查
make quality-check

# 或者分别运行
ruff check .
black --check .
mypy .
pytest --cov=app
```
