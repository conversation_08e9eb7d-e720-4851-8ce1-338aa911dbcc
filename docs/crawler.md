# 医学信息爬虫系统

一个工程化的医学信息爬虫系统，专门用于爬取MSD医学手册的专业版内容。

## 功能特点

✅ **三层爬虫架构**: 分类 → 详情 → 页面内容
✅ **健壮的错误处理**: 自动重试、错误分类、状态保存
✅ **实时进度追踪**: 进度条、统计信息、ETA预估
✅ **状态管理**: 支持暂停和恢复功能
✅ **详细报告**: HTML、JSON、XML多格式输出
✅ **站点地图生成**: 完整的内容索引
✅ **并发控制**: 可配置的并发数和请求频率

## 快速开始

### 1. 命令行启动

```bash
# 基本启动
python run_crawler.py start

# 自定义配置启动
python run_crawler.py start --output-dir ./my_data --concurrent 3 --delay 2

# 从中断处恢复
python run_crawler.py start --resume

# 查看状态
python run_crawler.py status

# 清除状态
python run_crawler.py clear
```

## 输出结构

爬虫运行后会生成以下目录结构：

```
medical_data/
├── 20241204_143022/              # 会话目录（时间戳）
│   ├── categories.json           # 医学主题分类数据
│   ├── details.json              # 详情分组数据
│   ├── pages_info.json           # 页面信息汇总
│   ├── pages/                    # 页面内容目录
│   │   ├── 营养不良_概述.md        # Markdown格式的页面内容
│   │   ├── 传染病_细菌感染.md      # ...
│   │   └── ...
│   ├── reports/                  # 报告目录
│   │   ├── crawl_report.md       # 详细爬取报告
│   │   ├── statistics.json       # 统计数据
│   │   ├── sitemap.html          # HTML站点地图
│   │   ├── sitemap.xml           # XML站点地图
│   │   ├── sitemap.json          # JSON站点地图
│   │   └── failed_urls.txt       # 失败URL列表
│   └── SESSION_SUMMARY.md        # 会话总结
└── crawler_state.json            # 爬虫状态文件
```

## 配置选项

### CrawlerConfig 参数

- `output_dir`: 输出目录（默认: `crawled_data`）
- `max_concurrent`: 最大并发数（默认: 5）
- `retry_times`: 重试次数（默认: 3）
- `delay_between_requests`: 请求间隔秒数（默认: 1.0）
- `enable_progress_bar`: 是否启用进度条（默认: True）
- `save_intermediate_results`: 是否保存中间结果（默认: True）

### 命令行参数

```bash
python run_crawler.py start --help
```

## 核心模块

### 1. 爬虫管理器 (`crawler_manager.py`)
- 统一的爬虫流程控制
- 协调三个爬虫模块的执行
- 管理整体状态和错误处理

### 2. 状态管理器 (`state_manager.py`)
- 持久化爬虫运行状态
- 支持暂停和恢复功能
- 错误记录和统计

### 3. 进度追踪器 (`progress_tracker.py`)
- 实时进度显示
- ETA预估和速率统计
- 多阶段进度管理

### 4. 报告生成器 (`report_generator.py`)
- 生成详细的爬取报告
- 多格式站点地图
- 统计分析和可视化

### 5. 爬虫模块 (`crawlers/`)
- `category_crawler.py`: 医学主题分类爬虫
- `detail_crawler.py`: 分类详情爬虫
- `page_crawler.py`: 页面内容爬虫

## 使用场景

### 场景1: 完整爬取
```bash
python simple_run.py
```
适合首次使用，会爬取所有医学内容。

### 场景2: 测试运行
```bash
python run_crawler.py start --concurrent 1 --delay 3
```
使用保守的配置进行测试。

### 场景3: 断点续传
```bash
python run_crawler.py start --resume
```
从上次中断的地方继续爬取。

### 场景4: 查看进度
```bash
python run_crawler.py status
```
查看当前爬取状态和统计信息。

## 注意事项

1. **网络友好**: 默认配置已考虑服务器负载，请勿随意增加并发数
2. **存储空间**: 完整爬取可能产生大量文件，确保有足够存储空间
3. **时间成本**: 完整爬取可能需要较长时间，建议在稳定网络环境下运行
4. **合法使用**: 请遵守网站的robots.txt和使用条款


## 技术架构

```
┌─────────────────┐
│  run_crawler.py │  命令行接口
└─────────────────┘
         │
┌─────────────────┐
│ crawler_manager │  爬虫管理器
└─────────────────┘
    │    │    │
┌───▼─┐ ┌▼───┐ ┌▼────┐
│cat  │ │det │ │page │  爬虫模块
│crawl│ │crawl│ │crawl│
└─────┘ └────┘ └─────┘
```
