# ============================================================
# AI Assistant Redis 配置
# ============================================================

# 网络配置
bind 0.0.0.0
port 6379
tcp-backlog 511
timeout 0
tcp-keepalive 300

# 通用配置
daemonize no
pidfile /var/run/redis_6379.pid
loglevel notice
logfile ""
databases 16

# 内存配置
maxmemory 256mb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# 持久化配置
save 900 1
save 300 10
save 60 10000

stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# AOF 配置
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
aof-use-rdb-preamble yes

# 慢日志配置
slowlog-log-slower-than 10000
slowlog-max-len 128

# 客户端配置
timeout 0
tcp-keepalive 300
