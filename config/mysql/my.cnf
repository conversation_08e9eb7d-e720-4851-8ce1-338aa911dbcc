# ============================================================
# AI Assistant MySQL 配置
# ============================================================

[mysqld]
# 基础配置
default-authentication-plugin=mysql_native_password
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci
init-connect='SET NAMES utf8mb4'

# 网络配置
bind-address=0.0.0.0
port=3306
max_connections=200
max_connect_errors=10

# 内存配置
innodb_buffer_pool_size=512M
innodb_log_file_size=128M
innodb_log_buffer_size=16M
key_buffer_size=128M
sort_buffer_size=4M
read_buffer_size=2M
read_rnd_buffer_size=8M
myisam_sort_buffer_size=64M
thread_cache_size=8
query_cache_size=32M
query_cache_limit=2M

# 日志配置
log-error=/var/log/mysql/error.log
slow-query-log=1
slow-query-log-file=/var/log/mysql/slow.log
long_query_time=2

# InnoDB 配置
innodb_file_per_table=1
innodb_flush_log_at_trx_commit=1
innodb_lock_wait_timeout=120

[mysql]
default-character-set=utf8mb4

[client]
default-character-set=utf8mb4
