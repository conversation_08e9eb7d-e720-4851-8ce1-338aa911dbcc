#!/bin/bash
# ============================================================
# AI Assistant 部署脚本
# ============================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${CYAN}[SUCCESS]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
AI Assistant 部署脚本

用法: $0 [选项] [命令]

命令:
    build       构建 Docker 镜像
    up          启动服务（包含构建）
    down        停止服务
    restart     重启服务
    logs        查看日志
    status      查看服务状态
    clean       清理资源
    backup      备份数据
    restore     恢复数据
    help        显示此帮助信息

选项:
    -d, --detach        后台运行
    -f, --force         强制执行
    --no-cache          构建时不使用缓存
    --with-nginx        启动 Nginx 反向代理
    --prod              生产模式部署
    --dev               开发模式部署

示例:
    $0 build --no-cache     # 重新构建镜像
    $0 up -d --with-nginx   # 后台启动包含 Nginx 的完整服务
    $0 logs fastapi         # 查看 FastAPI 服务日志
    $0 down --force         # 强制停止所有服务

EOF
}

# 检查依赖
check_dependencies() {
    log_step "检查系统依赖..."

    local deps=("docker" "docker-compose")
    local missing_deps=()

    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            missing_deps+=("$dep")
        fi
    done

    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        log_error "缺少必需的依赖："
        for dep in "${missing_deps[@]}"; do
            log_error "  - $dep"
        done
        log_error "请安装所有依赖后重新运行"
        exit 1
    fi

    log_success "系统依赖检查通过"
}

# 检查环境配置
check_environment() {
    log_step "检查环境配置..."

    if [[ ! -f ".env.docker" ]]; then
        log_warn ".env.docker 文件不存在，正在创建模板..."
        cp .env.docker.example .env.docker 2>/dev/null || {
            log_error "无法找到 .env.docker.example 模板文件"
            log_error "请手动创建 .env.docker 文件"
            exit 1
        }
        log_warn "请编辑 .env.docker 文件并填写正确的配置"
    fi

    # 检查必需的环境变量
    source .env.docker

    local required_vars=(
        "ARK_API_KEY"
        "VOLCENGINE_ACCESS_KEY"
        "VOLCENGINE_SECRET_KEY"
        "TOS_BUCKET_NAME"
    )

    local missing_vars=()

    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" || "${!var}" == "your_"* ]]; then
            missing_vars+=("$var")
        fi
    done

    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        log_error "以下环境变量需要配置："
        for var in "${missing_vars[@]}"; do
            log_error "  - $var"
        done
        log_error "请编辑 .env.docker 文件并填写正确的值"
        exit 1
    fi

    log_success "环境配置检查通过"
}

# 构建 Docker 镜像
build_image() {
    log_step "构建 Docker 镜像..."

    local build_args=""

    if [[ "$NO_CACHE" == "true" ]]; then
        build_args="--no-cache"
    fi

    docker build $build_args -t ai-assistant:latest .

    log_success "Docker 镜像构建完成"
}

# 启动服务
start_services() {
    log_step "启动服务..."

    local compose_args=""
    local compose_profiles=""

    if [[ "$DETACH" == "true" ]]; then
        compose_args="-d"
    fi

    if [[ "$WITH_NGINX" == "true" ]]; then
        compose_profiles="--profile nginx"
    fi

    docker-compose $compose_profiles up $compose_args

    if [[ "$DETACH" == "true" ]]; then
        log_success "服务已在后台启动"
        show_status
    else
        log_success "服务启动完成"
    fi
}

# 停止服务
stop_services() {
    log_step "停止服务..."

    local compose_args=""

    if [[ "$FORCE" == "true" ]]; then
        compose_args="--remove-orphans -v"
    fi

    docker-compose down $compose_args

    log_success "服务已停止"
}

# 重启服务
restart_services() {
    log_step "重启服务..."

    stop_services
    start_services

    log_success "服务已重启"
}

# 查看日志
show_logs() {
    local service=$1

    if [[ -n "$service" ]]; then
        log_step "查看 $service 服务日志..."
        docker-compose logs -f "$service"
    else
        log_step "查看所有服务日志..."
        docker-compose logs -f
    fi
}

# 查看服务状态
show_status() {
    log_step "检查服务状态..."

    echo -e "${BLUE}Docker 容器状态:${NC}"
    docker-compose ps

    echo -e "\n${BLUE}网络连接状态:${NC}"
    docker network ls | grep ai-assistant || log_warn "未找到 ai-assistant 网络"

    echo -e "\n${BLUE}数据卷状态:${NC}"
    docker volume ls | grep ai-assistant || log_warn "未找到 ai-assistant 数据卷"

    echo -e "\n${BLUE}服务健康检查:${NC}"

    # 检查服务可用性
    local services=("FastAPI:8000/health" "Streamlit:8501" "Redis:6379" "MySQL:3306")

    for service_info in "${services[@]}"; do
        IFS=':' read -r name endpoint <<< "$service_info"

        if [[ "$name" == "FastAPI" ]] || [[ "$name" == "Streamlit" ]]; then
            if curl -s -f "http://localhost:$endpoint" > /dev/null; then
                log_success "$name 服务运行正常 (http://localhost:${endpoint%%/*})"
            else
                log_error "$name 服务不可用"
            fi
        elif [[ "$name" == "Redis" ]]; then
            if docker exec ai-assistant-redis redis-cli ping > /dev/null 2>&1; then
                log_success "$name 服务运行正常"
            else
                log_error "$name 服务不可用"
            fi
        elif [[ "$name" == "MySQL" ]]; then
            if docker exec ai-assistant-mysql mysqladmin ping -h localhost -u root -p${MYSQL_PASSWORD:-root123} > /dev/null 2>&1; then
                log_success "$name 服务运行正常"
            else
                log_error "$name 服务不可用"
            fi
        fi
    done
}

# 清理资源
clean_resources() {
    log_step "清理 Docker 资源..."

    if [[ "$FORCE" == "true" ]]; then
        log_warn "强制清理模式：将删除所有相关资源"
        docker-compose down --remove-orphans -v
        docker system prune -f
        docker volume prune -f
    else
        docker-compose down --remove-orphans
        docker image prune -f
    fi

    log_success "资源清理完成"
}

# 备份数据
backup_data() {
    log_step "备份数据..."

    local backup_dir="backup/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"

    # 备份 MySQL 数据
    log_info "备份 MySQL 数据..."
    docker exec ai-assistant-mysql mysqldump -u root -p${MYSQL_PASSWORD:-root123} ai_assistant > "$backup_dir/mysql_backup.sql"

    # 备份 Redis 数据
    log_info "备份 Redis 数据..."
    docker exec ai-assistant-redis redis-cli BGSAVE
    docker cp ai-assistant-redis:/data/dump.rdb "$backup_dir/"

    # 备份应用数据
    log_info "备份应用数据..."
    cp -r data "$backup_dir/" 2>/dev/null || log_warn "应用数据目录不存在"

    log_success "数据备份完成: $backup_dir"
}

# 恢复数据
restore_data() {
    local backup_dir=$1

    if [[ -z "$backup_dir" ]]; then
        log_error "请指定备份目录"
        exit 1
    fi

    if [[ ! -d "$backup_dir" ]]; then
        log_error "备份目录不存在: $backup_dir"
        exit 1
    fi

    log_step "恢复数据从: $backup_dir"

    # 恢复 MySQL 数据
    if [[ -f "$backup_dir/mysql_backup.sql" ]]; then
        log_info "恢复 MySQL 数据..."
        docker exec -i ai-assistant-mysql mysql -u root -p${MYSQL_PASSWORD:-root123} ai_assistant < "$backup_dir/mysql_backup.sql"
    fi

    # 恢复 Redis 数据
    if [[ -f "$backup_dir/dump.rdb" ]]; then
        log_info "恢复 Redis 数据..."
        docker cp "$backup_dir/dump.rdb" ai-assistant-redis:/data/
        docker restart ai-assistant-redis
    fi

    # 恢复应用数据
    if [[ -d "$backup_dir/data" ]]; then
        log_info "恢复应用数据..."
        cp -r "$backup_dir/data" .
    fi

    log_success "数据恢复完成"
}

# 主函数
main() {
    log_info "AI Assistant 部署脚本 v1.0.0"

    # 解析参数
    DETACH=false
    FORCE=false
    NO_CACHE=false
    WITH_NGINX=false
    PROD_MODE=false
    DEV_MODE=false

    while [[ $# -gt 0 ]]; do
        case $1 in
            -d|--detach)
                DETACH=true
                shift
                ;;
            -f|--force)
                FORCE=true
                shift
                ;;
            --no-cache)
                NO_CACHE=true
                shift
                ;;
            --with-nginx)
                WITH_NGINX=true
                shift
                ;;
            --prod)
                PROD_MODE=true
                shift
                ;;
            --dev)
                DEV_MODE=true
                shift
                ;;
            build)
                COMMAND="build"
                shift
                ;;
            up)
                COMMAND="up"
                shift
                ;;
            down)
                COMMAND="down"
                shift
                ;;
            restart)
                COMMAND="restart"
                shift
                ;;
            logs)
                COMMAND="logs"
                SERVICE="$2"
                shift 2 2>/dev/null || shift
                ;;
            status)
                COMMAND="status"
                shift
                ;;
            clean)
                COMMAND="clean"
                shift
                ;;
            backup)
                COMMAND="backup"
                shift
                ;;
            restore)
                COMMAND="restore"
                BACKUP_DIR="$2"
                shift 2 2>/dev/null || shift
                ;;
            help|--help|-h)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # 默认命令
    if [[ -z "$COMMAND" ]]; then
        COMMAND="help"
    fi

    # 检查依赖（除了 help 命令）
    if [[ "$COMMAND" != "help" ]]; then
        check_dependencies
        check_environment
    fi

    # 执行命令
    case "$COMMAND" in
        build)
            build_image
            ;;
        up)
            build_image
            start_services
            ;;
        down)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        logs)
            show_logs "$SERVICE"
            ;;
        status)
            show_status
            ;;
        clean)
            clean_resources
            ;;
        backup)
            backup_data
            ;;
        restore)
            restore_data "$BACKUP_DIR"
            ;;
        help)
            show_help
            ;;
        *)
            log_error "未知命令: $COMMAND"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
