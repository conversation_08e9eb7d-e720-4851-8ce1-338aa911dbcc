# 健康体检报告解析系统使用指南

## 系统概述

新实现的健康体检报告解析系统支持异步任务处理，可以处理大量报告解析请求而不会阻塞API响应。

## 核心功能

### 1. 异步报告解析
- 支持通过 `reportId` 和 `reportKey` 提交解析任务
- 异步处理，不阻塞API响应
- 支持任务状态查询和进度监控

### 2. 多步骤处理流程
1. **OCR解析**: 从TOS获取文件并进行OCR处理
2. **个人信息提取**: 提取姓名、年龄、性别等基础信息
3. **总体结果分析**: 生成健康摘要和建议
4. **检查项指标分析**: 解析各项检查指标
5. **影像数据分析**: 提取影像检查描述
6. **JSON文件输出**: 生成最终结构化结果

### 3. 任务状态管理
- 支持从任意步骤重新执行
- 失败任务自动重试机制
- 详细的执行日志记录

## API接口

### 提交解析任务
```http
POST /api/v1/report/parse
Content-Type: application/json

{
    "report_id": "报告唯一标识",
    "report_key": "TOS文件存储Key"
}
```

### 查询任务状态
```http
GET /api/v1/report/task/{report_id}/status
```

### 重新执行任务
```http
POST /api/v1/report/task/{report_id}/restart
Content-Type: application/json

{
    "from_step": 2  // 可选，从指定步骤开始，不填则从头开始
}
```

### 监控统计
```http
GET /api/v1/report/monitor/stats
```

### 任务列表
```http
GET /api/v1/report/tasks?status=processing&page=1&size=20
```

## 数据库表结构

### 报告任务表 (report_tasks)
存储任务基本信息、状态、结果等

### 任务日志表 (report_task_logs)  
记录每个步骤的执行日志

## 文件输出

系统会在 `/data/reports/{date}/{report_id}/` 目录下生成：
- `final_result.json`: 最终合并结果
- `ocr_result.json`: OCR原始结果
- `personal_info.json`: 个人信息
- `overall_result.json`: 总体结果
- `tabular_data.json`: 检查项数据
- `imaging_data.json`: 影像数据

## 部署和启动

### 1. 数据库准备
确保MySQL数据库已配置，应用启动时会自动创建表。

### 2. 环境变量
需要配置以下环境变量：
- `MYSQL_HOST`, `MYSQL_USER`, `MYSQL_PASSWORD`, `MYSQL_DATABASE`
- `VOLCENGINE_ACCESS_KEY`, `VOLCENGINE_SECRET_KEY`
- `TOS_BUCKET_NAME`

### 3. 启动应用
```bash
fastapi dev app/main.py
# 或
uvicorn app.main:app --reload
```

应用启动时会：
- 自动创建数据库表
- 启动3个异步工作线程处理任务

## 监控和运维

### 健康检查
```http
GET /health
```

### 监控指标
- 任务总数、待处理数、处理中数、完成数、失败数
- 成功率、平均处理时间
- 详细的步骤执行日志

### 错误处理
- 自动重试机制（最多3次）
- 详细的错误日志记录
- 支持手动重新执行失败任务

## 扩展性

### 横向扩展
- 支持多实例部署
- 基于数据库的任务队列，天然支持分布式

### 性能优化
- 工作线程数量可配置（默认3个）
- 支持批量处理
- 异步非阻塞设计

### 监控告警
- 可集成外部监控系统
- 支持自定义告警阈值
- 详细的性能指标

## 注意事项

1. **数据安全**: 确保TOS访问权限正确配置
2. **存储空间**: 定期清理过期的JSON文件
3. **并发控制**: 合理设置工作线程数量
4. **错误恢复**: 定期检查失败任务并手动重试
5. **性能监控**: 关注任务处理时间和队列积压情况