# ============================================================
# 多阶段构建 Dockerfile for AI Assistant
# ============================================================
# 第一阶段：构建基础镜像
FROM python:3.12.4-slim as base

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# 替换为国内镜像源并安装系统依赖
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources && \
    sed -i 's/security.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources && \
    apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# 创建应用目录和用户
RUN useradd --create-home --shell /bin/bash app
WORKDIR /app
RUN chown app:app /app

# ============================================================
# 第二阶段：安装 Python 依赖
FROM base as deps

# 复制依赖文件
COPY requirements.txt /app/
COPY requirements-dev.txt /app/

# 配置 pip 使用国内镜像源并安装依赖
RUN pip install --no-cache-dir -i https://mirrors.aliyun.com/pypi/simple/ -r requirements.txt

# ============================================================
# 第三阶段：应用构建
FROM deps as builder

# 复制应用代码
COPY --chown=app:app . /app/

# 切换到应用用户
USER app

# ============================================================
# 第四阶段：生产镜像
FROM python:3.12.4-slim as production

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PATH="/home/<USER>/.local/bin:$PATH"

# 替换为国内镜像源并安装运行时依赖
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources && \
    sed -i 's/security.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources && \
    apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 创建应用用户
RUN useradd --create-home --shell /bin/bash app

# 从依赖阶段复制 Python 包
COPY --from=deps /usr/local/lib/python3.12/site-packages /usr/local/lib/python3.12/site-packages
COPY --from=deps /usr/local/bin /usr/local/bin

# 设置工作目录
WORKDIR /app
RUN chown app:app /app

# 复制应用代码
COPY --from=builder --chown=app:app /app /app

# 复制启动脚本
COPY --chown=app:app docker-entrypoint.sh /app/
RUN chmod +x /app/docker-entrypoint.sh

# 切换到应用用户
USER app

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 暴露端口
EXPOSE 8000 8501

# 设置启动命令
ENTRYPOINT ["/app/docker-entrypoint.sh"]
CMD ["app"]
