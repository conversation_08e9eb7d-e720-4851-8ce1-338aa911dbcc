from collections.abc import Callable
import time

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from app.middleware.traceid import get_trace_id
from app.utils.logger import get_logger

logger = get_logger(__name__)


class LoggingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()

        # 只对 /api/v1/ 开头的请求记录日志
        should_log = request.url.path.startswith("/api/v1/")

        if should_log:
            # 记录请求开始
            logger.info(
                "请求开始 --->",
                extra={
                    "trace_id": get_trace_id(),
                    "method": request.method,
                    "url": str(request.url),
                    "client_ip": request.client.host if request.client else None,
                    "user_agent": request.headers.get("user-agent"),
                },
            )

        # 处理请求
        response = await call_next(request)

        # 计算处理时间
        process_time = time.time() - start_time

        if should_log:
            # 记录请求完成
            logger.info(
                "请求结束 <---",
                extra={
                    "trace_id": get_trace_id(),
                    "method": request.method,
                    "url": str(request.url),
                    "status_code": response.status_code,
                    "process_time": round(process_time, 4),
                },
            )

        # 添加处理时间到响应头
        response.headers["X-Process-Time"] = str(process_time)

        return response
