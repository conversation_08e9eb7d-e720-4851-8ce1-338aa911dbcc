from collections.abc import Callable
from contextvars import ContextVar
import uuid

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

# 创建 TraceID 上下文变量
trace_id_var: ContextVar[str] = ContextVar("trace_id", default=None)


class TraceIDMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 从请求头获取 trace_id，如果没有则生成新的
        trace_id = request.headers.get("X-Trace-ID") or str(uuid.uuid4())

        # 设置到上下文变量中
        trace_id_var.set(trace_id)

        # 处理请求
        response = await call_next(request)

        # 在响应头中添加 trace_id
        response.headers["X-Trace-ID"] = trace_id

        return response


def get_trace_id() -> str:
    """获取当前请求的 TraceID"""
    return trace_id_var.get() or "unknown"
