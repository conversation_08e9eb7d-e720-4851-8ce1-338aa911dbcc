from agno.playground import Playground

# from app.agents.report_agent import get_report_agent
from app.agents.report_agent import ReportAnalysisWorkflow
from app.agents.web_agent import get_medical_agent

######################################################
## Routes for the Playground Interface
######################################################

# Get Agents to serve in the playground
medical_agent = get_medical_agent(debug_mode=True)
report_agent = ReportAnalysisWorkflow(debug_mode=True)

# Create a playground instance
playground = Playground(agents=[medical_agent], workflows=[report_agent])

# Get the router for the playground
playground_router = playground.get_async_router()
