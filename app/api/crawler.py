"""爬虫控制器"""

from fastapi import APIRouter, BackgroundTasks, HTTPException
from structlog import get_logger

from app.crawler.crawler_manager import CrawlerConfig, MedicalCrawlerManager
from app.schemas.request import CrawlerConfigRequest
from app.schemas.response import ApiResponse

logger = get_logger(__name__)
router = APIRouter()

# 全局爬虫管理器实例
crawler_manager: MedicalCrawlerManager | None = None


def get_crawler_manager() -> MedicalCrawlerManager:
    """获取爬虫管理器实例"""
    global crawler_manager
    if crawler_manager is None:
        # 使用默认配置创建
        config = CrawlerConfig()
        crawler_manager = MedicalCrawlerManager(config)
    return crawler_manager


async def run_crawler_background(resume: bool = False):
    """后台运行爬虫任务"""
    try:
        manager = get_crawler_manager()
        logger.info("开始后台执行爬虫任务", resume=resume)
        result = await manager.run_full_crawl(resume=resume)
        logger.info("爬虫任务完成", result=result)
    except Exception as e:
        logger.error("后台爬虫任务失败", error=str(e))


@router.post("/start")
async def start_crawler(
    background_tasks: BackgroundTasks, resume: bool = False, config: CrawlerConfigRequest | None = None
) -> ApiResponse:
    """启动爬虫任务

    Args:
        background_tasks: FastAPI 后台任务
        resume: 是否从上次中断处继续
        config: 爬虫配置（可选）

    Returns:
        ApiResponse: 包含启动结果的响应
    """
    try:
        global crawler_manager

        # 如果提供了配置，创建新的爬虫管理器
        if config:
            crawler_config = CrawlerConfig(
                output_dir=config.output_dir,
                max_concurrent=config.max_concurrent,
                retry_times=config.retry_times,
                delay_between_requests=config.delay_between_requests,
                enable_progress_bar=config.enable_progress_bar,
                save_intermediate_results=config.save_intermediate_results,
            )
            crawler_manager = MedicalCrawlerManager(crawler_config)

        manager = get_crawler_manager()

        # 检查是否已经在运行中
        if manager.is_running:
            raise HTTPException(status_code=400, detail="爬虫任务已在运行中")

        # 添加后台任务
        background_tasks.add_task(run_crawler_background, resume)

        response_data = {
            "message": "爬虫任务已启动",
            "session_id": manager.session_id,
            "resume": resume,
            "config": {
                "output_dir": manager.config.output_dir,
                "max_concurrent": manager.config.max_concurrent,
                "retry_times": manager.config.retry_times,
                "delay_between_requests": manager.config.delay_between_requests,
            },
        }

        logger.info("爬虫任务启动成功", resume=resume, session_id=manager.session_id)

        return ApiResponse.success(data=response_data)

    except HTTPException:
        raise
    except Exception as e:
        logger.error("启动爬虫任务失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"启动爬虫任务失败: {e!s}")


@router.get("/status")
async def get_crawler_status() -> ApiResponse:
    """获取爬虫状态

    Returns:
        ApiResponse: 包含爬虫状态信息的响应
    """
    try:
        manager = get_crawler_manager()

        progress_info = manager.get_progress()

        # 构建状态响应
        status_data = {
            "is_running": progress_info["is_running"],
            "session_id": progress_info["session_id"],
            "start_time": manager.start_time,
            "current_stage": progress_info["state"].get("current_stage", "init"),
            "status": progress_info["state"].get("status", "idle"),
            "progress": progress_info["progress"],
            "statistics": {
                "total_categories": progress_info["state"].get("total_categories", 0),
                "total_details": progress_info["state"].get("total_details", 0),
                "total_pages": progress_info["state"].get("total_pages", 0),
                "failed_urls": len(progress_info["state"].get("failed_urls", [])),
            },
            "stages_completed": {
                "categories": progress_info["state"].get("categories_completed", False),
                "details": progress_info["state"].get("details_completed", False),
                "pages": progress_info["state"].get("pages_completed", False),
            },
        }

        logger.info("获取爬虫状态成功", is_running=progress_info["is_running"])

        return ApiResponse.success(data=status_data)

    except Exception as e:
        logger.error("获取爬虫状态失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"获取爬虫状态失败: {e!s}")


@router.post("/stop")
async def stop_crawler() -> ApiResponse:
    """停止爬虫任务

    Returns:
        ApiResponse: 包含停止结果的响应
    """
    try:
        manager = get_crawler_manager()

        if not manager.is_running:
            return ApiResponse.success(data={"message": "爬虫任务未在运行中"}, message="爬虫任务未在运行中")

        # 停止爬虫
        manager.stop_crawl()

        logger.info("爬虫任务停止成功")

        return ApiResponse.success(data={"message": "爬虫任务已停止"}, message="爬虫任务已停止")

    except Exception as e:
        logger.error("停止爬虫任务失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"停止爬虫任务失败: {e!s}")


@router.get("/history")
async def get_crawler_history() -> ApiResponse:
    """获取爬虫历史记录

    Returns:
        ApiResponse: 包含历史记录的响应
    """
    try:
        manager = get_crawler_manager()

        # 获取状态管理器的历史信息
        resume_info = manager.state_manager.get_resume_info()

        if not resume_info["can_resume"]:
            return ApiResponse.success(data={"message": "暂无历史记录"}, message="暂无历史记录")

        history_data = {
            "session_id": resume_info["session_id"],
            "start_time": resume_info["start_time"],
            "current_stage": resume_info["current_stage"],
            "completed_stages": resume_info["completed_stages"],
            "statistics": resume_info["statistics"],
        }

        logger.info("获取爬虫历史记录成功")

        return ApiResponse.success(data=history_data)

    except Exception as e:
        logger.error("获取爬虫历史记录失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"获取爬虫历史记录失败: {e!s}")


@router.delete("/clear")
async def clear_crawler_state() -> ApiResponse:
    """清除爬虫状态

    Returns:
        ApiResponse: 包含清除结果的响应
    """
    try:
        manager = get_crawler_manager()

        # 如果正在运行，不允许清除
        if manager.is_running:
            raise HTTPException(status_code=400, detail="爬虫正在运行中，无法清除状态")

        # 清除状态
        manager.state_manager.clear_state()

        logger.info("爬虫状态清除成功")

        return ApiResponse.success(data={"message": "爬虫状态已清除"}, message="爬虫状态已清除")

    except HTTPException:
        raise
    except Exception as e:
        logger.error("清除爬虫状态失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"清除爬虫状态失败: {e!s}")
