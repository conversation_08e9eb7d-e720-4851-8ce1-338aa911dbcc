from collections.abc import AsyncGenerator
from logging import getLogger

from agno.agent import Agent
from fastapi import APIRouter, HTTPException, status
from fastapi.responses import StreamingResponse
from pydantic import BaseModel

from app.agents.web_agent import get_medical_agent

logger = getLogger(__name__)

######################################################
## Agent接口路由
######################################################

router = APIRouter(tags=["Agents"])


async def chat_response_streamer(agent: Agent, message: str) -> AsyncGenerator:
    """以流式方式逐块返回agent响应。

    参数:
        agent: 要交互的agent实例
        message: 要处理的用户消息

    生成:
        来自agent响应的文本块
    """
    try:
        run_response = await agent.arun(message, stream=True)
        async for chunk in run_response:
            logger.info(f"---> Chunk: {chunk}")
            # chunk.content只包含来自Agent的文本响应。
            # 对于高级用例，我们应该生成包含工具调用和中间步骤的完整chunk。
            if chunk.content is not None and chunk.content != "":
                yield chunk.content
    except Exception as e:
        logger.error(f"Error in chat_response_streamer: {e}")
        yield f"data: Error: {e!s}\n\n"


class RunRequest(BaseModel):
    """运行agent的请求模型"""

    message: str
    user_id: str
    stream: bool = True
    session_id: str | None = None


@router.post("/agent/runs", status_code=status.HTTP_200_OK)
async def create_agent_run(body: RunRequest) -> StreamingResponse:
    """向特定agent发送消息并返回响应。

    参数:
        agent_id: 要交互的agent的ID
        body: 包含消息的请求参数

    返回值:
        流式响应或完整的agent响应
    """
    logger.debug(f"RunRequest: {body}")

    try:
        agent: Agent = get_medical_agent(
            user_id=body.user_id,
            session_id=body.session_id,
        )
        logger.info(f"Created agent: {body.user_id}, session_id: {body.session_id}")
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))

    if body.stream:
        return StreamingResponse(
            chat_response_streamer(agent, body.message),
            media_type="text/event-stream",
        )
    response = await agent.arun(body.message, stream=False)
    # 在这种情况下，response.content只包含来自Agent的文本响应。
    # 对于高级用例，我们应该返回包含工具调用和中间步骤的完整响应。
    return response
