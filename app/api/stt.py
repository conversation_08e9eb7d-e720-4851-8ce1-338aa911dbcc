from fastapi import APIRouter, File, HTTPException, UploadFile

from app.schemas.response import ApiResponse
from app.schemas.stt import STTResponse
from app.services.stt import stt_service
from app.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()

# 支持的音频格式
SUPPORTED_FORMATS = {
    "audio/wav",
    "audio/wave",
    "audio/x-wav",
    "audio/mpeg",
    "audio/mp3",
    "audio/ogg",
    "audio/opus",
    "audio/pcm",
    "audio/L16",
}

# 支持的文件扩展名
SUPPORTED_EXTENSIONS = {".wav", ".mp3", ".ogg", ".opus", ".pcm"}


def validate_audio_file(file: UploadFile) -> None:
    """验证音频文件格式"""
    # 检查MIME类型
    if file.content_type and file.content_type not in SUPPORTED_FORMATS:
        raise HTTPException(
            status_code=400, detail=f"不支持的音频格式: {file.content_type}。支持的格式: PCM, WAV, MP3, OGG OPUS"
        )

    # 检查文件扩展名
    if file.filename:
        filename_lower = file.filename.lower()
        if not any(filename_lower.endswith(ext) for ext in SUPPORTED_EXTENSIONS):
            raise HTTPException(
                status_code=400, detail=f"不支持的文件扩展名。支持的扩展名: {', '.join(SUPPORTED_EXTENSIONS)}"
            )


@router.post("/recognize", response_model=ApiResponse[STTResponse])
async def recognize_audio(
    audio_file: UploadFile = File(..., description="音频文件（支持 PCM/WAV/MP3/OGG OPUS 格式，最大30MB）"),
) -> ApiResponse[STTResponse]:
    """
    语音转文本识别

    上传音频文件进行语音识别，返回识别出的文本内容。

    - **audio_file**: 音频文件，支持格式：PCM, WAV, MP3, OGG OPUS
    - **文件大小限制**: 最大30MB
    - **音频时长限制**: 最大2分钟

    返回识别出的文本内容及相关信息。
    """
    if not audio_file.filename:
        raise HTTPException(status_code=400, detail="请上传音频文件")

    logger.info("开始处理音频识别请求", filename=audio_file.filename, content_type=audio_file.content_type)

    try:
        # 验证文件格式
        validate_audio_file(audio_file)

        # 调用STT服务进行识别
        result = await stt_service.recognize_audio(audio_file.file, audio_file.filename)

        logger.info(
            "音频识别完成", filename=audio_file.filename, text_length=len(result.text), duration=result.duration
        )

        return ApiResponse.success(data=result.model_dump())

    except HTTPException:
        raise
    except Exception as e:
        logger.error("音频识别处理失败", error=str(e), filename=audio_file.filename)
        raise HTTPException(status_code=500, detail="音频识别处理失败")
