from fastapi import APIRouter

from app.api import agents, check, crawler, report, stt

# from app.api import chat

api_router = APIRouter()

# 注册状态检查相关路由
api_router.include_router(check.router, prefix="/status", tags=["status"])

# 注册爬虫相关路由
api_router.include_router(crawler.router, prefix="/crawler", tags=["crawler"])

# api_router.include_router(chat.router, prefix="/chat", tags=["chat"])

# 注册agent相关路由
api_router.include_router(agents.router, prefix="/agents", tags=["agents"])

# 注册STT相关路由
api_router.include_router(stt.router, prefix="/stt", tags=["stt"])

# 注册PDF相关路由
api_router.include_router(report.router, prefix="/report", tags=["report"])
