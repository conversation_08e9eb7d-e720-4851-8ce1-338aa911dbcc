"""服务 Check"""

from fastapi import APIRouter
from structlog import get_logger

from app.schemas.response import ApiResponse

logger = get_logger(__name__)
router = APIRouter()


@router.get("/health")
async def get_volcengine_status() -> ApiResponse:
    """获取火山引擎客户端，redis 等服务状态

    Returns:
        APIResponse: 包含客户端状态信息的响应
    """
    status = {"api": "OK"}
    return ApiResponse.success(data=status)
