"""JSON处理工具函数"""

import json
import re
from typing import Any

from structlog import get_logger

logger = get_logger(__name__)


def safe_json_parse(text: str) -> dict[str, Any]:
    """
    安全的JSON解析，最大化兼容各种格式的 JSON

    处理的情况包括：
    - markdown 代码块格式
    - 不完整的 JSON（缺少闭合括号等）
    - 单引号 JSON
    - 尾随逗号
    - 带注释的 JSON
    - 多种编码问题

    Args:
        text: 需要解析的文本

    Returns:
        解析后的字典对象，解析失败时返回包含错误信息的字典
    """

    def extract_json_from_text(text: str) -> str:
        """从文本中提取 JSON 字符串"""
        # 处理 markdown 代码块
        patterns = [
            r"```json\s*(.*?)```",  # ```json ... ```
            r"```\s*(.*?)```",  # ``` ... ```
            r"`(.*?)`",  # `...`
        ]

        for pattern in patterns:
            match = re.search(pattern, text, re.DOTALL | re.IGNORECASE)
            if match:
                return match.group(1).strip()

        return text.strip()

    def clean_json_string(json_str: str) -> str:
        """清理和修复 JSON 字符串"""
        # 移除注释 (// 和 /* */)
        json_str = re.sub(r"//.*?$", "", json_str, flags=re.MULTILINE)
        json_str = re.sub(r"/\*.*?\*/", "", json_str, flags=re.DOTALL)

        # 替换单引号为双引号（但要注意避免字符串内的单引号）
        json_str = re.sub(r"'([^'\\]*(\\.[^'\\]*)*)'", r'"\1"', json_str)

        # 移除尾随逗号
        json_str = re.sub(r",(\s*[}\]])", r"\1", json_str)

        return json_str.strip()

    def try_fix_incomplete_json(json_str: str) -> str:
        """尝试修复不完整的 JSON"""
        # 统计括号平衡
        open_braces = json_str.count("{")
        close_braces = json_str.count("}")
        open_brackets = json_str.count("[")
        close_brackets = json_str.count("]")

        # 自动补全缺失的括号
        missing_braces = open_braces - close_braces
        missing_brackets = open_brackets - close_brackets

        if missing_braces > 0:
            json_str += "}" * missing_braces
        elif missing_braces < 0:
            json_str = "{" * abs(missing_braces) + json_str

        if missing_brackets > 0:
            json_str += "]" * missing_brackets
        elif missing_brackets < 0:
            json_str = "[" * abs(missing_brackets) + json_str

        # 如果以逗号结尾，尝试移除最后一个逗号
        json_str = re.sub(r",\s*$", "", json_str.strip())

        return json_str

    def try_extract_partial_json(text: str) -> dict[str, Any]:
        """尝试从文本中提取部分有效的 JSON 对象"""
        # 寻找 JSON 对象的开始和可能的结束
        brace_start = text.find("{")
        if brace_start == -1:
            return {}

        # 尝试找到匹配的右括号
        brace_count = 0
        for i, char in enumerate(text[brace_start:]):
            if char == "{":
                brace_count += 1
            elif char == "}":
                brace_count -= 1
                if brace_count == 0:
                    # 找到完整的 JSON 对象
                    try:
                        return json.loads(text[brace_start : brace_start + i + 1])
                    except json.JSONDecodeError:
                        continue

        # 如果没有找到完整对象，尝试修复
        partial_json = text[brace_start:]
        fixed_json = try_fix_incomplete_json(partial_json)
        try:
            return json.loads(fixed_json)
        except json.JSONDecodeError:
            return {}

    # 主解析逻辑
    original_text = text

    try:
        # 第一步：提取潜在的 JSON 字符串
        json_str = extract_json_from_text(text)

        # 第二步：直接尝试解析
        try:
            return json.loads(json_str)
        except json.JSONDecodeError:
            pass

        # 第三步：清理后尝试解析
        cleaned_json = clean_json_string(json_str)
        try:
            return json.loads(cleaned_json)
        except json.JSONDecodeError:
            pass

        # 第四步：修复不完整的 JSON 后解析
        fixed_json = try_fix_incomplete_json(cleaned_json)
        try:
            return json.loads(fixed_json)
        except json.JSONDecodeError:
            pass

        # 第五步：尝试提取部分有效的 JSON
        partial_result = try_extract_partial_json(original_text)
        if partial_result:
            logger.warning(f"使用部分 JSON 解析: {partial_result}")
            return partial_result

        # 最终失败，返回原文本包装
        logger.error(f"JSON解析完全失败，返回原文本: {original_text[:200]}...")
        return {"error": "JSON parsing failed", "original_text": original_text}

    except Exception as e:
        logger.error(f"JSON解析异常: {e}, 原始文本: {original_text[:200]}...")
        return {"error": f"JSON parsing exception: {e!s}", "original_text": original_text}


def pretty_json(data: dict | list, indent: int = 2, ensure_ascii: bool = False) -> str:
    """
    美化 JSON 输出

    Args:
        data: 需要格式化的数据
        indent: 缩进空格数
        ensure_ascii: 是否确保ASCII编码

    Returns:
        格式化后的 JSON 字符串
    """
    try:
        return json.dumps(data, indent=indent, ensure_ascii=ensure_ascii)
    except (TypeError, ValueError) as e:
        logger.error(f"JSON格式化失败: {e}")
        return str(data)


def validate_json_schema(data: dict, required_keys: list[str]) -> tuple[bool, list[str]]:
    """
    验证 JSON 数据是否包含必需的键

    Args:
        data: 需要验证的数据
        required_keys: 必需的键列表

    Returns:
        (是否有效, 缺失的键列表)
    """
    missing_keys = [key for key in required_keys if key not in data]
    return len(missing_keys) == 0, missing_keys


def merge_json_objects(*objects: dict) -> dict:
    """
    深度合并多个 JSON 对象

    Args:
        *objects: 需要合并的字典对象

    Returns:
        合并后的字典
    """
    result = {}

    for obj in objects:
        if not isinstance(obj, dict):
            continue

        for key, value in obj.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = merge_json_objects(result[key], value)
            else:
                result[key] = value

    return result
