import logging

import structlog

from app.core.config import settings
from app.middleware.traceid import get_trace_id


def add_trace_id(_, __, event_dict):
    """添加 TraceID 到日志事件中"""
    event_dict["trace_id"] = get_trace_id()
    return event_dict


def configure_logging():
    """配置结构化日志"""
    logging.basicConfig(
        format="%(message)s",
        level=getattr(logging, settings.LOG_LEVEL.upper()),
    )

    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            add_trace_id,
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.dev.ConsoleRenderer() if settings.DEBUG else structlog.processors.JSONRenderer(),
        ],
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=structlog.stdlib.LoggerFactory(),
        cache_logger_on_first_use=True,
    )


def get_logger(name: str = __name__):
    """获取结构化日志记录器"""
    return structlog.get_logger(name)


# 在模块加载时配置日志
configure_logging()
