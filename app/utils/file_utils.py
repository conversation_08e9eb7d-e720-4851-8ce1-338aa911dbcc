"""文件处理工具函数"""

import os
from pathlib import Path

import streamlit as st
from structlog import get_logger

logger = get_logger(__name__)

# 支持的文件格式
SUPPORTED_IMAGE_FORMATS = {".jpg", ".jpeg", ".png", ".bmp"}
SUPPORTED_PDF_FORMAT = ".pdf"
MAX_FILE_SIZE = 200 * 1024 * 1024  # 200MB


def validate_file_format(file_name: str) -> tuple[bool, str]:
    """验证文件格式

    Args:
        file_name: 文件名

    Returns:
        (是否有效, 文件类型)
    """
    suffix = Path(file_name).suffix.lower()

    if suffix == SUPPORTED_PDF_FORMAT:
        return True, "pdf"
    elif suffix in SUPPORTED_IMAGE_FORMATS:
        return True, "image"
    else:
        return False, "unknown"


def validate_file_size(file_size: int) -> bool:
    """验证文件大小

    Args:
        file_size: 文件大小（字节）

    Returns:
        是否在允许范围内
    """
    return file_size <= MAX_FILE_SIZE


def save_uploaded_file(uploaded_file, download_dir: str = "download") -> str:
    """保存上传的文件到本地

    Args:
        uploaded_file: Streamlit 上传的文件对象
        download_dir: 下载目录

    Returns:
        保存的文件路径
    """
    # 确保下载目录存在
    download_path = Path(download_dir)
    download_path.mkdir(exist_ok=True)

    # 构造文件路径
    file_path = download_path / uploaded_file.name

    # 保存文件
    with open(file_path, "wb") as f:
        f.write(uploaded_file.getvalue())

    logger.info(f"文件已保存: {file_path}")
    return str(file_path)


def save_multiple_files(uploaded_files: list, download_dir: str = "download") -> list[str]:
    """保存多个上传的文件

    Args:
        uploaded_files: 上传的文件列表
        download_dir: 下载目录

    Returns:
        保存的文件路径列表
    """
    saved_files = []

    for uploaded_file in uploaded_files:
        try:
            file_path = save_uploaded_file(uploaded_file, download_dir)
            saved_files.append(file_path)
        except Exception as e:
            logger.error(f"保存文件失败: {uploaded_file.name}, 错误: {e!s}")
            st.error(f"保存文件失败: {uploaded_file.name}")

    return saved_files


def cleanup_temp_files(file_paths: list[str]):
    """清理临时文件

    Args:
        file_paths: 需要清理的文件路径列表
    """
    for file_path in file_paths:
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"临时文件已清理: {file_path}")
        except Exception as e:
            logger.error(f"清理临时文件失败: {file_path}, 错误: {e!s}")


def format_file_size(size_bytes: int) -> str:
    """格式化文件大小显示

    Args:
        size_bytes: 文件大小（字节）

    Returns:
        格式化后的大小字符串
    """
    if size_bytes == 0:
        return "0 B"

    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024
        i += 1

    return f"{size_bytes:.1f} {size_names[i]}"


def get_file_info(uploaded_file) -> dict:
    """获取文件信息

    Args:
        uploaded_file: Streamlit 上传的文件对象

    Returns:
        文件信息字典
    """
    is_valid, file_type = validate_file_format(uploaded_file.name)
    is_size_valid = validate_file_size(uploaded_file.size)

    return {
        "name": uploaded_file.name,
        "size": uploaded_file.size,
        "size_formatted": format_file_size(uploaded_file.size),
        "type": file_type,
        "is_valid_format": is_valid,
        "is_valid_size": is_size_valid,
        "is_valid": is_valid and is_size_valid,
    }
