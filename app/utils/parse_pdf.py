import re


def remove_markdown_images(text):
    """
    从文本中删除所有的Markdown图片标记

    参数:
        text (str): 包含Markdown图片的文本

    返回:
        str: 删除图片后的文本
    """
    # 正则表达式匹配 ![任意文字](任意URL)
    # 使用非贪婪匹配来确保正确匹配每个图片
    pattern = r"!\[([^\]]*)\]\([^\)]+\)"

    # 替换所有匹配的图片标记为空字符串
    cleaned_text = re.sub(pattern, "", text)

    # 清理多余的空行（可选）
    # 将连续的多个换行符替换为最多两个换行符
    cleaned_text = re.sub(r"\n{3,}", "\n\n", cleaned_text)

    # 去除行首尾的空白字符
    lines = cleaned_text.split("\n")
    cleaned_lines = [line.strip() for line in lines]
    cleaned_text = "\n".join(cleaned_lines)

    return cleaned_text


def remove_markdown_images_with_space(text):
    """
    从文本中删除所有的Markdown图片标记，保留图片位置的空行

    参数:
        text (str): 包含Markdown图片的文本

    返回:
        str: 删除图片后的文本
    """
    pattern = r"!\[([^\]]*)\]\([^\)]+\)"
    cleaned_text = re.sub(pattern, "", text)
    return cleaned_text


if __name__ == "__main__":
    text = "![fig_53237](https://pdf-bucket.tos-cn-beijing.volces.com/infer/20250714/eff39a35616246de9d90accdbaa2426e_1752485124852576741.jpg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTZDcxYzI0ZWI5NDkzNGRkNWI1OTdiNTVjNjZjYjU3NmQ%2F20250714%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250714T092524Z&X-Tos-Expires=1800&X-Tos-Signature=5ec5628eb14f6b51d313b0fe40b5a36898c51aa37cc1fd6305c31872d972e7c3&X-Tos-SignedHeaders=host) \n西安交通大学第一附属医院体检报告 \n\n| 体检机构:西安交通大学第一附属医院 | | 体检编号:210929000003 |\n| --- | --- | --- |\n| 体检项目类别:健康体检 | | 体检日期:2021-09-29 |\n| 姓名:候妮 | 性别:女 | 年龄:32岁 |\n| 身份证:610323198902064267 | 婚姻:已婚 | 联系方式:18192074283 |\n \n![fig_20244](https://pdf-bucket.tos-cn-beijing.volces.com/infer/20250714/eff39a35616246de9d90accdbaa2426e_1752485124854046116.jpg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTZDcxYzI0ZWI5NDkzNGRkNWI1OTdiNTVjNjZjYjU3NmQ%2F20250714%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250714T092524Z&X-Tos-Expires=1800&X-Tos-Signature=0b8cf81a393be47f07aba9254056d0a44697b8b81afa448d2040313371f1d03d&X-Tos-SignedHeaders=host) \n \n尊敬的 候妮 女士: \n您好!感谢您选择西安交通大学第一附属医院体检中心!健康体检是指通过医学手段和方法 对受检者进行身体检查,了解受检者健康状况,早期发现疾病线索和健康隐患的诊疗行为。现我 们将您的体检结果汇总报告如下,为了您的身体健康,建议您详细阅读健康指导建议,每年进行 一次系统体检。如果您对体检结果有异议,请致电体检中心进行核查(029-85323185)。期待您 的再次选择! \n目前仍处于疫情防控的特殊时期,本中心暂停门诊报告咨询服务,为您带来不便,敬请谅 解! \n检查汇总: \n1.身高、体重: 1)BMIT:27.18 kg/m2t \n2.妇科普检: 1)宫颈:柱状上皮异位(中度) \n3.十二导心电分析: 大致正常心电图 \n4.胸部CT: 两肺未见活动性病变,纵隔未见肿大淋巴结。 \n5.彩超(肝胆胰脾、双肾、子宫附件): 轻度脂肪肝:胆囊壁胆固醇结晶。 \n6.彩超(甲状腺): 甲状腺左叶结节,ACR TI-RADS3类(低度可疑恶性, $恶性<5\\%$ , $结节≥2.5cm FNA$ :结节≥ \n1.5cm随访):甲状腺弥漫性病变。 \n7.彩超(乳腺): 左侧乳腺囊肿,BI-RADS2类;左侧乳腺导管局限性扩张,BI-RADS 2类; \n8.血细胞分析+五分类: 1)红细胞分布宽度-CV值:11.5%↓ \n9.平诊血脂四项[复]: 1)高密度脂蛋白:1.20mmol/L↓ \n10.平诊肝功五项[复]: 1)总蛋白(TP):64.4g/L↓ \n总检结论: \n1. 高密度脂蛋白偏低 \n2. 超重 \n3.轻度脂肪肝 \n4. 甲状腺左叶结节甲状腺弥漫性病变 \n您的健康,我们的目标"
    r = remove_markdown_images_with_space(text)
    print(r)
