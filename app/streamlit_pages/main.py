"""Streamlit 主应用入口

运行命令：
streamlit run app/streamlit_pages/main.py
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import streamlit as st
from pages.health_report import health_report_page

# 页面配置
st.set_page_config(page_title="AI 医疗助手", page_icon="🏥", layout="wide", initial_sidebar_state="expanded")

# 主标题
st.title("🏥 AI 医疗助手")
st.markdown("---")

# 侧边栏导航
st.sidebar.title("导航")
page = st.sidebar.selectbox("选择功能", ["体检报告解析", "其他功能"])

# 页面路由
if page == "体检报告解析":
    health_report_page()
else:
    st.info("其他功能正在开发中...")
