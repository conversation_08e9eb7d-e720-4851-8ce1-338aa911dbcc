"""体检报告解析页面"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import asyncio
import hashlib
import json
import os
import time
import uuid
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime

import requests
import streamlit as st
from app.client.tos_client import get_tos_client
from app.services.report_ocr_handler import get_report_ocr_handler
from app.utils.file_utils import MAX_FILE_SIZE, cleanup_temp_files, format_file_size, get_file_info, save_uploaded_file
from app.utils.json_utils import safe_json_parse
from app.utils.pdf_utils import images_to_pdf


def safe_display_json(data, label: str = None):
    """安全显示JSON数据，自动处理各种格式"""
    try:
        # 如果是字符串，尝试使用safe_json_parse解析
        if isinstance(data, str):
            parsed_data = safe_json_parse(data)
            st.json(parsed_data)
        else:
            # 如果已经是字典或列表，直接显示
            st.json(data)
    except Exception as e:
        # 如果还是失败，显示为文本
        if label:
            st.text(f"{label}: {data}")
        else:
            st.text(str(data))


def display_file_info(file_info: dict, index: int | None = None):
    """显示文件信息"""
    prefix = f"文件 {index + 1}: " if index is not None else ""

    if file_info["is_valid"]:
        st.success(f"✅ {prefix}{file_info['name']} ({file_info['size_formatted']})")
    else:
        error_msg = f"❌ {prefix}{file_info['name']} - "
        if not file_info["is_valid_format"]:
            error_msg += "不支持的文件格式"
        elif not file_info["is_valid_size"]:
            error_msg += f"文件过大 ({file_info['size_formatted']}，最大 {format_file_size(MAX_FILE_SIZE)})"
        st.error(error_msg)


def upload_file_to_tos(file_path: str, tos_client) -> str | None:
    """上传文件到 TOS 并返回预签名访问链接"""
    try:
        file_name = Path(file_path).name
        object_key = f"health_reports/{int(time.time())}_{file_name}"

        result = tos_client.upload_file(file_path=file_path, object_key=object_key)

        if result.get("success"):
            # 获取预签名访问链接
            pre_signed_url = tos_client.get_object_url(object_key)
            print(f"预签名地址: {pre_signed_url}")
            return pre_signed_url
        else:
            st.error(f"上传失败: {file_name}")
            return None
    except Exception as e:
        st.error(f"上传文件时出错: {e!s}")
        return None


async def parse_local_pdf_async(file_path: str, ocr_handler) -> dict:
    """异步解析本地PDF文件"""
    try:
        result = await ocr_handler.parse_pdf_file(file_path, page_start=1, page_num=50)
        return result
    except Exception as e:
        return {"success": False, "error": str(e)}


def parse_local_pdf(file_path: str, ocr_handler) -> dict:
    """同步包装器：异步解析本地PDF文件"""
    try:
        return asyncio.run(parse_local_pdf_async(file_path, ocr_handler))
    except Exception as e:
        return {"success": False, "error": str(e)}


async def parse_report_async(file_url: str, ocr_handler) -> dict:
    """异步解析报告文件"""
    try:
        result = await ocr_handler.parse_pdf_url(file_url=file_url, page_start=1, page_num=50)
        return result
    except Exception as e:
        return {"success": False, "error": str(e)}


def parse_report(file_url: str, ocr_handler) -> dict:
    """同步包装器：异步解析报告文件"""
    try:
        return asyncio.run(parse_report_async(file_url, ocr_handler))
    except Exception as e:
        return {"success": False, "error": str(e)}


def merge_images_to_pdf(uploaded_files, image_order: list) -> str | None:
    """将多个图片文件按指定顺序合并为PDF"""
    try:
        # 按指定顺序保存图片文件到临时目录
        temp_image_paths = []
        for idx in image_order:
            uploaded_file = uploaded_files[idx]
            temp_path = save_uploaded_file(uploaded_file)
            temp_image_paths.append(temp_path)

        # 生成临时PDF文件路径
        timestamp = int(time.time())
        temp_pdf_path = f"/tmp/merged_images_{timestamp}.pdf"

        # 合并图片为PDF
        success = images_to_pdf(temp_image_paths, temp_pdf_path)

        if success:
            # 清理临时图片文件，但保留PDF文件
            cleanup_temp_files(temp_image_paths)
            return temp_pdf_path
        else:
            # 清理临时文件
            cleanup_temp_files(temp_image_paths)
            return None

    except Exception as e:
        st.error(f"合并图片为PDF时出错: {str(e)}")
        # 清理临时文件
        if "temp_image_paths" in locals():
            cleanup_temp_files(temp_image_paths)
        return None


def call_agent_api(input_text: str, agent_type: str) -> dict:
    """调用Agent API"""
    try:
        # 生成用户ID和会话ID
        user_id = str(uuid.uuid4())
        session_id = str(uuid.uuid4())

        # 准备请求数据
        request_data = {
            "input_text": input_text,
            "agent_type": agent_type,
            "user_id": user_id,
            "session_id": session_id,
        }

        # 获取FastAPI基础URL，默认使用localhost用于本地开发
        fastapi_base_url = os.getenv("FASTAPI_BASE_URL", "http://localhost:8000")
        api_url = f"{fastapi_base_url}/api/v1/report/agent/run"

        # 发送请求
        response = requests.post(
            api_url,
            json=request_data,
            headers={"Content-Type": "application/json"},
        )

        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 0:
                data = result.get("data")
                # 如果返回的data是字符串格式的JSON，使用safe_json_parse解析
                if isinstance(data, str):
                    data = safe_json_parse(data)
                return {"success": True, "data": data}
            else:
                return {"success": False, "error": result.get("message", "API返回错误")}
        else:
            return {"success": False, "error": f"HTTP {response.status_code}: {response.text}"}

    except Exception as e:
        return {"success": False, "error": str(e)}


def prepare_input_text(parse_result: dict, agent_type: str) -> str:
    """根据Agent类型准备输入文本，包含page_id和page_md"""
    if not parse_result.get("pretty"):
        return ""

    pages_data = parse_result["pretty"]

    if agent_type == "meta_extractor":
        # 取前4个页面的数据
        selected_pages = pages_data[:4]
    elif agent_type == "tabular_parser":
        # 取所有页面的数据用于表格解析
        selected_pages = pages_data
    elif agent_type == "img_parser":
        # 取所有页面的数据用于影像数据提取, 这里使用 detail 全量数据包含图片地址
        selected_pages = parse_result["detail"]
    elif agent_type == "existing_diseases":
        # 取所有页面的数据用于疾病提取分析
        selected_pages = pages_data
    else:  # overall_result
        # 取所有页面的数据
        selected_pages = pages_data

    # 构造包含page_id和page_md的字符串
    input_parts = []
    for page in selected_pages:
        page_id = page.get("page_id", "")
        page_md = page.get("page_md", "")
        input_parts.append(f"页码: {page_id}\n内容: {page_md}")

    return "\n\n".join(input_parts)


def save_agent_result_to_local(data: dict, agent_type: str, filename_prefix: str):
    """保存Agent结果到本地文件"""
    # 生成文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{filename_prefix}_{agent_type}_{timestamp}.json"

    # 创建下载链接
    json_str = json.dumps(data, ensure_ascii=False, indent=2)
    st.download_button(
        label="💾 保存结果到本地", data=json_str, file_name=filename, mime="application/json", use_container_width=True
    )


def save_merged_results_to_local(merged_data: dict, filename_prefix: str):
    """保存合并结果到本地文件"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{filename_prefix}_merged_analysis_{timestamp}.json"

    # 创建下载链接
    json_str = json.dumps(merged_data, ensure_ascii=False, indent=2)
    st.download_button(
        label="📥 下载合并分析结果",
        data=json_str,
        file_name=filename,
        mime="application/json",
        type="primary",
        use_container_width=True,
    )


def run_agent_analysis(input_text: str, agent_type: str) -> dict:
    """运行单个agent分析"""
    return call_agent_api(input_text, agent_type)


def run_single_agent(agent_type: str, selected_result: dict, file_index: int) -> bool:
    """运行单个agent分析"""
    agent_names = {
        "meta_extractor": "个人信息",
        "overall_result": "总体结果",
        "tabular_parser": "检查项指标",
        "img_parser": "影像数据",
        "existing_diseases": "疾病提取",
    }

    try:
        input_text = prepare_input_text(selected_result, agent_type)
        result = run_agent_analysis(input_text, agent_type)

        if result.get("success"):
            st.session_state[f"{agent_type}_data_{file_index}"] = result["data"]
            return True
        else:
            st.error(f"❌ {agent_names[agent_type]}分析失败: {result.get('error', '未知错误')}")
            return False
    except Exception as e:
        st.error(f"❌ 执行{agent_names[agent_type]}分析时出错: {str(e)}")
        return False


def run_all_agents_in_parallel(selected_result: dict, file_index: int):
    """并行运行所有AI分析"""
    agent_types = ["meta_extractor", "overall_result", "tabular_parser", "img_parser", "existing_diseases"]
    agent_names = {
        "meta_extractor": "个人信息",
        "overall_result": "总体结果",
        "tabular_parser": "检查项指标",
        "img_parser": "影像数据",
        "existing_diseases": "疾病提取",
    }

    progress_bar = st.progress(0)
    status_container = st.empty()
    results_container = st.container()

    # 创建线程池并行执行
    with ThreadPoolExecutor(max_workers=4) as executor:
        # 提交所有任务
        future_to_agent = {}
        for agent_type in agent_types:
            input_text = prepare_input_text(selected_result, agent_type)
            future = executor.submit(run_agent_analysis, input_text, agent_type)
            future_to_agent[future] = agent_type

        completed_count = 0
        total_count = len(agent_types)
        results = {}

        # 使用 as_completed 实时处理已完成的任务，并立即更新UI
        for future in as_completed(future_to_agent):
            agent_type = future_to_agent[future]

            try:
                result = future.result()
                results[agent_type] = result

                # 立即渲染单个任务的结果
                if result.get("success"):
                    st.session_state[f"{agent_type}_data_{file_index}"] = result["data"]
                    with results_container:
                        display_single_analysis_result(
                            result["data"],
                            agent_names[agent_type],
                            agent_type,
                            selected_result["file_name"],
                            file_index,
                        )
                else:
                    with results_container:
                        st.error(f"❌ {agent_names[agent_type]}分析失败: {result.get('error', '未知错误')}")
            except Exception as e:
                # 处理任务执行期间的异常
                results[agent_type] = {"success": False, "error": str(e)}
                with results_container:
                    st.error(f"❌ {agent_names[agent_type]}分析时发生意外错误: {str(e)}")

            # 更新总体进度
            completed_count += 1
            progress = completed_count / total_count
            progress_bar.progress(progress)
            # 更新状态文本，显示最新完成的任务
            status_container.info(f"✅ 已完成: {agent_names[agent_type]} ({completed_count}/{total_count})")

    progress_bar.progress(1.0)
    status_container.success(
        f"✅ 并行分析完成！成功: {sum(1 for r in results.values() if r.get('success'))}/{total_count}"
    )


def run_progressive_analysis(selected_result: dict, file_index: int):
    """逐步运行AI分析，每完成一项立即渲染结果（保留原有串行版本）"""
    agent_types = ["meta_extractor", "overall_result", "tabular_parser", "img_parser", "existing_diseases"]
    agent_names = {
        "meta_extractor": "个人信息",
        "overall_result": "总体结果",
        "tabular_parser": "检查项指标",
        "img_parser": "影像数据",
        "existing_diseases": "疾病提取",
    }

    # 创建进度条和状态显示
    progress_bar = st.progress(0)
    status_container = st.empty()

    # 创建结果展示容器
    results_container = st.container()

    completed_count = 0
    total_count = len(agent_types)

    for i, agent_type in enumerate(agent_types):
        # 更新进度和状态
        progress = i / total_count
        progress_bar.progress(progress)
        status_container.info(f"🔄 正在执行: {agent_names[agent_type]} ({i+1}/{total_count})")

        # 执行分析
        input_text = prepare_input_text(selected_result, agent_type)
        result = run_agent_analysis(input_text, agent_type)

        if result.get("success"):
            # 保存到session state
            st.session_state[f"{agent_type}_data_{file_index}"] = result["data"]
            completed_count += 1

            # 立即在结果容器中显示这个结果
            with results_container:
                display_single_analysis_result(
                    result["data"], agent_names[agent_type], agent_type, selected_result["file_name"], file_index
                )
        else:
            # 显示错误信息
            with results_container:
                st.error(f"❌ {agent_names[agent_type]}分析失败: {result.get('error', '未知错误')}")

    # 完成所有分析
    progress_bar.progress(1.0)
    status_container.success(f"✅ 所有分析完成！成功: {completed_count}/{total_count}")


def merge_analysis_results(selected_result: dict, file_index: int) -> dict:
    """合并所有分析结果"""
    agent_types = ["meta_extractor", "overall_result", "tabular_parser", "img_parser", "existing_diseases"]
    agent_names = {
        "meta_extractor": "个人信息",
        "overall_result": "总体结果",
        "tabular_parser": "检查项指标",
        "img_parser": "影像数据",
        "existing_diseases": "疾病提取",
    }

    merged_data = {
        "file_name": selected_result["file_name"],
        "analysis_timestamp": datetime.now().isoformat(),
        "merged_results": {},
    }

    # 收集所有结果
    for agent_type in agent_types:
        if st.session_state.get(f"{agent_type}_data_{file_index}"):
            merged_data["merged_results"][agent_names[agent_type]] = {
                "agent_type": agent_type,
                "data": st.session_state[f"{agent_type}_data_{file_index}"],
            }
        else:
            merged_data["merged_results"][agent_names[agent_type]] = {
                "agent_type": agent_type,
                "data": {"error": "未完成分析"},
            }

    return merged_data


def display_agent_result(data: dict, title: str):
    """显示Agent结果（仅JSON格式）"""
    st.markdown(f"### {title}")
    safe_display_json(data, title)


def display_single_analysis_result(data: dict, title: str, agent_type: str, filename: str, file_index: int):
    """显示单个分析结果"""
    with st.expander(f"✅ {title} - 分析完成", expanded=True):
        safe_display_json(data, title)

        # 添加保存单个结果的按钮
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_filename = f"{filename}_{agent_type}_{timestamp}.json"
        json_str = json.dumps(data, ensure_ascii=False, indent=2)

        st.download_button(
            label=f"💾 保存{title}结果",
            data=json_str,
            file_name=result_filename,
            mime="application/json",
            key=f"download_result_{agent_type}_{file_index}_{int(time.time()*1000)%10000}",
            use_container_width=True,
        )


def display_merge_download_section(selected_result: dict, file_index: int):
    """显示合并下载区域"""
    st.markdown("---")
    st.subheader("🎯 全部分析完成")

    # 合并所有结果
    merged_data = merge_analysis_results(selected_result, file_index)

    # 显示合并结果预览
    with st.expander("📊 合并分析结果预览", expanded=False):
        safe_display_json(merged_data, "合并分析结果")

    # 一键保存按钮
    col1, col2 = st.columns([2, 1])

    with col1:
        st.success("🎉 所有AI分析模块已完成！可以下载完整的分析报告。")

    with col2:
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{selected_result['file_name']}_完整分析报告_{timestamp}.json"
        json_str = json.dumps(merged_data, ensure_ascii=False, indent=2)

        st.download_button(
            label="📥 一键保存完整报告",
            data=json_str,
            file_name=filename,
            mime="application/json",
            type="primary",
            use_container_width=True,
            key=f"download_complete_report_{file_index}_{int(time.time()*1000)%10000}",
        )


# 本地数据库管理功能
LOCAL_DB_DIR = os.path.join(os.path.expanduser("~"), ".health_report_cache")
LOCAL_DB_FILE = os.path.join(LOCAL_DB_DIR, "report_database.json")


def init_local_db():
    """初始化本地数据库"""
    if not os.path.exists(LOCAL_DB_DIR):
        os.makedirs(LOCAL_DB_DIR)

    if not os.path.exists(LOCAL_DB_FILE):
        with open(LOCAL_DB_FILE, "w", encoding="utf-8") as f:
            json.dump({}, f)


def get_file_hash(file_content: bytes) -> str:
    """计算文件内容的hash值"""
    return hashlib.md5(file_content).hexdigest()


def load_local_db() -> dict:
    """加载本地数据库"""
    init_local_db()
    try:
        with open(LOCAL_DB_FILE, "r", encoding="utf-8") as f:
            return json.load(f)
    except (json.JSONDecodeError, FileNotFoundError):
        return {}


def save_local_db(db_data: dict):
    """保存本地数据库"""
    init_local_db()
    with open(LOCAL_DB_FILE, "w", encoding="utf-8") as f:
        json.dump(db_data, f, ensure_ascii=False, indent=2)


def save_file_to_cache(file_content: bytes, filename: str, content_type: str) -> str:
    """保存文件到缓存目录"""
    init_local_db()
    file_hash = get_file_hash(file_content)
    cache_filename = f"{file_hash}_{content_type}_{filename}"
    cache_path = os.path.join(LOCAL_DB_DIR, cache_filename)

    with open(
        cache_path,
        "wb" if content_type == "original" else "w",
        encoding=None if content_type == "original" else "utf-8",
    ) as f:
        if content_type == "original":
            f.write(file_content)
        else:
            f.write(file_content.decode("utf-8") if isinstance(file_content, bytes) else file_content)

    return cache_path


def get_cached_result(filename: str, file_content: bytes) -> dict | None:
    """获取缓存的解析结果"""
    db_data = load_local_db()
    file_hash = get_file_hash(file_content)

    cache_key = f"{filename}_{file_hash}"
    return db_data.get(cache_key)


def save_parse_result(filename: str, file_content: bytes, parse_result: dict):
    """保存解析结果到本地数据库"""
    db_data = load_local_db()
    file_hash = get_file_hash(file_content)

    # 保存markdown和json文件
    markdown_path = None
    json_path = None
    pretty_json_path = None

    if parse_result.get("markdown"):
        markdown_path = save_file_to_cache(parse_result["markdown"].encode("utf-8"), f"{filename}.md", "markdown")

    if parse_result.get("detail"):
        json_path = save_file_to_cache(
            json.dumps(parse_result["detail"], ensure_ascii=False, indent=2).encode("utf-8"), f"{filename}.json", "json"
        )

    if parse_result.get("pretty"):
        pretty_json_path = save_file_to_cache(
            json.dumps(parse_result["pretty"], ensure_ascii=False, indent=2).encode("utf-8"),
            f"{filename}-pretty.json",
            "json",
        )

    # 保存到数据库
    cache_key = f"{filename}_{file_hash}"
    db_data[cache_key] = {
        "filename": filename,
        "file_hash": file_hash,
        "timestamp": datetime.now().isoformat(),
        "markdown_path": markdown_path,
        "json_path": json_path,
        "pretty_json_path": pretty_json_path,
        "parse_result": parse_result,
    }

    save_local_db(db_data)


def render_ai_analysis_section():
    """渲染AI智能分析模块"""
    if not st.session_state.get("parse_results"):
        return

    st.markdown("---")
    st.subheader("🤖 AI智能分析")

    all_results = st.session_state["parse_results"]
    agent_definitions = {
        "meta_extractor": {
            "name": "👤 个人/机构信息",
            "icon": "👤",
            "description": "提取个人和机构基础信息",
            "color": "blue",
        },
        "overall_result": {
            "name": "📊 总检结果",
            "icon": "📊",
            "description": "生成报告的总体健康摘要和建议、异常指标、风险等级",
            "color": "green",
        },
        "tabular_parser": {
            "name": "📈 检查项指标",
            "icon": "📈",
            "description": "解析表格中的各项检查指标和参考范围",
            "color": "orange",
        },
        "img_parser": {
            "name": "🖼️ 影像数据",
            "icon": "🖼️",
            "description": "提取B超、X光、CT等影像检查的文字描述",
            "color": "purple",
        },
        "existing_diseases": {
            "name": "🏥 疾病提取",
            "icon": "🏥",
            "description": "提取报告中提到的疾病和健康异常",
            "color": "red",
        },
    }

    # 选择要分析的文件
    if len(all_results) > 1:
        selected_file_index = st.selectbox(
            "选择要分析的文件",
            range(len(all_results)),
            format_func=lambda x: all_results[x]["file_name"],
            key="ai_analysis_file_selector",
        )
    else:
        selected_file_index = 0

    if selected_file_index < len(all_results):
        selected_result = all_results[selected_file_index]
        filename = selected_result["file_name"]

        # 一键AI智能分析区域
        col1, col2 = st.columns([3, 1])
        with col1:
            if st.button(
                "🚀 一键AI智能分析（并行执行）",
                key=f"parallel_analysis_all_{selected_file_index}",
                use_container_width=True,
                type="primary",
            ):
                # 清除之前的分析结果
                for agent_type in agent_definitions.keys():
                    key = f"{agent_type}_data_{selected_file_index}"
                    if key in st.session_state:
                        del st.session_state[key]
                # 运行并行分析
                run_all_agents_in_parallel(selected_result, selected_file_index)
        with col2:
            # 显示整体状态
            completed_count = sum(
                1
                for agent_type in agent_definitions.keys()
                if st.session_state.get(f"{agent_type}_data_{selected_file_index}")
            )
            total_count = len(agent_definitions)
            st.metric("完成进度", f"{completed_count}/{total_count}")

        st.markdown("---")
        st.markdown("**📋 或者，选择单项进行分析：**")

        # 单项分析控制面板
        for agent_type, details in agent_definitions.items():
            status_key = f"{agent_type}_data_{selected_file_index}"
            is_completed = status_key in st.session_state
            is_failed = st.session_state.get(f"{agent_type}_failed_{selected_file_index}", False)

            # 创建卡片式布局
            with st.container():
                col1, col2, col3, col4 = st.columns([0.5, 2.5, 1.5, 1.5])

                with col1:
                    st.markdown(f"### {details['icon']}")

                with col2:
                    st.markdown(f"**{details['name']}**")
                    st.caption(details["description"])
                    if is_completed:
                        st.success("✅ 已完成")
                    elif is_failed:
                        st.error("❌ 失败")
                    else:
                        st.info("🔄 未开始")

                with col3:
                    button_text = "重新分析" if is_completed else ("重试" if is_failed else "开始分析")
                    button_type = "secondary" if is_completed else "primary"
                    if st.button(
                        button_text,
                        key=f"run_{agent_type}_{selected_file_index}",
                        use_container_width=True,
                        type=button_type,
                    ):
                        with st.spinner(f"正在分析 {details['name']}..."):
                            # 清除之前的结果和失败状态
                            if status_key in st.session_state:
                                del st.session_state[status_key]
                            st.session_state[f"{agent_type}_failed_{selected_file_index}"] = False

                            success = run_single_agent(agent_type, selected_result, selected_file_index)
                            if not success:
                                st.session_state[f"{agent_type}_failed_{selected_file_index}"] = True
                        st.rerun()

                with col4:
                    if is_completed:
                        data = st.session_state[status_key]
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        result_filename = f"{filename}_{agent_type}_{timestamp}.json"
                        json_str = json.dumps(data, ensure_ascii=False, indent=2)
                        st.download_button(
                            label="下载",
                            data=json_str,
                            file_name=result_filename,
                            mime="application/json",
                            key=f"download_single_{agent_type}_{selected_file_index}",
                            use_container_width=True,
                        )

        # 合并下载区域 - 只要有至少一项完成就显示
        completed_agents = [
            agent_type
            for agent_type in agent_definitions.keys()
            if st.session_state.get(f"{agent_type}_data_{selected_file_index}")
        ]

        if completed_agents:
            st.markdown("---")
            st.subheader("📦 合并下载区域")

            col1, col2 = st.columns([2, 1])
            with col1:
                st.success(f"🎉 已完成 {len(completed_agents)} 项分析，可以下载合并结果")
            with col2:
                merged_data = merge_analysis_results(selected_result, selected_file_index)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{selected_result['file_name']}_分析报告_{timestamp}.json"
                json_str = json.dumps(merged_data, ensure_ascii=False, indent=2)
                st.download_button(
                    label="📥 下载合并结果JSON数据",
                    data=json_str,
                    file_name=filename,
                    mime="application/json",
                    type="primary",
                    use_container_width=True,
                )

        # 显示已完成的分析结果详情
        st.markdown("---")
        st.subheader("📊 分析结果详情")

        results_display_container = st.container()
        with results_display_container:
            displayed_results = []
            for agent_type, details in agent_definitions.items():
                status_key = f"{agent_type}_data_{selected_file_index}"
                if st.session_state.get(status_key):
                    displayed_results.append(agent_type)
                    display_single_analysis_result(
                        st.session_state[status_key], details["name"], agent_type, filename, selected_file_index
                    )

            if not displayed_results:
                st.info("👆 请选择上方的分析项目进行执行，或使用一键分析功能")


def health_report_page():
    """体检报告解析页面主函数"""
    st.header("📋 体检报告解析")
    st.markdown("上传 PDF 文件或图片，系统将自动提取文本内容并进行结构化解析。")

    # 上半部分：创建左右两列布局
    col1, col2 = st.columns([1, 1])

    # 左侧：文件上传区域
    with col1:
        st.subheader("📁 文件上传")

        # 使用标签页区分 PDF 和图片上传
        tab1, tab2 = st.tabs(["📄 PDF 文件", "🖼️ 图片文件"])

        uploaded_files = []
        file_type = None

        with tab1:
            st.markdown("**支持格式**: PDF")
            st.markdown(f"**文件大小限制**: 最大 {format_file_size(MAX_FILE_SIZE)}")

            pdf_file = st.file_uploader("选择 PDF 文件", type=["pdf"], accept_multiple_files=False, key="pdf_uploader")

            # PDF文件选择后立即上传
            if pdf_file and pdf_file not in st.session_state.get("processed_files", []):
                # 标记文件正在处理
                if "processed_files" not in st.session_state:
                    st.session_state["processed_files"] = []
                st.session_state["processed_files"].append(pdf_file)

                # 立即上传文件
                with st.spinner(f"正在上传 {pdf_file.name}..."):
                    try:
                        # 验证文件
                        file_info = get_file_info(pdf_file)
                        if not file_info["is_valid"]:
                            if not file_info["is_valid_format"]:
                                st.error("❌ 不支持的文件格式")
                            elif not file_info["is_valid_size"]:
                                st.error(
                                    f"❌ 文件过大 ({file_info['size_formatted']}，最大 {format_file_size(MAX_FILE_SIZE)})"
                                )
                        else:
                            file_name = pdf_file.name
                            file_content = pdf_file.read()

                            # 检查是否有缓存的解析结果
                            cached_result = get_cached_result(file_name, file_content)

                            if cached_result:
                                # 使用缓存的结果
                                st.info(f"📄 发现缓存的解析结果: {file_name}")
                                st.session_state["uploaded_pdf"] = {
                                    "file_name": file_name,
                                    "url": "#cached",  # 缓存标识
                                    "object_key": f"cached_{file_name}",
                                }
                                st.session_state["cached_parse_result"] = cached_result["parse_result"]
                                st.success(f"✅ {file_name} 已加载缓存结果！")
                            else:
                                # 保存文件到本地临时位置
                                temp_file_path = save_uploaded_file(pdf_file)

                                # 初始化TOS客户端并上传
                                tos_client = get_tos_client()
                                object_key = f"health_reports/{int(time.time())}_{file_name}"

                                # 上传文件
                                result = tos_client.upload_file(file_path=temp_file_path, object_key=object_key)

                                if result.get("success"):
                                    # 获取预签名访问链接
                                    pre_signed_url = tos_client.get_object_url(object_key)

                                    # 保存到session state
                                    st.session_state["uploaded_pdf"] = {
                                        "file_name": file_name,
                                        "url": pre_signed_url,
                                        "object_key": object_key,
                                    }

                                    # 保存文件内容用于后续缓存
                                    st.session_state["uploaded_pdf_content"] = file_content

                                    st.success(f"✅ {file_name} 上传成功！")

                                    # 清理临时文件
                                    cleanup_temp_files([temp_file_path])
                                else:
                                    st.error(f"❌ 上传失败: {file_name}")

                    except Exception as e:
                        st.error(f"❌ 上传过程中出错: {e!s}")

            # 显示已上传的PDF信息
            if st.session_state.get("uploaded_pdf"):
                uploaded_info = st.session_state["uploaded_pdf"]
                st.info(f"📄 已上传文件: {uploaded_info['file_name']}")
                with st.expander("查看文件URL"):
                    st.code(uploaded_info["url"], language="text")

                uploaded_files = [pdf_file] if pdf_file else []
                file_type = "pdf"

        with tab2:
            # 创建左右布局，与PDF文件tab保持一致
            col_left, col_right = st.columns([1, 1])

            with col_left:
                st.markdown("**支持格式**: JPG, JPEG, PNG, BMP")
                st.markdown(f"**文件大小限制**: 每个文件最大 {format_file_size(MAX_FILE_SIZE)}")
                st.info("📑 所有图片将自动合并为PDF文件进行处理")

                image_files = st.file_uploader(
                    "选择图片文件（将合并为PDF）",
                    type=["jpg", "jpeg", "png", "bmp"],
                    accept_multiple_files=True,
                    key="image_uploader",
                )

                # 图片顺序调整（所有图片都需要合并为PDF）
                if image_files:
                    st.markdown("---")
                    st.subheader("📋 调整图片顺序")
                    st.info("💡 图片将按以下顺序合并为PDF，可通过按钮调整顺序")

                    # 初始化图片顺序到session state
                    if "image_order" not in st.session_state or len(st.session_state["image_order"]) != len(
                        image_files
                    ):
                        st.session_state["image_order"] = list(range(len(image_files)))

                    # 显示可调整的图片列表
                    for i, idx in enumerate(st.session_state["image_order"]):
                        img_col1, img_col2, img_col3 = st.columns([2, 1, 1])

                        with img_col1:
                            st.write(f"**{i+1}.** {image_files[idx].name}")
                            # 获取文件大小
                            file_size = len(image_files[idx].getvalue())
                            st.caption(f"大小: {format_file_size(file_size)}")

                        with img_col2:
                            if len(image_files) > 1 and st.button("⬆️ 上移", key=f"up_{i}", disabled=(i == 0)):
                                # 交换当前项与上一项
                                st.session_state["image_order"][i], st.session_state["image_order"][i - 1] = (
                                    st.session_state["image_order"][i - 1],
                                    st.session_state["image_order"][i],
                                )
                                st.rerun()

                        with img_col3:
                            if len(image_files) > 1 and st.button(
                                "⬇️ 下移", key=f"down_{i}", disabled=(i == len(st.session_state["image_order"]) - 1)
                            ):
                                # 交换当前项与下一项
                                st.session_state["image_order"][i], st.session_state["image_order"][i + 1] = (
                                    st.session_state["image_order"][i + 1],
                                    st.session_state["image_order"][i],
                                )
                                st.rerun()

                    uploaded_files = image_files
                    file_type = "images"

            with col_right:
                st.subheader("📊 OCR解析结果")

                # 显示图片合并和解析的结果
                if st.session_state.get("parse_results") and image_files:
                    all_results = st.session_state["parse_results"]
                    for i, result in enumerate(all_results):
                        if "合并图片" in result.get("file_name", ""):
                            with st.expander(f"📄 {result['file_name']}", expanded=True):
                                # 显示文件信息
                                st.markdown(f"**文件名**: {result['file_name']}")

                                # Markdown 内容
                                if result.get("markdown"):
                                    st.markdown("### 📝 解析内容")
                                    st.markdown(result["markdown"])
                                else:
                                    st.warning("未能提取到文本内容")

                                # 详细信息（可选展开）
                                if result.get("detail"):
                                    with st.expander("🔍 详细信息 (JSON)", expanded=False):
                                        safe_display_json(result["detail"], "详细信息")
                            break
                else:
                    st.info("👆 请先在左侧上传图片文件，然后点击解析按钮查看OCR结果")

        # 显示上传的文件信息
        if uploaded_files:
            st.markdown("---")
            st.subheader("📋 文件信息")

            all_valid = True
            file_infos = []

            for i, uploaded_file in enumerate(uploaded_files):
                file_info = get_file_info(uploaded_file)
                file_infos.append(file_info)
                display_file_info(file_info, i if len(uploaded_files) > 1 else None)

                if not file_info["is_valid"]:
                    all_valid = False

            # 解析按钮
            st.markdown("---")
            # 检查是否可以解析
            can_parse = False
            button_text = "🚀 开始解析"

            if file_type == "pdf":
                can_parse = st.session_state.get("uploaded_pdf") is not None
            else:
                can_parse = all_valid
                # 图片模式下显示合并按钮文字
                if can_parse:
                    button_text = f"📑 合并{len(uploaded_files)}张图片为PDF并解析"

            if st.button(button_text, disabled=not can_parse, use_container_width=True):
                if can_parse:
                    # 清理之前的合并PDF文件
                    if st.session_state.get("merged_pdf_path"):
                        cleanup_temp_files([st.session_state["merged_pdf_path"]])
                        del st.session_state["merged_pdf_path"]
                        if "merged_pdf_filename" in st.session_state:
                            del st.session_state["merged_pdf_filename"]

                    st.session_state["start_parsing"] = True
                    st.session_state["uploaded_files"] = uploaded_files
                    st.session_state["file_type"] = file_type
                    st.rerun()

    # 右侧：解析结果区域
    with col2:
        st.subheader("📊 解析结果")

        if (
            st.session_state.get("start_parsing")
            and st.session_state.get("uploaded_files")
            and not st.session_state.get("parsing_completed")
        ):
            # 如果解析还未完成，开始解析流程
            uploaded_files = st.session_state["uploaded_files"]
            file_type = st.session_state["file_type"]

            # 初始化客户端
            try:
                tos_client = get_tos_client()
                ocr_handler = get_report_ocr_handler()
            except Exception as e:
                st.error(f"初始化服务失败: {e!s}")
                st.stop()

            # 创建进度条
            progress_bar = st.progress(0)
            status_text = st.empty()

            all_results = []
            saved_files = []

            try:
                # PDF模式使用已上传的文件，图片模式继续原逻辑
                if file_type == "pdf" and st.session_state.get("uploaded_pdf"):
                    uploaded_info = st.session_state["uploaded_pdf"]

                    # 检查是否有缓存的解析结果
                    if st.session_state.get("cached_parse_result"):
                        status_text.text(f"加载缓存的解析结果: {uploaded_info['file_name']}")
                        progress_bar.progress(0.8)

                        result = st.session_state["cached_parse_result"]
                        result["file_name"] = uploaded_info["file_name"]
                        result["file_url"] = uploaded_info["url"]
                        all_results.append(result)

                        progress_bar.progress(1.0)
                    else:
                        # 进行新的解析
                        status_text.text(f"正在解析PDF文件: {uploaded_info['file_name']}")
                        progress_bar.progress(0.5)

                        if uploaded_info["url"] == "#cached":
                            # 本地文件模式，使用本地路径
                            file_path = uploaded_info.get("local_file_path", "")
                            if file_path and os.path.exists(file_path):
                                result = parse_local_pdf(file_path, ocr_handler)
                            else:
                                result = {"success": False, "error": "本地文件路径无效"}
                        else:
                            # URL模式，使用URL解析
                            result = parse_report(uploaded_info["url"], ocr_handler)

                        if result.get("success"):
                            result["file_name"] = uploaded_info["file_name"]
                            result["file_url"] = uploaded_info["url"]
                            all_results.append(result)

                            # 保存解析结果到缓存
                            if st.session_state.get("uploaded_pdf_content"):
                                save_parse_result(
                                    uploaded_info["file_name"], st.session_state["uploaded_pdf_content"], result
                                )
                                st.success("✅ 解析结果已保存到缓存")

                        progress_bar.progress(1.0)
                else:
                    # 图片模式：所有图片都合并为PDF
                    status_text.text("正在合并图片为PDF...")
                    progress_bar.progress(0.1)

                    # 获取图片顺序
                    image_order = st.session_state.get("image_order", list(range(len(uploaded_files))))

                    # 合并图片为PDF
                    merged_pdf_path = merge_images_to_pdf(uploaded_files, image_order)

                    if merged_pdf_path:
                        progress_bar.progress(0.3)
                        status_text.text("合并完成，正在上传PDF...")

                        # 上传合并后的PDF到TOS
                        merged_filename = f"merged_images_{int(time.time())}.pdf"
                        object_key = f"health_reports/{merged_filename}"

                        result = tos_client.upload_file(file_path=merged_pdf_path, object_key=object_key)

                        if result.get("success"):
                            progress_bar.progress(0.5)
                            status_text.text("上传完成，正在解析PDF...")

                            # 获取预签名访问链接
                            pre_signed_url = tos_client.get_object_url(object_key)

                            # 解析合并后的PDF
                            progress_bar.progress(0.7)
                            parse_result = parse_report(pre_signed_url, ocr_handler)

                            if parse_result.get("success"):
                                parse_result["file_name"] = f"合并图片_{len(uploaded_files)}张.pdf"
                                parse_result["file_url"] = pre_signed_url
                                all_results.append(parse_result)

                                # 保存合并后的PDF路径到session state，供下载使用
                                st.session_state["merged_pdf_path"] = merged_pdf_path
                                st.session_state["merged_pdf_filename"] = f"合并图片_{len(uploaded_files)}张.pdf"

                                progress_bar.progress(0.9)
                                status_text.text("PDF解析完成")
                            else:
                                st.error(f"解析合并PDF失败: {parse_result.get('error', '未知错误')}")
                                # 清理临时PDF文件
                                cleanup_temp_files([merged_pdf_path])
                        else:
                            st.error("上传合并PDF失败")
                            # 清理临时PDF文件
                            cleanup_temp_files([merged_pdf_path])
                    else:
                        st.error("合并图片为PDF失败")

                # 完成处理
                status_text.text("解析完成！")
                progress_bar.progress(1.0)

                # 显示结果
                if all_results:
                    st.success(f"✅ 成功解析 {len(all_results)} 个文件")

                    # 如果是从图片合并生成的PDF，显示下载按钮
                    if st.session_state.get("merged_pdf_path") and st.session_state.get("merged_pdf_filename"):
                        st.markdown("---")
                        col1, col2 = st.columns([2, 1])
                        with col1:
                            st.info("📑 此文件由图片合并生成，您可以下载合并后的PDF文件")
                        with col2:
                            # 读取PDF文件内容
                            try:
                                with open(st.session_state["merged_pdf_path"], "rb") as f:
                                    pdf_data = f.read()

                                st.download_button(
                                    label="📥 下载合并PDF",
                                    data=pdf_data,
                                    file_name=st.session_state["merged_pdf_filename"],
                                    mime="application/pdf",
                                    use_container_width=True,
                                    key="download_merged_pdf_initial",
                                )
                            except Exception as e:
                                st.error(f"无法读取PDF文件: {str(e)}")

                    # 显示每个文件的解析结果
                    for i, result in enumerate(all_results):
                        with st.expander(f"📄 {result['file_name']}", expanded=len(all_results) == 1):

                            # 显示文件信息
                            st.markdown(f"**文件名**: {result['file_name']}")
                            with st.expander("🔗 文件URL"):
                                st.code(result["file_url"], language="text")

                            # Markdown 内容
                            if result.get("markdown"):
                                st.markdown("### 📝 解析内容")
                                st.markdown(result["markdown"])
                            else:
                                st.warning("未能提取到文本内容")

                            # 详细信息（可选展开）
                            if result.get("detail"):
                                with st.expander("🔍 详细信息 (JSON)", expanded=False):
                                    safe_display_json(result["detail"], "详细信息")

                else:
                    st.error("❌ 没有成功解析的文件")

            except Exception as e:
                st.error(f"处理过程中出现错误: {e!s}")

            finally:
                # 清理临时文件
                cleanup_temp_files(saved_files)
                # 保存解析结果到session state，避免数据丢失
                if all_results:
                    st.session_state["parse_results"] = all_results
                # 标记解析已完成，但保留start_parsing状态以便AI分析按钮能正常工作
                st.session_state["parsing_completed"] = True
                if "uploaded_files" in st.session_state:
                    del st.session_state["uploaded_files"]
                if "file_type" in st.session_state:
                    del st.session_state["file_type"]
                # 保留uploaded_pdf状态，方便用户再次解析

        elif st.session_state.get("parse_results"):
            # 检查是否有已保存的解析结果 (包括解析完成的情况)
            if True:
                # 显示已保存的解析结果
                all_results = st.session_state["parse_results"]
                st.success(f"✅ 显示已解析的 {len(all_results)} 个文件")

                # 如果是从图片合并生成的PDF，显示下载按钮
                if st.session_state.get("merged_pdf_path") and st.session_state.get("merged_pdf_filename"):
                    st.markdown("---")
                    col1, col2 = st.columns([2, 1])
                    with col1:
                        st.info("📑 此文件由图片合并生成，您可以下载合并后的PDF文件")
                    with col2:
                        # 读取PDF文件内容
                        try:
                            with open(st.session_state["merged_pdf_path"], "rb") as f:
                                pdf_data = f.read()

                            st.download_button(
                                label="📥 下载合并PDF",
                                data=pdf_data,
                                file_name=st.session_state["merged_pdf_filename"],
                                mime="application/pdf",
                                use_container_width=True,
                                key="download_merged_pdf",
                            )
                        except Exception as e:
                            st.error(f"无法读取PDF文件: {str(e)}")

                # 显示每个文件的解析结果
                for i, result in enumerate(all_results):
                    with st.expander(f"📄 {result['file_name']}", expanded=len(all_results) == 1):

                        # 显示文件信息
                        st.markdown(f"**文件名**: {result['file_name']}")
                        if result["file_url"] != "#cached":
                            with st.expander("🔗 文件URL"):
                                st.code(result["file_url"], language="text")

                        # Markdown 内容
                        if result.get("markdown"):
                            st.markdown("### 📝 解析内容")
                            st.markdown(result["markdown"])
                        else:
                            st.warning("未能提取到文本内容")

                        # 详细信息（可选展开）
                        if result.get("detail"):
                            with st.expander("🔍 详细信息 (JSON)", expanded=False):
                                safe_display_json(result["detail"], "详细信息")

            else:
                # 默认显示
                st.info("👆 请先在左侧上传文件，然后点击 开始解析 按钮")

                # 显示使用说明
                st.markdown("### 📖 使用说明")
                st.markdown(
                    """
                1. **PDF 文件**: 上传单个 PDF 文件进行解析
                2. **解析功能**:
                   - 自动提取文本内容
                   - 表格识别和 Markdown 格式化
                   - 结构化数据提取
                """
                )

    # 下半部分：AI智能分析模块（独立的全宽区域）
    render_ai_analysis_section()


# 如果直接运行此文件，显示页面
if __name__ == "__main__":
    health_report_page()
