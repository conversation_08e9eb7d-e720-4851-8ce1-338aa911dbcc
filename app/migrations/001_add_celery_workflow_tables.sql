-- Migration: Add Celery workflow support tables
-- Date: 2024-01-01
-- Description: Add task_steps and task_results tables for enhanced workflow tracking

-- Add new columns to existing report_tasks table
ALTER TABLE report_tasks 
ADD COLUMN workflow_id VARCHAR(100) COMMENT 'Celery工作流ID' AFTER report_key;

-- Add index for workflow_id
CREATE INDEX idx_workflow_id ON report_tasks(workflow_id);

-- Create task_steps table for detailed step tracking
CREATE TABLE task_steps (
    id INT AUTO_INCREMENT PRIMARY KEY,
    report_id VARCHAR(64) NOT NULL COMMENT '报告ID',
    step_name VARCHAR(50) NOT NULL COMMENT '步骤名称',
    status VARCHAR(50) NOT NULL COMMENT '步骤状态',
    celery_task_id VARCHAR(100) COMMENT 'Celery任务ID',
    start_time TIMESTAMP NULL COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    duration INT COMMENT '耗时(秒)',
    result JSON COMMENT '执行结果',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_report_step (report_id, step_name),
    INDEX idx_report_id (report_id),
    INDEX idx_status (status),
    INDEX idx_celery_task_id (celery_task_id),
    FOREIGN KEY (report_id) REFERENCES report_tasks(report_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务步骤状态表';

-- Create task_results table for storing task outputs
CREATE TABLE task_results (
    id INT AUTO_INCREMENT PRIMARY KEY,
    report_id VARCHAR(64) NOT NULL COMMENT '报告ID',
    result_type VARCHAR(50) NOT NULL COMMENT '结果类型',
    result_data JSON NOT NULL COMMENT '结果数据',
    file_path VARCHAR(500) COMMENT '文件路径',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_report_type (report_id, result_type),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (report_id) REFERENCES report_tasks(report_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务结果存储表';

-- Create task_errors table for comprehensive error tracking
CREATE TABLE task_errors (
    id INT AUTO_INCREMENT PRIMARY KEY,
    report_id VARCHAR(64) NOT NULL COMMENT '报告ID',
    task_name VARCHAR(100) NOT NULL COMMENT '任务名称',
    error_type VARCHAR(100) NOT NULL COMMENT '错误类型',
    error_message TEXT NOT NULL COMMENT '错误信息',
    traceback TEXT COMMENT '错误堆栈',
    celery_task_id VARCHAR(100) COMMENT 'Celery任务ID',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_report_id (report_id),
    INDEX idx_task_name (task_name),
    INDEX idx_error_type (error_type),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (report_id) REFERENCES report_tasks(report_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务错误记录表';