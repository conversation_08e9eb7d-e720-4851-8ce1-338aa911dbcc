-- Rollback: Remove Celery workflow support tables
-- Date: 2024-01-01
-- Description: Rollback migration 001 - Remove task_steps, task_results, and task_errors tables

-- Drop new tables (in reverse order due to foreign key constraints)
DROP TABLE IF EXISTS task_errors;
DROP TABLE IF EXISTS task_results;
DROP TABLE IF EXISTS task_steps;

-- Remove new columns from report_tasks table
ALTER TABLE report_tasks DROP INDEX idx_workflow_id;
ALTER TABLE report_tasks DROP COLUMN workflow_id;