"""
数据库迁移运行器
"""

import logging
import os
from pathlib import Path
from typing import List

import pymysql
from app.core.config import settings

logger = logging.getLogger(__name__)


class MigrationRunner:
    """数据库迁移运行器"""
    
    def __init__(self):
        self.migrations_dir = Path(__file__).parent
        self.connection_params = {
            'host': settings.MYSQL_HOST,
            'port': settings.MYSQL_PORT,
            'user': settings.MYSQL_USER,
            'password': settings.MYSQL_PASSWORD,
            'database': settings.MYSQL_DATABASE,
            'charset': settings.MYSQL_CHARSET,
            'autocommit': False
        }
    
    def get_connection(self):
        """获取数据库连接"""
        return pymysql.connect(**self.connection_params)
    
    def create_migration_table(self):
        """创建迁移记录表"""
        sql = """
        CREATE TABLE IF NOT EXISTS schema_migrations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            migration_name VARCHAR(255) NOT NULL UNIQUE,
            applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_migration_name (migration_name)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据库迁移记录表'
        """
        
        with self.get_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(sql)
                conn.commit()
                logger.info("Migration tracking table created/verified")
    
    def get_applied_migrations(self) -> List[str]:
        """获取已应用的迁移列表"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT migration_name FROM schema_migrations ORDER BY applied_at")
                    return [row[0] for row in cursor.fetchall()]
        except pymysql.Error as e:
            if "doesn't exist" in str(e):
                return []
            raise
    
    def get_available_migrations(self) -> List[str]:
        """获取可用的迁移文件列表"""
        migration_files = []
        for file_path in self.migrations_dir.glob("*.sql"):
            if not file_path.name.endswith("_rollback.sql"):
                migration_files.append(file_path.stem)
        
        return sorted(migration_files)
    
    def apply_migration(self, migration_name: str):
        """应用单个迁移"""
        migration_file = self.migrations_dir / f"{migration_name}.sql"
        
        if not migration_file.exists():
            raise FileNotFoundError(f"Migration file not found: {migration_file}")
        
        logger.info(f"Applying migration: {migration_name}")
        
        with self.get_connection() as conn:
            try:
                with conn.cursor() as cursor:
                    # 读取并执行迁移SQL
                    sql_content = migration_file.read_text(encoding='utf-8')
                    
                    # 分割SQL语句（简单的分割，可能需要更复杂的解析）
                    statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
                    
                    for statement in statements:
                        if statement:
                            cursor.execute(statement)
                    
                    # 记录迁移已应用
                    cursor.execute(
                        "INSERT INTO schema_migrations (migration_name) VALUES (%s)",
                        (migration_name,)
                    )
                    
                    conn.commit()
                    logger.info(f"Migration {migration_name} applied successfully")
                    
            except Exception as e:
                conn.rollback()
                logger.error(f"Failed to apply migration {migration_name}: {e}")
                raise
    
    def rollback_migration(self, migration_name: str):
        """回滚单个迁移"""
        rollback_file = self.migrations_dir / f"{migration_name}_rollback.sql"
        
        if not rollback_file.exists():
            raise FileNotFoundError(f"Rollback file not found: {rollback_file}")
        
        logger.info(f"Rolling back migration: {migration_name}")
        
        with self.get_connection() as conn:
            try:
                with conn.cursor() as cursor:
                    # 读取并执行回滚SQL
                    sql_content = rollback_file.read_text(encoding='utf-8')
                    
                    statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
                    
                    for statement in statements:
                        if statement:
                            cursor.execute(statement)
                    
                    # 删除迁移记录
                    cursor.execute(
                        "DELETE FROM schema_migrations WHERE migration_name = %s",
                        (migration_name,)
                    )
                    
                    conn.commit()
                    logger.info(f"Migration {migration_name} rolled back successfully")
                    
            except Exception as e:
                conn.rollback()
                logger.error(f"Failed to rollback migration {migration_name}: {e}")
                raise
    
    def migrate(self):
        """应用所有待处理的迁移"""
        self.create_migration_table()
        
        applied_migrations = set(self.get_applied_migrations())
        available_migrations = self.get_available_migrations()
        
        pending_migrations = [m for m in available_migrations if m not in applied_migrations]
        
        if not pending_migrations:
            logger.info("No pending migrations")
            return
        
        logger.info(f"Found {len(pending_migrations)} pending migrations")
        
        for migration in pending_migrations:
            self.apply_migration(migration)
        
        logger.info("All migrations applied successfully")
    
    def status(self):
        """显示迁移状态"""
        try:
            self.create_migration_table()
            applied_migrations = set(self.get_applied_migrations())
            available_migrations = self.get_available_migrations()
            
            print("Migration Status:")
            print("================")
            
            for migration in available_migrations:
                status = "✓ Applied" if migration in applied_migrations else "✗ Pending"
                print(f"{migration}: {status}")
            
            pending_count = len([m for m in available_migrations if m not in applied_migrations])
            print(f"\nTotal: {len(available_migrations)} migrations, {pending_count} pending")
            
        except Exception as e:
            logger.error(f"Failed to get migration status: {e}")
            raise


def main():
    """命令行入口"""
    import sys
    
    runner = MigrationRunner()
    
    if len(sys.argv) < 2:
        print("Usage: python migration_runner.py [migrate|status|rollback <migration_name>]")
        return
    
    command = sys.argv[1]
    
    try:
        if command == "migrate":
            runner.migrate()
        elif command == "status":
            runner.status()
        elif command == "rollback" and len(sys.argv) > 2:
            migration_name = sys.argv[2]
            runner.rollback_migration(migration_name)
        else:
            print("Invalid command or missing arguments")
            print("Usage: python migration_runner.py [migrate|status|rollback <migration_name>]")
    
    except Exception as e:
        logger.error(f"Migration command failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()