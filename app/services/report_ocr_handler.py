"""PDF/图片 体检报告解析服务"""

import asyncio
import base64
import json
from pathlib import Path
from typing import Any
import uuid

import requests
import structlog
from volcengine.visual.VisualService import VisualService

from app.client.tos_client import get_tos_client
from app.core.config import Settings
from app.utils.parse_pdf import remove_markdown_images

logger = structlog.get_logger()


class ReportOCRHandler:
    """PDF/图片 体检报告解析处理器"""

    # 支持的图片格式
    SUPPORTED_IMAGE_FORMATS = {".jpg", ".jpeg", ".png", ".bmp"}

    def __init__(self, settings: Settings):
        self.settings = settings
        self.service = VisualService()
        # 设置访问密钥和区域
        self.service.set_ak(settings.VOLCENGINE_ACCESS_KEY)
        self.service.set_sk(settings.VOLCENGINE_SECRET_KEY)
        self.tos_client = get_tos_client()

    def _get_file_type(self, file_path: str) -> str:
        """根据文件路径或URL后缀判断文件类型

        Args:
            file_path: 文件路径或URL

        Returns:
            文件类型: "pdf" 或 "image"

        Raises:
            ValueError: 当文件格式不支持时抛出
        """
        # 处理URL中的查询参数
        file_path = file_path.split("?")[0]

        suffix = Path(file_path).suffix.lower()
        if suffix == ".pdf":
            return "pdf"
        elif suffix in self.SUPPORTED_IMAGE_FORMATS:
            return "image"
        else:
            raise ValueError(f"不支持的文件格式: {suffix}")

    async def parse_pdf_file(self, file_path: str, page_start: int = 0, page_num: int = 50) -> dict[str, Any]:
        """解析本地 PDF 或图片文件

        Args:
            file_path: PDF 或图片文件路径
            page_start: 起始页码，从 0 开始（仅 PDF 有效）
            page_num: 解析页数（仅 PDF 有效）

        Returns:
            解析结果
        """
        try:
            # 识别文件类型
            file_type = self._get_file_type(file_path)

            # 读取文件并转换为 base64
            with open(file_path, "rb") as f:
                file_content = f.read()

            # 检查文件大小 (200MB 限制)
            if len(file_content) > 200 * 1024 * 1024:
                raise ValueError("文件大小超过 200MB 限制")

            file_base64 = base64.b64encode(file_content).decode("utf-8")

            return await self._parse_with_base64(file_base64, page_start, page_num, file_type)

        except Exception as e:
            logger.error("文件解析失败", file_path=file_path, error=str(e))
            raise

    async def parse_pdf_url(self, file_url: str, page_start: int = 0, page_num: int = 50) -> dict[str, Any]:
        """解析在线 PDF 或图片文件

        Args:
            file_url: PDF 或图片文件 URL
            page_start: 起始页码，从 0 开始（仅 PDF 有效）
            page_num: 解析页数（仅 PDF 有效）

        Returns:
            解析结果
        """
        try:
            # 根据 URL 后缀判断文件类型
            file_type = self._get_file_type(file_url)
            return await self._parse_with_url(file_url, page_start, page_num, file_type)
        except Exception as e:
            logger.error("URL 解析失败", file_url=file_url, error=str(e))
            raise

    async def _parse_with_base64(
        self, file_base64: str, page_start: int, page_num: int, file_type: str = "pdf"
    ) -> dict[str, Any]:
        """使用 base64 编码内容解析"""
        form = {
            "image_base64": file_base64,
            "image_url": "",
            "version": "v3",
            "page_start": str(page_start),
            "page_num": str(page_num),
            "table_mode": "markdown",
            "parse_mode": "ocr",
            # "filter_header": "true",
            "file_type": file_type,
        }

        try:
            response = self.service.ocr_pdf(form)
            return await self._process_response(response)
        except Exception as e:
            logger.error(f"火山引擎解析 API 调用失败, error={e}")
            raise

    async def _parse_with_url(
        self, file_url: str, page_start: int, page_num: int, file_type: str = "pdf"
    ) -> dict[str, Any]:
        """使用 URL 解析"""
        form = {
            "image_base64": "",
            "image_url": file_url,
            "version": "v3",
            "page_start": str(page_start),
            "page_num": str(page_num),
            "table_mode": "markdown",
            # "filter_header": "true",
            "file_type": file_type,
        }

        task = {"url": file_url, "id": str(uuid.uuid4()), "type": file_type}

        try:
            response = self.service.ocr_pdf(form)
            return await self._process_response(response, task)
        except Exception as e:
            logger.error("火山引擎 URL 解析 API 调用失败", error=str(e))
            raise

    async def _download_and_upload_image(self, url: str, image_id: str) -> str:
        """下载图片并上传到 TOS

        Args:
            url: 图片URL
            image_id: 图片ID，用于生成object_key

        Returns:
            TOS object_key
        """
        try:
            # 下载图片
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(None, lambda: requests.get(url, timeout=30))
            if response.status_code != 200:
                logger.error(f"下载图片失败: {url}, status: {response.status_code}")
                return ""

            image_data = response.content
            content_type = response.headers.get("content-type", "image/png")

            # 生成唯一的object_key
            file_extension = Path(url).suffix or ".png"
            object_key = f"report_images/{image_id}_{uuid.uuid4().hex}{file_extension}"

            # 上传到 TOS
            tos_client = get_tos_client()
            upload_result = await tos_client.async_upload_bytes(
                content=image_data, object_key=object_key, content_type=content_type
            )

            if upload_result.get("success"):
                logger.info(f"图片上传成功: {object_key}")
                return object_key
            else:
                logger.error(f"图片上传失败: {object_key}")
                return ""

        except Exception as e:
            logger.error(f"下载或上传图片失败: {url}, error: {e!s}")
            return ""

    async def _upload_results_to_tos(
        self, report_id: str, markdown_content: str, detail_data: Any, pretty_data: Any
    ) -> dict[str, str]:
        """将解析结果上传到TOS

        Args:
            report_id: 报告唯一标识
            markdown_content: markdown格式内容
            detail_data: 原始detail数据
            pretty_data: 清洗后的pretty数据

        Returns:
            包含三个文件TOS key的字典
        """
        try:
            # 生成TOS key路径
            base_path = f"report/{report_id}"
            markdown_key = f"{base_path}/result.md"
            detail_key = f"{base_path}/detail.json"
            pretty_key = f"{base_path}/pretty.json"

            # 上传markdown文件
            await self.tos_client.async_upload_string(
                content=markdown_content, object_key=markdown_key, content_type="text/markdown"
            )

            # 上传detail JSON文件
            detail_json = json.dumps(detail_data, ensure_ascii=False, indent=2)
            await self.tos_client.async_upload_string(
                content=detail_json, object_key=detail_key, content_type="application/json"
            )

            # 上传pretty JSON文件
            pretty_json = json.dumps(pretty_data, ensure_ascii=False, indent=2)
            await self.tos_client.async_upload_string(
                content=pretty_json, object_key=pretty_key, content_type="application/json"
            )

            logger.info(
                "报告解析结果已上传到TOS",
                report_id=report_id,
                markdown_key=markdown_key,
                detail_key=detail_key,
                pretty_key=pretty_key,
            )

            return {"markdown_key": markdown_key, "detail_key": detail_key, "pretty_key": pretty_key}

        except Exception as e:
            logger.error("上传解析结果到TOS失败", report_id=report_id, error=str(e))
            raise

    async def _process_response(self, response: dict[str, Any], task: dict[str, Any]) -> dict[str, Any]:
        """处理 API 响应"""
        if not response.get("data"):
            logger.error(f"task: {task}, error: response: {response}")
            raise ValueError("API 调用失败: 响应数据为空")

        data = response.get("data", {})
        report_id = task.get("id")

        # 基础结果
        result = {
            "success": True,
            "report_id": report_id,
            "markdown": data.get("markdown", ""),
            "detail": None,
            "pretty": None,
            "tos_keys": {},
        }

        detail_str = data.get("detail", "")
        self._parse_and_process_detail(report_id, detail_str, result)

        # 上传到TOS
        try:
            tos_keys = await self._upload_results_to_tos(
                report_id=report_id,
                markdown_content=result["markdown"],
                detail_data=result["detail"],
                pretty_data=result["pretty"],
            )
            result["tos_keys"] = tos_keys
        except Exception as e:
            logger.error("处理结果上传到TOS失败", report_id=report_id, error=str(e))
            result["tos_upload_error"] = str(e)

        logger.info(
            f"PDF 解析结果 - 解析成功: {result['success']}, "
            f"报告ID: {report_id}, Markdown 内容长度: {len(result['markdown'])}"
        )

        return result

    def _parse_and_process_detail(self, report_id: str, detail_str: str, result: dict[str, Any]):
        """解析 detail JSON 字符串并处理其内容。"""
        if not detail_str:
            result["detail"] = None
            result["pretty"] = None
            return

        try:
            detail_data = json.loads(detail_str)
            result["detail"] = detail_data

            if isinstance(detail_data, list):
                processed_detail = []
                for item in detail_data:
                    processed_item = self._process_page_item(report_id, item)
                    if processed_item:
                        processed_detail.append(processed_item)

                if processed_detail:
                    result["pretty"] = processed_detail
                else:
                    result["pretty"] = None
            else:
                result["pretty"] = None

        except json.JSONDecodeError as e:
            logger.error("解析详细信息JSON失败", error=str(e))
            result["detail"] = detail_str
            result["pretty"] = None
            result["success"] = False

    def _process_page_item(self, report_id: str, item: dict[str, Any]) -> dict[str, Any] | None:
        """处理 detail 数据中的单个页面项目。"""
        if not (isinstance(item, dict) and "page_id" in item and "page_md" in item):
            return None

        processed_item = {"page_id": item["page_id"], "page_md": remove_markdown_images(item["page_md"])}

        if "textblocks" in item and isinstance(item["textblocks"], list):
            processed_item["textblocks"] = [self._handle_image_textblock(report_id, tb) for tb in item["textblocks"]]

        return processed_item

    def _handle_image_textblock(self, report_id: str, textblock: dict[str, Any]) -> dict[str, Any]:
        """处理单个 textblock，如果 textblock 是图片则下载并上传。"""
        processed_textblock = textblock.copy()
        if not (isinstance(textblock, dict) and textblock.get("label") == "image" and "url" in textblock):
            return processed_textblock

        url = textblock["url"]
        try:
            response = requests.get(url, timeout=30)
            if response.status_code != 200:
                logger.error(f"下载图片失败: {url}, status: {response.status_code}")
                return processed_textblock

            image_data = response.content
            content_type = response.headers.get("content-type", "image/png")

            image_id = textblock.get("image_id", "unknown")
            # 过滤URL中的查询参数
            url_without_query = url.split("?")[0]
            file_extension = Path(url_without_query).suffix or ".png"
            object_key = f"report/{report_id}/images/{image_id}_{uuid.uuid4().hex}{file_extension}"

            tos_client = get_tos_client()
            upload_result = tos_client.upload_bytes(
                content=image_data, object_key=object_key, content_type=content_type
            )

            if upload_result.get("success"):
                processed_textblock["object_key"] = object_key
                logger.info(f"图片上传成功: {object_key}")
            else:
                logger.error(f"图片上传失败: {object_key}")

        except Exception as e:
            logger.error(f"下载或上传图片失败: {url}, error: {e!s}")

        return processed_textblock


def get_report_ocr_handler() -> ReportOCRHandler:
    """获取 PDF 处理器实例"""
    from app.core.config import settings

    return ReportOCRHandler(settings)
