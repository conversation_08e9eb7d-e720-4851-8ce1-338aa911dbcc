"""
语音转文本服务
调用火山引擎STT API进行语音转文本

需要开通 豆包语音-语音识别大模型-录音文件识别大模型-极速版

docs: https://www.volcengine.com/docs/6561/1631584
"""

import base64
from typing import BinaryIO
import uuid

import aiofiles
from fastapi import HTTPException
import httpx

from app.core.config import settings
from app.schemas.stt import STTResponse
from app.utils.logger import get_logger

logger = get_logger(__name__)


class STTService:
    """火山引擎语音转文本服务"""

    def __init__(self):
        self.app_id = settings.VOLCENGINE_STT_APP_ID
        self.access_token = settings.VOLCENGINE_STT_ACCESS_TOKEN
        self.endpoint = settings.VOLCENGINE_STT_ENDPOINT
        self.resource_id = settings.VOLCENGINE_STT_RESOURCE_ID
        self.model_name = settings.VOLCENGINE_STT_MODEL_NAME

        if not self.app_id or not self.access_token:
            raise ValueError("STT 配置不完整：缺少 APP_ID 或 ACCESS_TOKEN")

    async def recognize_audio(self, audio_file: BinaryIO, filename: str) -> STTResponse:
        """
        识别音频文件并返回文本

        Args:
            audio_file: 音频文件对象
            filename: 文件名

        Returns:
            STTResponse: 识别结果
        """
        try:
            # 读取音频文件内容
            audio_content = await self._read_audio_file(audio_file)

            # 验证文件大小（30MB限制）
            if len(audio_content) > 30 * 1024 * 1024:
                raise HTTPException(status_code=400, detail="音频文件大小不能超过30MB")

            # 转换为base64
            audio_base64 = base64.b64encode(audio_content).decode("utf-8")

            # 调用火山引擎STT API
            result = await self._call_stt_api(audio_base64)

            # 解析响应
            return self._parse_response(result)

        except HTTPException:
            raise
        except Exception as e:
            logger.error("STT识别失败", error=str(e), filename=filename)
            raise HTTPException(status_code=500, detail=f"音频识别失败: {e!s}")

    async def _read_audio_file(self, audio_file: BinaryIO) -> bytes:
        """读取音频文件内容"""
        try:
            # 检查是否有有效的文件路径
            if hasattr(audio_file, "name") and audio_file.name and isinstance(audio_file.name, str):
                async with aiofiles.open(audio_file.name, "rb") as f:
                    content = await f.read()
                return content
            else:
                # 对于内存中的文件对象（如 FastAPI 上传文件），使用同步方式
                # 重置文件指针到开始位置
                audio_file.seek(0)
                content = audio_file.read()
                return content
        except Exception as e:
            logger.error("读取音频文件失败", error=str(e))
            raise HTTPException(status_code=400, detail="读取音频文件失败")

    async def _call_stt_api(self, audio_base64: str) -> dict:
        """调用火山引擎STT API"""
        # 生成请求ID
        request_id = str(uuid.uuid4())

        # 构建请求头
        headers = {
            "X-Api-App-Key": self.app_id,
            "X-Api-Access-Key": self.access_token,
            "X-Api-Resource-Id": self.resource_id,
            "X-Api-Request-Id": request_id,
            "Content-Type": "application/json",
        }

        # 构建请求体
        payload = {"model_name": self.model_name, "audio": {"data": audio_base64}}

        logger.info("调用STT API", endpoint=self.endpoint, request_id=request_id, model_name=self.model_name)
        logger.info(headers)

        # 发送请求
        async with httpx.AsyncClient(timeout=60.0) as client:
            try:
                response = await client.post(self.endpoint, headers=headers, json=payload)

                if response.status_code != 200:
                    error_msg = f"STT API调用失败: HTTP {response.status_code}"
                    logger.error(error_msg, response_text=response.text)
                    raise HTTPException(status_code=500, detail=error_msg)

                result = response.json()
                logger.info("STT API调用成功", request_id=request_id)
                return result

            except httpx.TimeoutException:
                logger.error("STT API调用超时", request_id=request_id)
                raise HTTPException(status_code=500, detail="音频识别超时")
            except httpx.RequestError as e:
                logger.error("STT API调用网络错误", error=str(e), request_id=request_id)
                raise HTTPException(status_code=500, detail="网络请求失败")

    def _parse_response(self, result: dict) -> STTResponse:
        """解析STT API响应"""
        try:
            # 火山引擎返回格式: {"audio_info": {"duration": xxx}, "result": {"text": "xxx", "utterances": [...]}}
            audio_info = result.get("audio_info", {})
            api_result = result.get("result", {})

            # 获取音频时长（毫秒转秒）
            duration_ms = audio_info.get("duration", 0)
            duration = duration_ms / 1000.0 if duration_ms else 0.0

            # 获取识别文本
            text = api_result.get("text", "")

            return STTResponse(text=text.strip(), duration=duration)

        except Exception as e:
            logger.error("解析STT响应失败", error=str(e), result=result)
            raise HTTPException(status_code=500, detail="解析识别结果失败")


# 全局STT服务实例
stt_service = STTService()
