"""
报告任务处理器 - 优化版本
负责执行报告解析的具体步骤，包含OCR缓存优化、超时控制等改进
"""

import asyncio
from contextlib import asynccontextmanager
from datetime import datetime
import json
from pathlib import Path
import threading
import time

from cachetools import TTL<PERSON>ache
from sqlalchemy.orm import Session
from structlog import get_logger

from app.agents.report_agent import AgentType, ReportAnalysisWorkflow
from app.client.tos_client import get_tos_client
from app.core.config import settings
from app.models import LogStatus, ReportTask, ReportTaskLog, TaskStatus, get_db
from app.services.report_ocr_handler import get_report_ocr_handler

logger = get_logger(__name__)


class ReportTaskProcessor:
    """报告任务处理器"""

    # 处理步骤定义
    STEPS = [
        ("ocr_processing", "OCR解析", TaskStatus.OCR_PROCESSING, TaskStatus.OCR_COMPLETED),
        ("meta_processing", "个人信息提取", TaskStatus.META_PROCESSING, TaskStatus.META_COMPLETED),
        ("overall_processing", "总体结果分析", TaskStatus.OVERALL_PROCESSING, TaskStatus.OVERALL_COMPLETED),
        ("tabular_processing", "检查项指标分析", TaskStatus.TABULAR_PROCESSING, TaskStatus.TABULAR_COMPLETED),
        ("image_processing", "影像数据分析", TaskStatus.IMAGE_PROCESSING, TaskStatus.IMAGE_COMPLETED),
        ("writing_json", "写入JSON文件", TaskStatus.WRITING_JSON, TaskStatus.COMPLETED),
    ]

    def __init__(self):
        self.tos_client = get_tos_client()
        self.ocr_handler = get_report_ocr_handler()
        self.workflow = None
        self._ocr_cache = TTLCache(maxsize=settings.OCR_CACHE_SIZE, ttl=settings.OCR_CACHE_EXPIRE_MINUTES * 60)

    @asynccontextmanager
    async def get_db_session(self):
        """安全的数据库会话管理"""
        db = next(get_db())
        try:
            yield db
        finally:
            db.close()

    async def process_task(self, report_id: str) -> bool:
        """处理单个任务"""

        async with self.get_db_session() as db:
            return await self._process_task_impl(report_id, db)

    async def _process_task_impl(self, report_id: str, db: Session) -> bool:
        """处理任务的具体实现"""
        try:
            task = db.query(ReportTask).filter(ReportTask.report_id == report_id).first()

            if not task:
                logger.error("任务不存在", report_id=report_id)
                return False

            if task.status == TaskStatus.COMPLETED:
                logger.info("任务已完成", report_id=report_id)
                return True

            # 更新任务开始时间
            if not task.started_at:
                task.started_at = datetime.utcnow()
                db.commit()

            # 从当前步骤开始执行
            start_step = task.current_step

            for step_index in range(start_step, len(self.STEPS)):
                success = await self._execute_step(task, step_index, db)
                if not success:
                    # 步骤失败，停止处理
                    return False

            # 所有步骤完成，标记任务完成
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.utcnow()
            task.progress_percent = 100.00
            db.commit()

            await self._log_step(task.report_id, "任务完成", LogStatus.COMPLETED, "所有步骤成功完成")

            logger.info("任务处理完成", report_id=report_id)
            return True

        except Exception as e:
            logger.error("处理任务失败", report_id=report_id, error=str(e))
            await self._handle_task_error(report_id, str(e), db)
            return False

    async def _execute_step(self, task: ReportTask, step_index: int, db: Session) -> bool:
        """执行单个步骤"""

        if step_index >= len(self.STEPS):
            logger.error("步骤索引超出范围", step_index=step_index)
            return False

        step_method, step_name, processing_status, completed_status = self.STEPS[step_index]

        logger.info("开始执行步骤", report_id=task.report_id, step=step_name, step_index=step_index)

        # 记录步骤开始
        await self._log_step(task.report_id, step_name, LogStatus.STARTED)

        # 更新任务状态
        task.status = processing_status
        task.current_step = step_index
        task.progress_percent = (step_index / len(self.STEPS)) * 100
        task.updated_at = datetime.utcnow()
        db.commit()

        start_time = time.time()

        try:
            # 执行具体步骤
            if step_method == "ocr_processing":
                success = await self._execute_ocr_step(task, db)
            elif step_method == "meta_processing":
                success = await self._execute_meta_step(task, db)
            elif step_method == "overall_processing":
                success = await self._execute_overall_step(task, db)
            elif step_method == "tabular_processing":
                success = await self._execute_tabular_step(task, db)
            elif step_method == "image_processing":
                success = await self._execute_image_step(task, db)
            elif step_method == "writing_json":
                success = await self._execute_json_step(task, db)
            else:
                logger.error("未知步骤方法", method=step_method)
                success = False

            execution_time = int((time.time() - start_time) * 1000)

            if success:
                # 步骤成功
                task.status = completed_status
                task.updated_at = datetime.utcnow()
                db.commit()

                await self._log_step(
                    task.report_id, step_name, LogStatus.COMPLETED, f"步骤完成，耗时 {execution_time}ms", execution_time
                )

                logger.info("步骤执行成功", report_id=task.report_id, step=step_name, time_ms=execution_time)
                return True
            else:
                # 步骤失败
                error_msg = f"步骤 {step_name} 执行失败"
                await self._handle_step_error(task, step_name, error_msg, execution_time, db)
                return False

        except Exception as e:
            execution_time = int((time.time() - start_time) * 1000)
            error_msg = f"步骤 {step_name} 执行异常: {e!s}"
            await self._handle_step_error(task, step_name, error_msg, execution_time, db)
            return False

    async def _execute_ocr_step(self, task: ReportTask, db: Session) -> bool:
        """执行OCR解析步骤"""

        try:
            # 检查缓存中是否已有OCR结果
            cached_result = self._ocr_cache.get(task.report_id)
            if cached_result:
                logger.info("使用缓存的OCR结果", report_id=task.report_id)
                return True

            # 通过reportKey从TOS获取预签名访问链接
            pre_signed_url = self.tos_client.get_object_url(task.report_key)

            logger.info("获取预签名URL成功", report_id=task.report_id, url=pre_signed_url[:100] + "...")

            # 添加超时控制的OCR处理
            ocr_timeout = getattr(settings, "OCR_TIMEOUT_SECONDS", 300)  # 默认5分钟超时

            try:
                ocr_result = await asyncio.wait_for(
                    self.ocr_handler.parse_pdf_url(file_url=pre_signed_url, page_start=0, page_num=50),
                    timeout=ocr_timeout,
                )
            except TimeoutError:
                error_msg = f"OCR处理超时，超过{ocr_timeout}秒"
                task.ocr_error = error_msg
                db.commit()
                logger.error("OCR处理超时", report_id=task.report_id, timeout=ocr_timeout)
                return False

            if not ocr_result.get("success"):
                task.ocr_error = "OCR处理失败"
                db.commit()
                return False

            # 保存OCR结果到TOS (使用现有的上传逻辑)
            if ocr_result.get("tos_keys"):
                task.ocr_result_key = ocr_result["tos_keys"].get("markdown_key", "")

            db.commit()

            # 将OCR结果保存到任务中，供后续步骤使用
            self._save_ocr_result(task.report_id, ocr_result)

            logger.info("OCR处理完成", report_id=task.report_id)
            return True

        except Exception as e:
            task.ocr_error = str(e)
            db.commit()
            logger.error("OCR处理失败", report_id=task.report_id, error=str(e))
            return False

    async def _execute_meta_step(self, task: ReportTask, db: Session) -> bool:
        """执行个人信息提取步骤"""

        try:
            # 获取OCR结果
            ocr_result = self._get_ocr_result(task.report_id)
            if not ocr_result:
                return False

            # 准备输入文本（取前4页的数据）
            input_text = self._prepare_input_for_agent(ocr_result, "meta_extractor")

            # 创建工作流实例
            workflow = ReportAnalysisWorkflow(
                user_id=task.report_id, session_id=f"meta_{task.report_id}", debug_mode=False
            )

            # 添加超时控制的个人信息提取
            agent_timeout = getattr(settings, "AGENT_TIMEOUT_SECONDS", 180)  # 默认3分钟超时

            try:
                result = await asyncio.wait_for(
                    asyncio.get_event_loop().run_in_executor(
                        None,
                        lambda: workflow.run_single_agent(agent_type=AgentType.META_EXTRACTOR, input_text=input_text),
                    ),
                    timeout=agent_timeout,
                )
            except TimeoutError:
                logger.error("个人信息提取超时", report_id=task.report_id, timeout=agent_timeout)
                return False

            # 保存结果
            task.meta_result = result.content
            db.commit()

            logger.info("个人信息提取完成", report_id=task.report_id)
            return True

        except Exception as e:
            logger.error("个人信息提取失败", report_id=task.report_id, error=str(e))
            return False

    async def _execute_overall_step(self, task: ReportTask, db: Session) -> bool:
        """执行总体结果分析步骤"""

        try:
            # 获取OCR结果
            ocr_result = self._get_ocr_result(task.report_id)
            if not ocr_result:
                return False

            # 准备输入文本（使用所有页面数据）
            input_text = self._prepare_input_for_agent(ocr_result, "overall_result")

            # 创建工作流实例
            workflow = ReportAnalysisWorkflow(
                user_id=task.report_id, session_id=f"overall_{task.report_id}", debug_mode=False
            )

            # 执行总体结果分析
            result = workflow.run_single_agent(agent_type=AgentType.OVERALL_RESULT, input_text=input_text)

            # 保存结果
            task.overall_result = result.content
            db.commit()

            logger.info("总体结果分析完成", report_id=task.report_id)
            return True

        except Exception as e:
            logger.error("总体结果分析失败", report_id=task.report_id, error=str(e))
            return False

    async def _execute_tabular_step(self, task: ReportTask, db: Session) -> bool:
        """执行检查项指标分析步骤"""

        try:
            # 获取OCR结果
            ocr_result = self._get_ocr_result(task.report_id)
            if not ocr_result:
                return False

            # 准备输入文本（使用所有页面数据）
            input_text = self._prepare_input_for_agent(ocr_result, "tabular_parser")

            # 创建工作流实例
            workflow = ReportAnalysisWorkflow(
                user_id=task.report_id, session_id=f"tabular_{task.report_id}", debug_mode=False
            )

            # 执行检查项指标分析
            result = workflow.run_single_agent(agent_type=AgentType.TABULAR_PARSER, input_text=input_text)

            # 保存结果
            task.tabular_result = result.content
            db.commit()

            logger.info("检查项指标分析完成", report_id=task.report_id)
            return True

        except Exception as e:
            logger.error("检查项指标分析失败", report_id=task.report_id, error=str(e))
            return False

    async def _execute_image_step(self, task: ReportTask, db: Session) -> bool:
        """执行影像数据分析步骤"""

        try:
            # 获取OCR结果
            ocr_result = self._get_ocr_result(task.report_id)
            if not ocr_result:
                return False

            # 准备输入文本（使用detail全量数据，包含图片信息）
            input_text = self._prepare_input_for_agent(ocr_result, "img_parser")

            # 创建工作流实例
            workflow = ReportAnalysisWorkflow(
                user_id=task.report_id, session_id=f"image_{task.report_id}", debug_mode=False
            )

            # 执行影像数据分析
            result = workflow.run_single_agent(agent_type=AgentType.MEDICAL_IMAGING_PARSER, input_text=input_text)

            # 保存结果
            task.image_result = result.content
            db.commit()

            logger.info("影像数据分析完成", report_id=task.report_id)
            return True

        except Exception as e:
            logger.error("影像数据分析失败", report_id=task.report_id, error=str(e))
            return False

    async def _execute_json_step(self, task: ReportTask, db: Session) -> bool:
        """执行JSON文件写入步骤"""

        try:
            # 获取OCR结果
            ocr_result = self._get_ocr_result(task.report_id)

            # 构建最终结果
            final_result = {
                "report_id": task.report_id,
                "processing_info": {
                    "created_at": task.created_at.isoformat() if task.created_at else None,
                    "started_at": task.started_at.isoformat() if task.started_at else None,
                    "completed_at": datetime.utcnow().isoformat(),
                    "processing_time_seconds": (
                        int((datetime.utcnow() - task.started_at).total_seconds()) if task.started_at else 0
                    ),
                },
                "ocr_result": ocr_result or {},
                "analysis_results": {
                    "personal_info": json.loads(task.meta_result) if task.meta_result else {},
                    "overall_result": json.loads(task.overall_result) if task.overall_result else {},
                    "tabular_data": json.loads(task.tabular_result) if task.tabular_result else {},
                    "imaging_data": json.loads(task.image_result) if task.image_result else {},
                },
            }

            # 创建输出目录
            date_str = datetime.utcnow().strftime("%Y%m%d")
            output_dir = Path(f"/data/reports/{date_str}/{task.report_id}")
            output_dir.mkdir(parents=True, exist_ok=True)

            # 写入最终合并结果
            final_json_path = output_dir / "final_result.json"
            with open(final_json_path, "w", encoding="utf-8") as f:
                json.dump(final_result, f, ensure_ascii=False, indent=2)

            # 写入各个子结果文件
            if ocr_result:
                ocr_path = output_dir / "ocr_result.json"
                with open(ocr_path, "w", encoding="utf-8") as f:
                    json.dump(ocr_result, f, ensure_ascii=False, indent=2)

            if task.meta_result:
                meta_path = output_dir / "personal_info.json"
                with open(meta_path, "w", encoding="utf-8") as f:
                    json.dump(json.loads(task.meta_result), f, ensure_ascii=False, indent=2)

            if task.overall_result:
                overall_path = output_dir / "overall_result.json"
                with open(overall_path, "w", encoding="utf-8") as f:
                    json.dump(json.loads(task.overall_result), f, ensure_ascii=False, indent=2)

            if task.tabular_result:
                tabular_path = output_dir / "tabular_data.json"
                with open(tabular_path, "w", encoding="utf-8") as f:
                    json.dump(json.loads(task.tabular_result), f, ensure_ascii=False, indent=2)

            if task.image_result:
                image_path = output_dir / "imaging_data.json"
                with open(image_path, "w", encoding="utf-8") as f:
                    json.dump(json.loads(task.image_result), f, ensure_ascii=False, indent=2)

            # 更新任务记录
            task.final_json_path = str(final_json_path)
            db.commit()

            logger.info("JSON文件写入完成", report_id=task.report_id, path=str(final_json_path))
            return True

        except Exception as e:
            logger.error("JSON文件写入失败", report_id=task.report_id, error=str(e))
            return False

    def _prepare_input_for_agent(self, ocr_result: dict, agent_type: str) -> str:
        """为Agent准备输入文本"""

        if not ocr_result or not ocr_result.get("pretty"):
            return ""

        pages_data = ocr_result["pretty"]

        if agent_type == "meta_extractor":
            # 取前4个页面的数据
            selected_pages = pages_data[:4]
        elif agent_type == "img_parser":
            # 使用detail全量数据包含图片地址
            return json.dumps(ocr_result.get("detail", []), ensure_ascii=False)
        else:
            # 其他agent使用所有页面数据
            selected_pages = pages_data

        # 构造包含page_id和page_md的字符串
        input_parts = []
        for page in selected_pages:
            if isinstance(page, dict):
                page_id = page.get("page_id", "")
                page_md = page.get("page_md", "")
                input_parts.append(f"页码: {page_id}\n内容: {page_md}")

        return "\n\n".join(input_parts)

    def _save_ocr_result(self, report_id: str, ocr_result: dict):
        """保存OCR结果到TTL缓存"""
        self._ocr_cache[report_id] = ocr_result
        logger.debug("保存OCR结果到缓存", report_id=report_id, cache_size=len(self._ocr_cache))

    def _get_ocr_result(self, report_id: str) -> dict | None:
        """从TTL缓存获取OCR结果"""
        result = self._ocr_cache.get(report_id)
        if result:
            logger.debug("从缓存获取OCR结果", report_id=report_id)
        return result

    async def _handle_step_error(
        self, task: ReportTask, step_name: str, error_msg: str, execution_time: int, db: Session
    ):
        """处理步骤错误"""

        task.status = TaskStatus.FAILED
        task.error_message = error_msg
        task.error_step = step_name
        task.retry_count += 1
        task.updated_at = datetime.utcnow()

        db.commit()

        await self._log_step(task.report_id, step_name, LogStatus.FAILED, error_msg, execution_time)

        logger.error("步骤执行失败", report_id=task.report_id, step=step_name, error=error_msg)

    async def _handle_task_error(self, report_id: str, error_msg: str, db: Session):
        """处理任务级别错误"""

        task = db.query(ReportTask).filter(ReportTask.report_id == report_id).first()

        if task:
            task.status = TaskStatus.FAILED
            task.error_message = error_msg
            task.updated_at = datetime.utcnow()
            db.commit()

        await self._log_step(report_id, "任务处理", LogStatus.FAILED, error_msg)

    async def _log_step(
        self,
        report_id: str,
        step_name: str,
        status: LogStatus,
        message: str | None = None,
        execution_time_ms: int | None = None,
    ):
        """记录步骤日志"""

        async with self.get_db_session() as db:
            try:
                log = ReportTaskLog(
                    report_id=report_id,
                    step_name=step_name,
                    status=status,
                    message=message,
                    execution_time_ms=execution_time_ms,
                )

                db.add(log)
                db.commit()

            except Exception as e:
                logger.error("记录步骤日志失败", report_id=report_id, step=step_name, error=str(e))


# 全局任务处理器实例 - 线程安全
_task_processor = None
_task_processor_lock = threading.Lock()


def get_task_processor() -> ReportTaskProcessor:
    """获取任务处理器实例 - 线程安全版本"""
    global _task_processor
    if _task_processor is None:
        with _task_processor_lock:
            # 双重检查锁定
            if _task_processor is None:
                _task_processor = ReportTaskProcessor()
    return _task_processor
