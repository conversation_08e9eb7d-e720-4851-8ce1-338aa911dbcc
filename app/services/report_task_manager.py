"""
报告任务管理器 - 优化版本
"""

import asyncio
from contextlib import asynccontextmanager
from datetime import datetime
import threading

import redis.asyncio as aioredis
from sqlalchemy.orm import Session
from structlog import get_logger

from app.core.config import settings
from app.models import ReportTask, TaskStatus, get_db
from app.schemas.report_task import (
    StepDetail,
    TaskStatusResponse,
)

logger = get_logger(__name__)


class RedisTaskQueue:
    """Redis持久化任务队列"""

    def __init__(self):
        self.redis: aioredis.Redis | None = None
        self.queue_name = "report_task_queue"
        self._initialized = False

    async def initialize(self):
        """初始化Redis连接"""
        if self._initialized:
            return

        try:
            self.redis = aioredis.from_url(settings.REDIS_URL)
            await self.redis.ping()
            self._initialized = True
            logger.info("Redis任务队列初始化成功")
        except Exception as e:
            logger.error("Redis连接失败，回退到内存队列", error=str(e))
            self.redis = None

    async def put(self, task_id: str):
        """添加任务到队列"""
        await self.redis.lpush(self.queue_name, task_id)

    async def get(self):
        """从队列获取任务"""
        result = await self.redis.brpop(self.queue_name, timeout=1)
        return result[1].decode() if result else None

    async def size(self) -> int:
        """获取队列大小"""
        return await self.redis.llen(self.queue_name)


class ReportTaskManager:
    """报告任务管理器"""

    def __init__(self):
        self.task_queue = RedisTaskQueue()
        self._workers_started = False
        self._lock = threading.Lock()  # 线程安全保护
        self._worker_tasks = []  # 存储工作任务引用

    @asynccontextmanager
    async def get_db_session(self):
        """安全的数据库会话管理"""
        db = next(get_db())
        try:
            yield db
        finally:
            db.close()

    async def submit_task(self, report_id: str, report_key: str, db: Session = None) -> dict:
        """提交报告解析任务"""

        # 确保Redis队列已初始化
        await self.task_queue.initialize()

        try:
            # 检查任务是否已存在
            existing_task = db.query(ReportTask).filter(ReportTask.report_id == report_id).first()

            if existing_task:
                if existing_task.status in [TaskStatus.PENDING, TaskStatus.FAILED]:
                    # 重新提交失败或待处理的任务
                    existing_task.status = TaskStatus.PENDING
                    existing_task.current_step = 0
                    existing_task.progress_percent = 0.00
                    existing_task.error_message = None
                    existing_task.error_step = None
                    existing_task.retry_count = 0
                    existing_task.updated_at = datetime.utcnow()

                    db.commit()

                    # 添加到任务队列
                    await self.task_queue.put(report_id)

                    logger.info("重新提交现有任务", report_id=report_id)
                    return {"task_id": report_id, "status": "resubmitted", "message": "任务已重新提交"}
                else:
                    logger.warning("任务已存在且正在处理", report_id=report_id, status=existing_task.status)
                    return {"task_id": report_id, "status": existing_task.status.value, "message": "任务已存在"}

            # 创建新任务
            new_task = ReportTask(
                report_id=report_id,
                report_key=report_key,
                status=TaskStatus.PENDING,
                current_step=0,
                total_steps=6,
                progress_percent=0.00,
            )

            db.add(new_task)
            db.commit()

            # 添加到任务队列
            await self.task_queue.put(report_id)

            # 启动工作线程（如果还没启动）
            with self._lock:  # 线程安全
                if not self._workers_started:
                    await self.start_workers()

            logger.info("创建新任务", report_id=report_id, report_key=report_key)

            return {"task_id": report_id, "status": "submitted", "message": "任务已提交"}

        except Exception as e:
            logger.error("提交任务失败", report_id=report_id, error=str(e))
            db.rollback()
            raise

    async def get_task_status(self, report_id: str, db: Session) -> TaskStatusResponse | None:
        """获取任务状态"""
        try:
            task = db.query(ReportTask).filter(ReportTask.report_id == report_id).first()

            if not task:
                return None

            # 构建步骤详情
            step_details = self._build_step_details(task)

            return TaskStatusResponse(
                report_id=task.report_id,
                status=task.status.value,
                current_step=task.current_step,
                total_steps=task.total_steps,
                progress_percent=task.progress_percent,
                step_details=step_details,
                ocr_result_key=task.ocr_result_key,
                final_json_path=task.final_json_path,
                error_message=task.error_message,
                error_step=task.error_step,
                retry_count=task.retry_count,
                created_at=task.created_at,
                updated_at=task.updated_at,
                started_at=task.started_at,
                completed_at=task.completed_at,
            )

        except Exception as e:
            logger.error("获取任务状态失败", report_id=report_id, error=str(e))
            raise


    async def restart_task(self, report_id: str, from_step: int, db: Session) -> dict:
        """重新执行任务的具体实现"""
        try:
            task = db.query(ReportTask).filter(ReportTask.report_id == report_id).first()

            if not task:
                return {"report_id": report_id, "status": "not_found", "message": "任务不存在"}

            # 重置任务状态
            task.status = TaskStatus.PENDING
            task.current_step = from_step
            task.progress_percent = (from_step / task.total_steps) * 100
            task.error_message = None
            task.error_step = None
            task.retry_count = 0
            task.updated_at = datetime.utcnow()

            # 根据from_step清理相应的结果
            if from_step <= 0:
                task.ocr_result_key = None
                task.ocr_error = None
            if from_step <= 1:
                task.meta_result = None
            if from_step <= 2:
                task.overall_result = None
            if from_step <= 3:
                task.tabular_result = None
            if from_step <= 4:
                task.image_result = None
            if from_step <= 5:
                task.final_json_path = None

            db.commit()

            # 重新添加到任务队列
            await self.task_queue.put(report_id)

            logger.info("重新启动任务", report_id=report_id, from_step=from_step)

            return {
                "report_id": report_id,
                "status": "restarted",
                "message": f"任务已从第{from_step}步重新开始",
                "restarted_from_step": from_step,
            }

        except Exception as e:
            logger.error("重启任务失败", report_id=report_id, error=str(e))
            db.rollback()
            raise


    def _build_step_details(self, task: ReportTask) -> list[StepDetail]:
        """构建步骤详情列表"""

        steps = [
            ("OCR解析", TaskStatus.OCR_PROCESSING, TaskStatus.OCR_COMPLETED),
            ("个人信息提取", TaskStatus.META_PROCESSING, TaskStatus.META_COMPLETED),
            ("总体结果分析", TaskStatus.OVERALL_PROCESSING, TaskStatus.OVERALL_COMPLETED),
            ("检查项指标分析", TaskStatus.TABULAR_PROCESSING, TaskStatus.TABULAR_COMPLETED),
            ("影像数据分析", TaskStatus.IMAGE_PROCESSING, TaskStatus.IMAGE_COMPLETED),
            ("写入JSON文件", TaskStatus.WRITING_JSON, TaskStatus.COMPLETED),
        ]

        step_details = []
        for i, (step_name, processing_status, completed_status) in enumerate(steps):
            if i < task.current_step:
                # 已完成的步骤
                status = "completed"
                completed = True
                error_message = None
            elif i == task.current_step:
                # 当前步骤
                if task.status == TaskStatus.FAILED and task.error_step:
                    status = "failed"
                    completed = False
                    error_message = task.error_message
                elif task.status in [processing_status, completed_status]:
                    status = "processing" if task.status == processing_status else "completed"
                    completed = task.status == completed_status
                    error_message = None
                else:
                    status = "pending"
                    completed = False
                    error_message = None
            else:
                # 未来的步骤
                status = "pending"
                completed = False
                error_message = None

            step_details.append(
                StepDetail(
                    step_index=i, step_name=step_name, status=status, completed=completed, error_message=error_message
                )
            )

        return step_details

    async def start_workers(self, num_workers: int = 3):
        """启动工作线程"""
        if self._workers_started:
            return

        # 确保Redis队列已初始化
        await self.task_queue.initialize()

        for i in range(num_workers):
            task = asyncio.create_task(self._worker(f"worker-{i}"))
            self._worker_tasks.append(task)  # 存储任务引用

        self._workers_started = True
        logger.info("启动任务工作线程", num_workers=num_workers)

    async def _worker(self, worker_name: str):
        """工作线程 - 优化版本"""
        logger.info("工作线程启动", worker=worker_name)

        # 导入处理器（延迟导入避免循环依赖）
        from app.services.report_task_processor import get_task_processor

        processor = get_task_processor()

        while True:
            try:
                # 从队列获取任务，带超时处理
                report_id = await self.task_queue.get()

                if report_id is None:
                    # 超时或队列为空，继续循环
                    continue

                logger.info("工作线程获取到任务", worker=worker_name, report_id=report_id)

                # 处理任务
                success = await processor.process_task(report_id)

                if success:
                    logger.info("任务处理成功", worker=worker_name, report_id=report_id)
                else:
                    logger.error("任务处理失败", worker=worker_name, report_id=report_id)

                # Redis队列不需要task_done操作

            except Exception as e:
                logger.error("工作线程处理任务失败", worker=worker_name, error=str(e))
                # 等待一段时间后继续，避免快速失败循环
                await asyncio.sleep(5)


# 全局任务管理器实例 - 线程安全
_task_manager = None
_task_manager_lock = threading.Lock()


def get_task_manager() -> ReportTaskManager:
    """获取任务管理器实例 - 线程安全版本"""
    global _task_manager
    if _task_manager is None:
        with _task_manager_lock:
            # 双重检查锁定
            if _task_manager is None:
                _task_manager = ReportTaskManager()
    return _task_manager
