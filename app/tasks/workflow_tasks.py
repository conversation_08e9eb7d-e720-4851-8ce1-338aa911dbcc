"""
工作流任务模块
"""

import logging
from typing import Dict, List
from datetime import datetime

from celery import chain, group, chord
from app.core.celery_config import celery_app
from app.tasks.base_task import BaseWorkflowTask, RetryableError, FatalError

# 导入具体任务
from app.tasks.ocr_tasks import ocr_task
from app.tasks.ai_tasks import (
    meta_analysis_task, tabular_analysis_task, 
    overall_analysis_task, image_analysis_task, 
    diseases_extraction_task
)
from app.tasks.post_process_tasks import translation_task, indicator_conversion_task
from app.tasks.storage_tasks import call_api_task, store_vector_task

logger = logging.getLogger(__name__)


@celery_app.task(base=BaseWorkflowTask, bind=True, queue='workflow_queue')
def create_report_workflow(self, report_id: str, report_key: str):
    """
    创建并执行报告处理工作流
    
    Args:
        report_id: 报告ID
        report_key: 报告密钥
    """
    try:
        self.validate_task_input(report_id=report_id)
        
        if not report_key:
            raise FatalError("Missing required parameter: report_key")
        
        logger.info(f"Creating workflow for report {report_id}")
        
        # 更新主任务状态为处理中
        _update_main_task_status(report_id, 'processing')
        
        # 步骤1: OCR处理
        ocr_step = ocr_task.s(report_id=report_id, report_key=report_key)
        
        # 步骤2: AI分析并行任务组
        ai_analysis_group = group(
            meta_analysis_task.s(report_id=report_id),
            tabular_analysis_task.s(report_id=report_id),
            overall_analysis_task.s(report_id=report_id),
            image_analysis_task.s(report_id=report_id),
            diseases_extraction_task.s(report_id=report_id)
        )
        
        # 步骤3: 后处理串行任务
        post_processing_chain = chain(
            translation_task.s(report_id=report_id),
            indicator_conversion_task.s(report_id=report_id)
        )
        
        # 步骤4: 存储并行任务
        storage_group = group(
            call_api_task.s(report_id=report_id),
            store_vector_task.s(report_id=report_id)
        )
        
        # 组合完整工作流
        workflow = chain(
            ocr_step,
            chord(ai_analysis_group, post_processing_chain),
            storage_group,
            finalize_workflow.s(report_id=report_id)
        )
        
        # 执行工作流
        result = workflow.apply_async()
        
        # 保存工作流ID
        _save_workflow_id(report_id, result.id)
        
        logger.info(f"Workflow created and started for report {report_id}, workflow_id: {result.id}")
        
        return {
            'success': True,
            'report_id': report_id,
            'workflow_id': result.id,
            'status': 'started'
        }
        
    except Exception as e:
        logger.error(f"Failed to create workflow for report {report_id}: {e}")
        _update_main_task_status(report_id, 'failed', str(e))
        if isinstance(e, (FatalError, RetryableError)):
            raise
        else:
            raise FatalError(f"Workflow creation error: {e}")


@celery_app.task(base=BaseWorkflowTask, bind=True, queue='workflow_queue')
def finalize_workflow(self, results: List[Dict], report_id: str):
    """
    完成工作流处理
    
    Args:
        results: 前面步骤的结果列表
        report_id: 报告ID
    """
    try:
        self.validate_task_input(report_id=report_id)
        
        logger.info(f"Finalizing workflow for report {report_id}")
        
        # 汇总所有结果
        summary = _summarize_results(results, report_id)
        
        # 更新主任务状态为完成
        _update_main_task_status(report_id, 'completed')
        
        # 保存最终汇总结果
        self._save_task_result(report_id, 'final_summary', summary)
        
        logger.info(f"Workflow completed for report {report_id}")
        
        return {
            'success': True,
            'report_id': report_id,
            'status': 'completed',
            'summary': summary
        }
        
    except Exception as e:
        logger.error(f"Failed to finalize workflow for report {report_id}: {e}")
        _update_main_task_status(report_id, 'failed', str(e))
        if isinstance(e, (FatalError, RetryableError)):
            raise
        else:
            raise FatalError(f"Workflow finalization error: {e}")


def _update_main_task_status(report_id: str, status: str, error_message: str = None):
        """更新主任务状态"""
        try:
            from app.models import get_db, ReportTask
            
            db = next(get_db())
            try:
                task = db.query(ReportTask).filter(ReportTask.report_id == report_id).first()
                if task:
                    task.status = status
                    task.updated_at = datetime.utcnow()
                    
                    if status == 'completed':
                        task.completed_at = datetime.utcnow()
                    elif error_message:
                        task.error_message = error_message
                    
                    db.commit()
                    
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Failed to update main task status: {e}")
    
def _save_workflow_id(report_id: str, workflow_id: str):
        """保存工作流ID"""
        try:
            from app.models import get_db, ReportTask
            
            db = next(get_db())
            try:
                task = db.query(ReportTask).filter(ReportTask.report_id == report_id).first()
                if task:
                    task.workflow_id = workflow_id
                    task.updated_at = datetime.utcnow()
                    db.commit()
                    
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Failed to save workflow ID: {e}")
    
def _summarize_results(results: List[Dict], report_id: str) -> Dict:
        """汇总工作流结果"""
        try:
            summary = {
                'report_id': report_id,
                'completed_at': datetime.utcnow().isoformat(),
                'total_steps': len(results) if results else 0,
                'successful_steps': 0,
                'failed_steps': 0,
                'step_results': []
            }
            
            if results:
                for result in results:
                    if isinstance(result, dict):
                        if result.get('success', False):
                            summary['successful_steps'] += 1
                        else:
                            summary['failed_steps'] += 1
                        summary['step_results'].append(result)
            
            return summary
            
        except Exception as e:
            logger.error(f"Failed to summarize results: {e}")
            return {
                'report_id': report_id,
                'error': f"Failed to summarize: {e}",
                'completed_at': datetime.utcnow().isoformat()
            }