{"cells": [{"cell_type": "code", "id": "f84810a1", "metadata": {"ExecuteTime": {"end_time": "2025-07-22T14:49:52.941382Z", "start_time": "2025-07-22T14:49:50.249453Z"}}, "source": ["from dotenv import load_dotenv\n", "load_dotenv()\n", "\n", "from agno.models.openai import OpenAILike\n", "from agno.agent import Agent\n", "\n", "from app.core.llm_core import get_agent_model_config\n", "\n", "model=OpenAILike(\n", "    **get_agent_model_config(\"doubao_seed\"),\n", "    request_params={\n", "        'extra_body': {\n", "            \"thinking\": {\n", "                \"type\": \"disabled\",  # 不使用深度思考能力\n", "                # \"type\": \"enabled\", # 使用深度思考能力\n", "                # \"type\": \"auto\", # 自行判断是否使用深度思考能力\n", "                }\n", "        }})\n", "\n", "agent = Agent(model=model, markdown=True, debug_mode=True)\n", "\n", "agent.print_response(\"锄禾日当午\")\n"], "outputs": [{"data": {"text/plain": ["\u001b[32mDEBUG\u001b[0m ****** Agent ID: \u001b[93me6c329a6-4147-4342-b8a7-3e895bb25b87\u001b[0m ******                                                 \n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">DEBUG</span> ****** Agent ID: <span style=\"color: #ffff00; text-decoration-color: #ffff00\">e6c329a6-4147-4342-b8a7-3e895bb25b87</span> ******                                                 \n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[32mDEBUG\u001b[0m ***** Session ID: \u001b[93m43aaaad2-f673-434c-937f-4f692053c031\u001b[0m *****                                                 \n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">DEBUG</span> ***** Session ID: <span style=\"color: #ffff00; text-decoration-color: #ffff00\">43aaaad2-f673-434c-937f-4f692053c031</span> *****                                                 \n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[32mDEBUG\u001b[0m ** Agent Run Start: \u001b[93mb77192a9-3c1e-4c7c-a6c4-4608a7f83a1f\u001b[0m ***                                                 \n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">DEBUG</span> ** Agent Run Start: <span style=\"color: #ffff00; text-decoration-color: #ffff00\">b77192a9-3c1e-4c7c-a6c4-4608a7f83a1f</span> ***                                                 \n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[32mDEBUG\u001b[0m ------------------ OpenAI Response Start -------------------                                                 \n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">DEBUG</span> ------------------ OpenAI Response Start -------------------                                                 \n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[32mDEBUG\u001b[0m -------------- Model: doubao-seed-\u001b[1;36m1\u001b[0m-\u001b[1;36m6\u001b[0m-\u001b[1;36m250615\u001b[0m ---------------                                                 \n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">DEBUG</span> -------------- Model: doubao-seed-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">250615</span> ---------------                                                 \n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[32mDEBUG\u001b[0m ========================== system ==========================                                                 \n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">DEBUG</span> ========================== system ==========================                                                 \n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[32mDEBUG\u001b[0m \u001b[1m<\u001b[0m\u001b[1;95madditional_information\u001b[0m\u001b[39m>\u001b[0m                                                                                     \n", "      \u001b[39m- Use markdown to format your answers.\u001b[0m                                                                       \n", "      \u001b[39m<\u001b[0m\u001b[35m/\u001b[0m\u001b[95madditional_information\u001b[0m\u001b[1m>\u001b[0m                                                                                    \n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">DEBUG</span> <span style=\"font-weight: bold\">&lt;</span><span style=\"color: #ff00ff; text-decoration-color: #ff00ff; font-weight: bold\">additional_information</span><span style=\"color: #000000; text-decoration-color: #000000\">&gt;</span>                                                                                     \n", "      <span style=\"color: #000000; text-decoration-color: #000000\">- Use markdown to format your answers.</span>                                                                       \n", "      <span style=\"color: #000000; text-decoration-color: #000000\">&lt;</span><span style=\"color: #800080; text-decoration-color: #800080\">/</span><span style=\"color: #ff00ff; text-decoration-color: #ff00ff\">additional_information</span><span style=\"font-weight: bold\">&gt;</span>                                                                                    \n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[32mDEBUG\u001b[0m =========================== user ===========================                                                 \n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">DEBUG</span> =========================== user ===========================                                                 \n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[32mDEBUG\u001b[0m 锄禾日当午                                                                                                   \n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">DEBUG</span> 锄禾日当午                                                                                                   \n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["Output()"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "34770cd5164d4576a364547272b79d20"}}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[32mDEBUG\u001b[0m ======================== assistant =========================                                                 \n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">DEBUG</span> ======================== assistant =========================                                                 \n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[32mDEBUG\u001b[0m 汗滴禾下土。                                                                                                 \n", "      谁知盘中餐，                                                                                                 \n", "      粒粒皆辛苦。                                                                                                 \n", "                                                                                                                   \n", "      这首诗出自唐代诗人李绅的《悯农二首·其二》，通过描绘农民劳作的艰辛，告诫人们珍惜粮食。                        \n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">DEBUG</span> 汗滴禾下土。                                                                                                 \n", "      谁知盘中餐，                                                                                                 \n", "      粒粒皆辛苦。                                                                                                 \n", "                                                                                                                   \n", "      这首诗出自唐代诗人李绅的《悯农二首·其二》，通过描绘农民劳作的艰辛，告诫人们珍惜粮食。                        \n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[32mDEBUG\u001b[0m ************************  METRICS  *************************                                                 \n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">DEBUG</span> ************************  METRICS  *************************                                                 \n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[32mDEBUG\u001b[0m * Tokens:                      \u001b[33minput\u001b[0m=\u001b[1;36m64\u001b[0m, \u001b[33moutput\u001b[0m=\u001b[1;36m45\u001b[0m, \u001b[33mtotal\u001b[0m=\u001b[1;36m109\u001b[0m                                                \n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">DEBUG</span> * Tokens:                      <span style=\"color: #808000; text-decoration-color: #808000\">input</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">64</span>, <span style=\"color: #808000; text-decoration-color: #808000\">output</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">45</span>, <span style=\"color: #808000; text-decoration-color: #808000\">total</span>=<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">109</span>                                                \n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[32mDEBUG\u001b[0m * Prompt tokens details:       \u001b[1m{\u001b[0m\u001b[32m'cached_tokens'\u001b[0m: \u001b[1;36m0\u001b[0m\u001b[1m}\u001b[0m                                                          \n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">DEBUG</span> * Prompt tokens details:       <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'cached_tokens'</span>: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span><span style=\"font-weight: bold\">}</span>                                                          \n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[32mDEBUG\u001b[0m * Completion tokens details:   \u001b[1m{\u001b[0m\u001b[32m'reasoning_tokens'\u001b[0m: \u001b[1;36m0\u001b[0m\u001b[1m}\u001b[0m                                                       \n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">DEBUG</span> * Completion tokens details:   <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'reasoning_tokens'</span>: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span><span style=\"font-weight: bold\">}</span>                                                       \n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[32mDEBUG\u001b[0m * Time:                        \u001b[1;36m1.\u001b[0m0742s                                                                       \n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">DEBUG</span> * Time:                        <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.</span>0742s                                                                       \n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[32mDEBUG\u001b[0m * Tokens per second:           \u001b[1;36m41.8923\u001b[0m tokens/s                                                              \n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">DEBUG</span> * Tokens per second:           <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">41.8923</span> tokens/s                                                              \n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[32mDEBUG\u001b[0m ************************  METRICS  *************************                                                 \n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">DEBUG</span> ************************  METRICS  *************************                                                 \n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[32mDEBUG\u001b[0m ------------------- OpenAI Response End --------------------                                                 \n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">DEBUG</span> ------------------- OpenAI Response End --------------------                                                 \n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[32mDEBUG\u001b[0m Added RunResponse to Memory                                                                                  \n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">DEBUG</span> Added RunResponse to Memory                                                                                  \n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\u001b[32mDEBUG\u001b[0m *** Agent Run End: \u001b[93mb77192a9-3c1e-4c7c-a6c4-4608a7f83a1f\u001b[0m ****                                                 \n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">DEBUG</span> *** Agent Run End: <span style=\"color: #ffff00; text-decoration-color: #ffff00\">b77192a9-3c1e-4c7c-a6c4-4608a7f83a1f</span> ****                                                 \n", "</pre>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}, "output_type": "display_data"}], "execution_count": 4}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}