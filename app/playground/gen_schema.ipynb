{"cells": [{"cell_type": "code", "execution_count": 1, "id": "08746bfe", "metadata": {}, "outputs": [], "source": ["\"\"\"生成 output_schema\n", "\n", "对于新页面，需要生成 output_schema, 然后保存到 schemas 目录下, 方便后续使用\n", "支持三种方式:\n", "1. 从本地HTML文件读取\n", "2. 从URL读取\n", "3. 直接使用raw HTML字符串\n", "\"\"\"\n", "import json\n", "import os\n", "\n", "from crawl4ai import AsyncWebCrawler\n", "from crawl4ai import CacheMode\n", "from crawl4ai import CrawlerRunConfig\n", "from crawl4ai import LLMConfig\n", "from crawl4ai.extraction_strategy import JsonCssExtractionStrategy\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()\n", "\n", "\n", "async def generate_schema_from_file(file_path: str, prompt: str):\n", "    \"\"\"从本地HTML文件生成schema\"\"\"\n", "    with open(file_path, encoding='utf-8') as f:\n", "        html_content = f.read()\n", "    return await generate_schema_from_raw_html(html_content, prompt)\n", "\n", "\n", "async def generate_schema_from_url(url: str, prompt: str):\n", "    \"\"\"从URL生成schema\"\"\"\n", "    async with AsyncWebCrawler() as crawler:\n", "        result = await crawler.arun(\n", "            url=url,\n", "            config=CrawlerRunConfig(\n", "                cache_mode=CacheMode.BYPASS,\n", "            ),\n", "        )\n", "        return await generate_schema_from_raw_html(result.html, prompt)\n", "\n", "\n", "async def generate_schema_from_raw_html(raw_html: str, prompt: str):\n", "    \"\"\"从raw HTML字符串生成schema\"\"\"\n", "    async with AsyncWebCrawler() as crawler:\n", "        schema = JsonCssExtractionStrategy.generate_schema(\n", "            raw_html,\n", "            llm_config=LLMConfig(\n", "                provider=\"openrouter/google/gemini-2.5-pro-preview\",\n", "                api_token=os.getenv(\"OPENAI_API_KEY\"),\n", "            ),\n", "            query=prompt,\n", "        )\n", "        strategy = JsonCssExtractionStrategy(schema)\n", "        print(\"Generated Schema:\")\n", "        print(strategy.schema)\n", "\n", "        result = await crawler.arun(\n", "            url=\"raw://\" + raw_html,\n", "            config=CrawlerRunConfig(\n", "                cache_mode=CacheMode.BYPASS,\n", "                extraction_strategy=strategy,\n", "            ),\n", "        )\n", "        data = json.loads(result.extracted_content)\n", "        print(\"\\nExtracted Data:\")\n", "        print(data)\n", "        return schema, data\n"]}, {"cell_type": "code", "execution_count": 2, "id": "c9809822", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080\">INIT</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080\">.... → Crawl4AI </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.6</span><span style=\"color: #008080; text-decoration-color: #008080\">.</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m[\u001b[0m\u001b[36mINIT\u001b[0m\u001b[1;36m]\u001b[0m\u001b[36m...\u001b[0m\u001b[36m. → Crawl4AI \u001b[0m\u001b[1;36m0.6\u001b[0m\u001b[36m.\u001b[0m\u001b[1;36m3\u001b[0m\u001b[36m \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">FETCH</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">... ↓ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://www.msdmanuals.cn/professional/nutrition...al-considerations/nutrition-in-clinical-medicine</span><span style=\"color: #008000; text-decoration-color: #008000\">  |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">19s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mFETCH\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m...\u001b[0m\u001b[32m ↓ \u001b[0m\u001b[4;32mhttps://www.msdmanuals.cn/professional/nutrition...al-considerations/nutrition-in-clinical-medicine\u001b[0m\u001b[32m  |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m19s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">SCRAPE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">.. ◆ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://www.msdmanuals.cn/professional/nutrition...al-considerations/nutrition-in-clinical-medicine</span><span style=\"color: #008000; text-decoration-color: #008000\">  |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">06s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mSCRAPE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m.. ◆ \u001b[0m\u001b[4;32mhttps://www.msdmanuals.cn/professional/nutrition...al-considerations/nutrition-in-clinical-medicine\u001b[0m\u001b[32m  |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m06s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">COMPLETE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> ● </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://www.msdmanuals.cn/professional/nutrition...al-considerations/nutrition-in-clinical-medicine</span><span style=\"color: #008000; text-decoration-color: #008000\">  |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">25s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mCOMPLETE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m ● \u001b[0m\u001b[4;32mhttps://www.msdmanuals.cn/professional/nutrition...al-considerations/nutrition-in-clinical-medicine\u001b[0m\u001b[32m  |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m25s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080\">INIT</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080\">.... → Crawl4AI </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.6</span><span style=\"color: #008080; text-decoration-color: #008080\">.</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m[\u001b[0m\u001b[36mINIT\u001b[0m\u001b[1;36m]\u001b[0m\u001b[36m...\u001b[0m\u001b[36m. → Crawl4AI \u001b[0m\u001b[1;36m0.6\u001b[0m\u001b[36m.\u001b[0m\u001b[1;36m3\u001b[0m\u001b[36m \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Generated Schema:\n", "{'name': 'Medical_Article', 'baseSelector': \"div[id^='container-']\", 'fields': [{'name': 'title', 'selector': 'h1#topicHeaderTitle', 'type': 'text'}, {'name': 'content', 'selector': \"div[data-testid='topic-main-content']\", 'type': 'text'}, {'name': 'sections', 'selector': 'section[id]', 'type': 'list', 'fields': [{'name': 'title', 'selector': 'h2', 'type': 'text'}, {'name': 'content', 'selector': 'div.TopicFHead_fHeadBody__mLB6h', 'type': 'text'}]}]}\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">FETCH</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">... ↓ Raw HTML                                                                                             |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">00s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mFETCH\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m...\u001b[0m\u001b[32m ↓ Raw HTML                                                                                             |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m00s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">SCRAPE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">.. ◆ Raw HTML                                                                                             |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">07s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mSCRAPE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m.. ◆ Raw HTML                                                                                             |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m07s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080\">EXTRACT</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080\">. ■ Completed for Raw HTML... | Time: </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.</span><span style=\"color: #008080; text-decoration-color: #008080\">01952941669151187s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m[\u001b[0m\u001b[36mEXTRACT\u001b[0m\u001b[1;36m]\u001b[0m\u001b[36m. ■ Completed for Raw HTML\u001b[0m\u001b[36m...\u001b[0m\u001b[36m | Time: \u001b[0m\u001b[1;36m0.\u001b[0m\u001b[36m01952941669151187s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">COMPLETE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> ● Raw HTML                                                                                             |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">10s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mCOMPLETE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m ● Raw HTML                                                                                             |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m10s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Extracted Data:\n", "[{'title': '临床营养', 'content': '营养缺乏常导致健康损害（不论是否存在疾病状态），一些机体疾病状态（如吸收不良）可以导致营养缺乏。 此外，许多病人（如急性住院的老年患者）可能存在需要治疗的未被察觉的营养不良。许多医疗中心已经建立由医师、护士、营养学家和药剂师等组成的临床营养支持疗法的多学科专业团队，提供对营养缺乏症的预防、诊断和治疗服务。过度营养可能招致慢性疾病，如癌症、高血压、肥胖症、糖尿病和冠心病。 对某些遗传性代谢性疾病（如半乳糖血症、苯丙酮尿症）实施饮食控制是必要的。营养状态的评价营养评估的适应症包括：异常的体重或体质组成怀疑存在特异性的必需营养素缺乏或中毒在婴幼儿中，生长发育迟滞营养状况应作为临床检查的一部分，进行常规评估婴儿和儿童老年人群接受几种药物的人精神异常的人患有系统性疾病的人持续时间超过数天营养评价的一般内容包括病史、体格检查，有时需要化验。 如果怀疑存在营养缺乏，应该进行实验室检验（如血清白蛋白测定）和皮肤迟发行性变态反应试验。 体质成分组成的检测（如皮褶厚度测量和生物电阻抗测定）通常用于评估体内脂肪的含量以诊断肥胖病。病史采集包括询问膳食情况、体重变化，营养缺乏的危险因素，以及各个系统相关的重点回顾（见表营养不良的症状和体征）。营养学家可以收集更详细的膳食情况信息，通常包括24小时内摄入的食物调查问卷，记录了摄入的食物内容和数量。通过膳食调查表，可以详细了解患者的体重以及摄入的食物性质和数量。表格营养缺乏症的临床症状和体征表格营养缺乏症的临床症状和体征部位/系统症状和体征缺乏的营养素一般表现消瘦总热liang皮肤皮疹多种维生素、锌、必需脂肪酸背侧皮疹烟酸（糙皮病）容易出血、瘀伤维生素C，维生素K毛发和指甲毛发稀疏或脱落蛋白质白发早现硒指甲外翻的杓样变铁眼睛夜盲维生素A角膜软化（角膜干燥和云翳）维生素A口腔唇炎和舌炎核黄素，烟酸，吡哆醛，铁齿龈出血维生素C，核黄素肢端水肿蛋白质神经系统感觉异常或手套样皮肤感觉麻木硫胺（脚气病）手足搐愵钙、镁感知缺失硫胺、烟酸、吡哆醛、维生素B12痴呆硫胺、烟酸、维生素B12骨骼肌系统肌肉萎缩蛋白质骨骼变型（例如O型腿、两腿向内弯曲、脊柱弯曲）维生素D、钙骨软化维生素D关节痛或水肿维生素C胃肠系统腹泻蛋白质、烟酸、叶酸、维生素 B12腹泻和味觉障碍锌吞咽困难或吞咽痛（由于Plummer-Vinson综合征导致）铁内分泌系统甲状腺肿大碘体格检查应该包括体重和身高的测定以及身体脂肪的部位分布。体质指数（BMI）--体重（kg）/身高（m）2, 根据身高校正体重(见表体质指数)， 此种评价方法比身高和体重表准确。婴儿、儿童以及青少年都有各自的生长和体重增加标准（见婴儿和儿童的身体发育）。表格体质指数（BMI）表格体质指数（BMI）体重类别（体质指数BMI)正常*（≥ 18.5 且 < 25）超重（≥ 25 且 < 30）肥胖：I级 （≥ 30 且 < 35）肥胖：II 级（≥ 35 且 < 40）肥胖：III级（≥40）高度体重60–61英寸（152–155厘米）97–127磅（44–58千克）128–153磅（58–69千克）153–180磅（69–82千克）179–206磅（81–93千克）> 206磅（> 93公斤）62–63英寸（157–160厘米）104–135磅（47–61千克）136–163磅（62–74千克）164–191磅（74–87千克）191–220磅（87–100公斤）> 220磅（> 100公斤）64–65英寸（162–165厘米）110–144磅（50–65千克）145–174磅（66–79千克）174–204磅（79–93千克）204–234磅（93–106千克）> 234磅（> 106公斤）66–67英寸（168–170厘米）118–153磅（54–69千克）155–185磅（70–84千克）186–217磅（84–98千克）216–249磅（98–113千克）> 249磅（> 113公斤）68–69英寸（173–175厘米）125–162磅（57–74公斤）164–196磅（74–89千克）197–230磅（89–104千克）230–263磅（104–119千克）> 263磅（> 119公斤）70–71英寸（178–180厘米）132–172磅（60–78千克）174–208磅（79–94千克）209–243磅（95–110千克）243–279磅（110–127公斤）> 279磅（> 127公斤）72–73英寸（183–185厘米）140–182磅（64–83千克）184–219磅（84–99千克）221–257磅（100–117千克）258–295磅（117–134千克）> 295磅（> 134公斤）74–75英寸（188–190厘米）148–192磅（67–87千克）194–232磅（88–105千克）233–272磅（106–123千克）272–311磅（123–141千克）> 311磅（> 141公斤）76英寸（193厘米）156–197磅（71–89千克）205–238磅（93–108千克）246–279磅（112–127千克）287～320磅（130～145千克）> 320磅（> 145公斤）*BMI值低于所列的正常值被认为是低体重。体内脂肪的部位分布也是评价的重要方面，躯干部分的脂肪堆积（表现为腰/臀比例>0.8）相比于其他部位的脂肪堆积，更多伴随着心脑血管疾病、高血压和糖尿病。对于BMI小于35的患者进行腰围的测量，有助于判断他们是否存在躯干型肥胖，也有助于预测糖尿病、高血压、高胆固醇血症和心血管疾病发生的危险性。如果男性的腰围大于102cm，女性的腰围大于88cm，上述疾病发生的危险性将大为增加。临床计算器体质指数（凯特勒指数）', 'sections': [{'title': '营养状态的评价', 'content': '营养评估的适应症包括：异常的体重或体质组成怀疑存在特异性的必需营养素缺乏或中毒在婴幼儿中，生长发育迟滞营养状况应作为临床检查的一部分，进行常规评估婴儿和儿童老年人群接受几种药物的人精神异常的人患有系统性疾病的人持续时间超过数天营养评价的一般内容包括病史、体格检查，有时需要化验。 如果怀疑存在营养缺乏，应该进行实验室检验（如血清白蛋白测定）和皮肤迟发行性变态反应试验。 体质成分组成的检测（如皮褶厚度测量和生物电阻抗测定）通常用于评估体内脂肪的含量以诊断肥胖病。病史采集包括询问膳食情况、体重变化，营养缺乏的危险因素，以及各个系统相关的重点回顾（见表营养不良的症状和体征）。营养学家可以收集更详细的膳食情况信息，通常包括24小时内摄入的食物调查问卷，记录了摄入的食物内容和数量。通过膳食调查表，可以详细了解患者的体重以及摄入的食物性质和数量。表格营养缺乏症的临床症状和体征表格营养缺乏症的临床症状和体征部位/系统症状和体征缺乏的营养素一般表现消瘦总热liang皮肤皮疹多种维生素、锌、必需脂肪酸背侧皮疹烟酸（糙皮病）容易出血、瘀伤维生素C，维生素K毛发和指甲毛发稀疏或脱落蛋白质白发早现硒指甲外翻的杓样变铁眼睛夜盲维生素A角膜软化（角膜干燥和云翳）维生素A口腔唇炎和舌炎核黄素，烟酸，吡哆醛，铁齿龈出血维生素C，核黄素肢端水肿蛋白质神经系统感觉异常或手套样皮肤感觉麻木硫胺（脚气病）手足搐愵钙、镁感知缺失硫胺、烟酸、吡哆醛、维生素B12痴呆硫胺、烟酸、维生素B12骨骼肌系统肌肉萎缩蛋白质骨骼变型（例如O型腿、两腿向内弯曲、脊柱弯曲）维生素D、钙骨软化维生素D关节痛或水肿维生素C胃肠系统腹泻蛋白质、烟酸、叶酸、维生素 B12腹泻和味觉障碍锌吞咽困难或吞咽痛（由于Plummer-Vinson综合征导致）铁内分泌系统甲状腺肿大碘体格检查应该包括体重和身高的测定以及身体脂肪的部位分布。体质指数（BMI）--体重（kg）/身高（m）2, 根据身高校正体重(见表体质指数)， 此种评价方法比身高和体重表准确。婴儿、儿童以及青少年都有各自的生长和体重增加标准（见婴儿和儿童的身体发育）。表格体质指数（BMI）表格体质指数（BMI）体重类别（体质指数BMI)正常*（≥ 18.5 且 < 25）超重（≥ 25 且 < 30）肥胖：I级 （≥ 30 且 < 35）肥胖：II 级（≥ 35 且 < 40）肥胖：III级（≥40）高度体重60–61英寸（152–155厘米）97–127磅（44–58千克）128–153磅（58–69千克）153–180磅（69–82千克）179–206磅（81–93千克）> 206磅（> 93公斤）62–63英寸（157–160厘米）104–135磅（47–61千克）136–163磅（62–74千克）164–191磅（74–87千克）191–220磅（87–100公斤）> 220磅（> 100公斤）64–65英寸（162–165厘米）110–144磅（50–65千克）145–174磅（66–79千克）174–204磅（79–93千克）204–234磅（93–106千克）> 234磅（> 106公斤）66–67英寸（168–170厘米）118–153磅（54–69千克）155–185磅（70–84千克）186–217磅（84–98千克）216–249磅（98–113千克）> 249磅（> 113公斤）68–69英寸（173–175厘米）125–162磅（57–74公斤）164–196磅（74–89千克）197–230磅（89–104千克）230–263磅（104–119千克）> 263磅（> 119公斤）70–71英寸（178–180厘米）132–172磅（60–78千克）174–208磅（79–94千克）209–243磅（95–110千克）243–279磅（110–127公斤）> 279磅（> 127公斤）72–73英寸（183–185厘米）140–182磅（64–83千克）184–219磅（84–99千克）221–257磅（100–117千克）258–295磅（117–134千克）> 295磅（> 134公斤）74–75英寸（188–190厘米）148–192磅（67–87千克）194–232磅（88–105千克）233–272磅（106–123千克）272–311磅（123–141千克）> 311磅（> 141公斤）76英寸（193厘米）156–197磅（71–89千克）205–238磅（93–108千克）246–279磅（112–127千克）287～320磅（130～145千克）> 320磅（> 145公斤）*BMI值低于所列的正常值被认为是低体重。体内脂肪的部位分布也是评价的重要方面，躯干部分的脂肪堆积（表现为腰/臀比例>0.8）相比于其他部位的脂肪堆积，更多伴随着心脑血管疾病、高血压和糖尿病。对于BMI小于35的患者进行腰围的测量，有助于判断他们是否存在躯干型肥胖，也有助于预测糖尿病、高血压、高胆固醇血症和心血管疾病发生的危险性。如果男性的腰围大于102cm，女性的腰围大于88cm，上述疾病发生的危险性将大为增加。临床计算器体质指数（凯特勒指数）'}]}]\n", "{'name': 'Medical_Article', 'baseSelector': \"div[id^='container-']\", 'fields': [{'name': 'title', 'selector': 'h1#topicHeaderTitle', 'type': 'text'}, {'name': 'content', 'selector': \"div[data-testid='topic-main-content']\", 'type': 'text'}, {'name': 'sections', 'selector': 'section[id]', 'type': 'list', 'fields': [{'name': 'title', 'selector': 'h2', 'type': 'text'}, {'name': 'content', 'selector': 'div.TopicFHead_fHeadBody__mLB6h', 'type': 'text'}]}]}\n"]}], "source": ["\n", "\n", "url_schema, url_data = await generate_schema_from_url(\n", "    \"https://www.msdmanuals.cn/professional/nutritional-disorders/nutrition-general-considerations/nutrition-in-clinical-medicine\",\n", "    \"\"\"\n", "    文章标题\n", "<h1 id=\"topicHeaderTitle\"><span>文章标题</span></h1>\n", "\n", "文章内容：\n", "<div data-testid=\"topic-main-content\">\n", "文章内容\n", "<section>\n", "文章 section \n", "<h2>文章标题</h2>\n", "<div>\n", "文章section内容\n", "</div>\n", "</section>\n", "</div>\n", "\n", "我期望的JSON 数据结构：\n", "{\n", "  \"title\": \"文章标题\",\n", "  \"content\": \"文章内容\",\n", "  \"sections\": [\n", "    {\n", "      \"title\": \"文章标题\",\n", "      \"content\": \"文章section内容\"\n", "    }\n", "  ]\n", "}\n", "    \"\"\",\n", ")\n", "\n", "print(url_schema)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "8bc49fec", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}