{"cells": [{"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-07-14T16:10:29.382059Z", "start_time": "2025-07-14T16:10:29.053265Z"}}, "source": ["# PDF 体检报告解析功能演示\n", "# 本 notebook 演示如何使用火山引擎 PDF 解析 API 来处理体检报告 PDF 文件\n", "\n", "import sys\n", "import os\n", "\n", "# 添加项目根目录到 Python 路径\n", "current_dir = os.path.dirname(os.path.abspath(''))\n", "project_root = os.path.dirname(current_dir)\n", "sys.path.append(project_root)\n", "\n", "from app.services.report_ocr_handler import get_report_ocr_handler\n", "from app.core.config import settings\n", "import asyncio\n", "import json"], "outputs": [], "execution_count": 1}, {"cell_type": "code", "id": "b1435016", "metadata": {"ExecuteTime": {"end_time": "2025-07-14T16:10:31.054700Z", "start_time": "2025-07-14T16:10:31.052501Z"}}, "source": ["# 创建 PDF 处理器实例\n", "pdf_handler = get_report_ocr_handler()\n"], "outputs": [], "execution_count": 2}, {"cell_type": "code", "id": "6ad86126", "metadata": {"ExecuteTime": {"end_time": "2025-07-14T16:10:33.704608Z", "start_time": "2025-07-14T16:10:33.502111Z"}}, "source": ["# 示例：解析本地 PDF 文件（需要实际的 PDF 文件）\n", "# pdf_file_path = \"/Users/<USER>/work/ai-assistant/data/test1.pdf\"\n", "pdf_file_path = \"/Users/<USER>/Downloads/output_30_37-已压缩.pdf\"\n", "\n", "result = pdf_handler.parse_pdf_file(\n", "        file_path=pdf_file_path,\n", "        page_start=1,\n", "        page_num=30\n", "    )\n", "\n", "# # markdown, json 分别存储到本地\n", "# with open(\"tmp/markdown.md\", \"w\", encoding=\"utf-8\") as f:\n", "#     f.write(result[\"markdown\"])\n", "# with open(\"tmp/json.json\", \"w\", encoding=\"utf-8\") as f:\n", "#     f.write(json.dumps(result[\"detail\"], ensure_ascii=False))\n"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["正在解析 PDF 文件...\n", "\u001b[2m2025-07-15 00:10:33\u001b[0m [\u001b[31m\u001b[1merror    \u001b[0m] \u001b[1m火山引擎解析 API 调用失败, error=encoding without a string argument\u001b[0m\n", "\u001b[2m2025-07-15 00:10:33\u001b[0m [\u001b[31m\u001b[1merror    \u001b[0m] \u001b[1m文件解析失败                        \u001b[0m \u001b[36merror\u001b[0m=\u001b[35m'encoding without a string argument'\u001b[0m \u001b[36mfile_path\u001b[0m=\u001b[35m/Users/<USER>/Downloads/output_30_37-已压缩.pdf\u001b[0m\n"]}, {"ename": "Exception", "evalue": "encoding without a string argument", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mJSONDecodeError\u001b[0m                           Traceback (most recent call last)", "File \u001b[0;32m~/work/ai-assistant/.venv/lib/python3.12/site-packages/volcengine/visual/VisualService.py:180\u001b[0m, in \u001b[0;36mVisualService.common_handler\u001b[0;34m(self, api, form)\u001b[0m\n\u001b[1;32m    179\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 180\u001b[0m     res_json \u001b[38;5;241m=\u001b[39m \u001b[43mjson\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mloads\u001b[49m\u001b[43m(\u001b[49m\u001b[43mres\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    181\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m res_json\n", "File \u001b[0;32m~/.pyenv/versions/3.12.4/lib/python3.12/json/__init__.py:346\u001b[0m, in \u001b[0;36mloads\u001b[0;34m(s, cls, object_hook, parse_float, parse_int, parse_constant, object_pairs_hook, **kw)\u001b[0m\n\u001b[1;32m    343\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m (\u001b[38;5;28mcls\u001b[39m \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m object_hook \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m\n\u001b[1;32m    344\u001b[0m         parse_int \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m parse_float \u001b[38;5;129;01<PERSON>\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m\n\u001b[1;32m    345\u001b[0m         parse_constant \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m object_pairs_hook \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m kw):\n\u001b[0;32m--> 346\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_default_decoder\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdecode\u001b[49m\u001b[43m(\u001b[49m\u001b[43ms\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    347\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mcls\u001b[39m \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "File \u001b[0;32m~/.pyenv/versions/3.12.4/lib/python3.12/json/decoder.py:337\u001b[0m, in \u001b[0;36mJSONDecoder.decode\u001b[0;34m(self, s, _w)\u001b[0m\n\u001b[1;32m    333\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Return the Python representation of ``s`` (a ``str`` instance\u001b[39;00m\n\u001b[1;32m    334\u001b[0m \u001b[38;5;124;03mcontaining a JSON document).\u001b[39;00m\n\u001b[1;32m    335\u001b[0m \n\u001b[1;32m    336\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m--> 337\u001b[0m obj, end \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mraw_decode\u001b[49m\u001b[43m(\u001b[49m\u001b[43ms\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43midx\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m_w\u001b[49m\u001b[43m(\u001b[49m\u001b[43ms\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mend\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    338\u001b[0m end \u001b[38;5;241m=\u001b[39m _w(s, end)\u001b[38;5;241m.\u001b[39mend()\n", "File \u001b[0;32m~/.pyenv/versions/3.12.4/lib/python3.12/json/decoder.py:355\u001b[0m, in \u001b[0;36mJSONDecoder.raw_decode\u001b[0;34m(self, s, idx)\u001b[0m\n\u001b[1;32m    354\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mStopIteration\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n\u001b[0;32m--> 355\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m JSONDecodeError(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mExpecting value\u001b[39m\u001b[38;5;124m\"\u001b[39m, s, err\u001b[38;5;241m.\u001b[39mvalue) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mN<PERSON>\u001b[39;00m\n\u001b[1;32m    356\u001b[0m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m obj, end\n", "\u001b[0;31mJSONDecodeError\u001b[0m: Expecting value: line 1 column 1 (char 0)", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31mException\u001b[0m                                 Traceback (most recent call last)", "File \u001b[0;32m~/work/ai-assistant/.venv/lib/python3.12/site-packages/volcengine/visual/VisualService.py:944\u001b[0m, in \u001b[0;36mVisualService.ocr_pdf\u001b[0;34m(self, form)\u001b[0m\n\u001b[1;32m    943\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 944\u001b[0m     res_json \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcommon_handler\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mOCRPdf\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mform\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    945\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m res_json\n", "File \u001b[0;32m~/work/ai-assistant/.venv/lib/python3.12/site-packages/volcengine/visual/VisualService.py:183\u001b[0m, in \u001b[0;36mVisualService.common_handler\u001b[0;34m(self, api, form)\u001b[0m\n\u001b[1;32m    182\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m:\n\u001b[0;32m--> 183\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m(\u001b[38;5;28mstr\u001b[39m(e))\n", "\u001b[0;31mException\u001b[0m: encoding without a string argument", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31mException\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[3], line 6\u001b[0m\n\u001b[1;32m      3\u001b[0m pdf_file_path \u001b[38;5;241m=\u001b[39m  \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m/Users/<USER>/Downloads/output_30_37-已压缩.pdf\u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[1;32m      5\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m正在解析 PDF 文件...\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m----> 6\u001b[0m result \u001b[38;5;241m=\u001b[39m \u001b[43mpdf_handler\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mparse_pdf_file\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m      7\u001b[0m \u001b[43m    \u001b[49m\u001b[43mfile_path\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mpdf_file_path\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m      8\u001b[0m \u001b[43m    \u001b[49m\u001b[43mpage_start\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m      9\u001b[0m \u001b[43m    \u001b[49m\u001b[43mpage_num\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m30\u001b[39;49m\n\u001b[1;32m     10\u001b[0m \u001b[43m)\u001b[49m\n\u001b[1;32m     12\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m✓ PDF 解析成功！\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     13\u001b[0m \u001b[38;5;28mprint\u001b[39m(result)\n", "File \u001b[0;32m~/work/ai-assistant/app/services/report_ocr_handler.py:73\u001b[0m, in \u001b[0;36mReportOCRHandler.parse_pdf_file\u001b[0;34m(self, file_path, page_start, page_num)\u001b[0m\n\u001b[1;32m     69\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39;00m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m文件大小超过 8MB 限制\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     71\u001b[0m     file_base64 \u001b[38;5;241m=\u001b[39m base64\u001b[38;5;241m.\u001b[39mb64encode(file_content)\u001b[38;5;241m.\u001b[39mdecode(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mutf-8\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m---> 73\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_parse_with_base64\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfile_base64\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpage_start\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpage_num\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfile_type\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     75\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m     76\u001b[0m     logger\u001b[38;5;241m.\u001b[39merror(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m文件解析失败\u001b[39m\u001b[38;5;124m\"\u001b[39m, file_path\u001b[38;5;241m=\u001b[39mfile_path, error\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mstr\u001b[39m(e))\n", "File \u001b[0;32m~/work/ai-assistant/app/services/report_ocr_handler.py:112\u001b[0m, in \u001b[0;36mReportOCRHandler._parse_with_base64\u001b[0;34m(self, file_base64, page_start, page_num, file_type)\u001b[0m\n\u001b[1;32m    100\u001b[0m form \u001b[38;5;241m=\u001b[39m {\n\u001b[1;32m    101\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mimage_base64\u001b[39m\u001b[38;5;124m\"\u001b[39m: file_base64,\n\u001b[1;32m    102\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mimage_url\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    108\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfile_type\u001b[39m\u001b[38;5;124m\"\u001b[39m: file_type\n\u001b[1;32m    109\u001b[0m }\n\u001b[1;32m    111\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 112\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mservice\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mocr_pdf\u001b[49m\u001b[43m(\u001b[49m\u001b[43mform\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    113\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_process_response(response)\n\u001b[1;32m    114\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n", "File \u001b[0;32m~/work/ai-assistant/.venv/lib/python3.12/site-packages/volcengine/visual/VisualService.py:947\u001b[0m, in \u001b[0;36mVisualService.ocr_pdf\u001b[0;34m(self, form)\u001b[0m\n\u001b[1;32m    945\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m res_json\n\u001b[1;32m    946\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m--> 947\u001b[0m     \u001b[38;5;28;01m<PERSON>se\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m(\u001b[38;5;28mstr\u001b[39m(e))\n", "\u001b[0;31mException\u001b[0m: encoding without a string argument"]}], "execution_count": 3}, {"cell_type": "code", "execution_count": null, "id": "e02eef03", "metadata": {}, "outputs": [], "source": []}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-14T14:44:25.170024Z", "start_time": "2025-07-14T14:44:21.863228Z"}}, "cell_type": "code", "source": ["from pdf2image import convert_from_path\n", "\n", "pages = convert_from_path(\n", "    'abc.pdf',\n", "    first_page=30,\n", "    last_page=37\n", ")\n", "\n", "for i, page in enumerate(pages):\n", "    page.save(f'page_{i}.png', 'PNG')\n"], "id": "7e4a31ffaf1ca64a", "outputs": [], "execution_count": 6}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-14T16:45:44.486628Z", "start_time": "2025-07-14T16:45:43.807345Z"}}, "cell_type": "code", "source": ["# 按页范围切割 PDF（如 30~37），页码从1开始计数\n", "from PyPDF2 import PdfReader, PdfWriter\n", "\n", "\n", "input_pdf = 'abc.pdf'\n", "output_pdf = 'split-abc.pdf'\n", "start_page = 31  # 起始页（包含）\n", "end_page = 37    # 结束页（包含）\n", "\n", "reader = PdfReader(input_pdf)\n", "writer = PdfWriter()\n", "\n", "# 注意：PyPDF2 的页码是从0开始的\n", "for i in range(start_page - 1, end_page):\n", "    writer.add_page(reader.pages[i])\n", "\n", "with open(output_pdf, \"wb\") as f:\n", "    writer.write(f)\n", "\n", "print(f\"已将第{start_page}到{end_page}页保存为 {output_pdf}\")\n"], "id": "a0a6eaf1fc1f8358", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["已将第31到37页保存为 /Users/<USER>/Downloads/output_下-30_37.pdf\n"]}], "execution_count": 4}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}