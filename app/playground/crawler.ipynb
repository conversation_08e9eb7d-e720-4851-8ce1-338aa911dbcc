{"cells": [{"cell_type": "code", "execution_count": 3, "id": "733ebf78bcb12247", "metadata": {"ExecuteTime": {"end_time": "2025-06-16T02:56:29.607127Z", "start_time": "2025-06-16T02:56:29.604362Z"}}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080\">INIT</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080\">.... → Crawl4AI </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.6</span><span style=\"color: #008080; text-decoration-color: #008080\">.</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m[\u001b[0m\u001b[36mINIT\u001b[0m\u001b[1;36m]\u001b[0m\u001b[36m...\u001b[0m\u001b[36m. → Crawl4AI \u001b[0m\u001b[1;36m0.6\u001b[0m\u001b[36m.\u001b[0m\u001b[1;36m3\u001b[0m\u001b[36m \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">FETCH</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">... ↓ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://www.msdmanuals.cn/professional/nutrition...ion-general-considerations/overview-of-nutrition</span><span style=\"color: #008000; text-decoration-color: #008000\">  |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">01s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mFETCH\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m...\u001b[0m\u001b[32m ↓ \u001b[0m\u001b[4;32mhttps://www.msdmanuals.cn/professional/nutrition...ion-general-considerations/overview-of-nutrition\u001b[0m\u001b[32m  |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m01s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">SCRAPE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">.. ◆ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://www.msdmanuals.cn/professional/nutrition...ion-general-considerations/overview-of-nutrition</span><span style=\"color: #008000; text-decoration-color: #008000\">  |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">08s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mSCRAPE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m.. ◆ \u001b[0m\u001b[4;32mhttps://www.msdmanuals.cn/professional/nutrition...ion-general-considerations/overview-of-nutrition\u001b[0m\u001b[32m  |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m08s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">COMPLETE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> ● </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://www.msdmanuals.cn/professional/nutrition...ion-general-considerations/overview-of-nutrition</span><span style=\"color: #008000; text-decoration-color: #008000\">  |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">09s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mCOMPLETE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m ● \u001b[0m\u001b[4;32mhttps://www.msdmanuals.cn/professional/nutrition...ion-general-considerations/overview-of-nutrition\u001b[0m\u001b[32m  |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m09s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}], "source": ["from crawl4ai import AsyncWebCrawler\n", "from crawl4ai import CrawlerRunConfig\n", "\n", "# 在浏览器中启用详细日志记录\n", "# browser_config = BrowserConfig(verbose=True)\n", "\n", "run_config = CrawlerRunConfig(\n", ")\n", "\n", "url = \"https://www.msdmanuals.cn/professional/nutritional-disorders/nutrition-general-considerations/overview-of-nutrition\"\n", "\n", "async with AsyncWebCrawler() as crawler:\n", "    result = await crawler.arun(url=url, config=run_config)\n", "\n", "    if not result.success:\n", "        print(f\"Crawl failed: {result.error_message}\")\n", "        print(f\"Status code: {result.status_code}\")\n", "    else:\n", "        print(result.markdown.fit_markdown)\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 38, "id": "aa269e2f", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080\">INIT</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080\">.... → Crawl4AI </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.6</span><span style=\"color: #008080; text-decoration-color: #008080\">.</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m[\u001b[0m\u001b[36mINIT\u001b[0m\u001b[1;36m]\u001b[0m\u001b[36m...\u001b[0m\u001b[36m. → Crawl4AI \u001b[0m\u001b[1;36m0.6\u001b[0m\u001b[36m.\u001b[0m\u001b[1;36m3\u001b[0m\u001b[36m \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">FETCH</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">... ↓ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://www.msdmanuals.cn/professional/pediatric...l-and-genitourinary-anomalies/ureteral-anomalies</span><span style=\"color: #008000; text-decoration-color: #008000\">  |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">00s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mFETCH\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m...\u001b[0m\u001b[32m ↓ \u001b[0m\u001b[4;32mhttps://www.msdmanuals.cn/professional/pediatric...l-and-genitourinary-anomalies/ureteral-anomalies\u001b[0m\u001b[32m  |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m00s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">SCRAPE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">.. ◆ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://www.msdmanuals.cn/professional/pediatric...l-and-genitourinary-anomalies/ureteral-anomalies</span><span style=\"color: #008000; text-decoration-color: #008000\">  |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">03s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mSCRAPE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m.. ◆ \u001b[0m\u001b[4;32mhttps://www.msdmanuals.cn/professional/pediatric...l-and-genitourinary-anomalies/ureteral-anomalies\u001b[0m\u001b[32m  |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m03s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">COMPLETE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> ● </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://www.msdmanuals.cn/professional/pediatric...l-and-genitourinary-anomalies/ureteral-anomalies</span><span style=\"color: #008000; text-decoration-color: #008000\">  |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">04s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mCOMPLETE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m ● \u001b[0m\u001b[4;32mhttps://www.msdmanuals.cn/professional/pediatric...l-and-genitourinary-anomalies/ureteral-anomalies\u001b[0m\u001b[32m  |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m04s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Raw Markdown length: 2846\n", "Fit Markdown length: 2688\n", "输尿管异常经常并发于[肾脏畸形](https://www.msdmanuals.cn/professional/pediatrics/congenital-renal-and-genitourinary-anomalies/renal-anomalies)，也可独立发生。输尿管异常的合并症包括：\n", "  * 梗阻、[膀胱输尿管反流](https://www.msdmanuals.cn/professional/pediatrics/congenital-renal-and-genitourinary-anomalies/vesicoureteral-reflux-vur)、[尿路感染](https://www.msdmanuals.cn/professional/pediatrics/miscellaneous-bacterial-infections-in-infants-and-children/urinary-tract-infection-uti-in-children)和[尿结石](https://www.msdmanuals.cn/professional/genitourinary-disorders/urinary-calculi/urinary-calculi)形成（由于尿潴留）\n", "  * [尿失禁](https://www.msdmanuals.cn/professional/pediatrics/urinary-incontinence-in-children/urinary-incontinence-in-children)（因输尿管异位开口于尿道，会阴和阴道而出现）\n", "\n", "\n", "常规产前超声检查发现的泌尿道异常（例如肾积水）可能提示子宫内存在输尿管异常。在新生儿或儿童中，偶尔会通过体格检查发现异常（例如，发现外部异位输尿管口或可触及的肿块）。\n", "在儿童出现肾盂肾炎发作或反复[尿路感染](https://www.msdmanuals.cn/professional/pediatrics/miscellaneous-bacterial-infections-in-infants-and-children/urinary-tract-infection-uti-in-children)、女孩持续尿失禁或男孩反复发作[睾丸炎](https://www.msdmanuals.cn/professional/genitourinary-disorders/penile-and-scrotal-disorders/orchitis)的情况下，应怀疑输尿管异常。测试通常需要超声检查肾脏，输尿管，膀胱和排尿后，再行荧光排尿膀胱尿道造影。磁共振尿路造影可用于识别与无功能肾段相关的异位输尿管 ()。\n", "输尿管异常通过手术治疗。\n", "## 参考文献\n", "  1. 1.[<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, et al](https://www.sciencedirect.com/science/article/pii/S2214854X22000668): Ectopic ureter: A concise narrative review with anatomical and clinical commentaries._Trans Res Anat_.2022;29:100220.doi:10.1016/j.tria.2022.100220.\n", "\n", "\n", "##  输尿管开口异位\n", "重复的单侧或双侧输尿管可异位开口于膀胱侧壁、膀胱三角远端、膀胱颈部、女性尿道括约肌远端的尿道（导致持续尿失禁）、生殖系统（男性为前列腺、精囊，女性为子宫、阴道）或外阴。侧位输尿管异位开口常出现[膀胱输尿管反流](https://www.msdmanuals.cn/professional/pediatrics/congenital-renal-and-genitourinary-anomalies/vesicoureteral-reflux-vur)，输尿管异位开口于尾侧端常合并梗阻或尿失禁。出现梗阻、尿失禁是外科手术的指征，有时膀胱输尿管反流也需手术。\n", "## 输尿管位于腔静脉后\n", "腔静脉异常发育（输尿管前腔静脉）使肾脏平面以下的腔静脉位于输尿管前方（常见于右侧）。\n", "左侧腔静脉后输尿管仅见于左侧大静脉系统持续存在或完全内脏反位的情况，腔静脉后输尿管可导致输尿管梗阻。若梗阻明显，可进行输尿管离断，对于这些患者需于腔静脉或髂血管前再行吻合术。\n", "## 输尿管重复畸形\n", "单侧或双侧输尿管的不完全型（部分）或完全型重复畸形可能与同侧肾盂重复畸形相伴发生。\n", "对于完全重复，肾上极的输尿管开口较之肾下极开口，更靠背侧。其结果是，下极的输尿管容易回流，而上极容易阻碍时病理存在。可能发生一个或两个开孔的异位或狭窄，膀胱输尿管返流进入输尿管下段或双侧输尿管，以及[输尿管囊肿](https://www.msdmanuals.cn/professional/pediatrics/congenital-renal-and-genitourinary-anomalies/ureteral-anomalies#v8518726_zh)。手术是治疗膀胱输尿管反流、输尿管梗阻及尿失禁的主要手段。\n", "不完全肾重复几乎无临床意义。\n", "##  输尿管狭窄\n", "输尿管狭窄可发生在任何部位，最常见在肾盂输尿管连接处，其次为输尿管膀胱连接处（原发性巨输尿管），产生感染、血尿和梗阻。并发症包括感染，血尿，梗阻。随着孩子的成长，狭窄通常会改善。\n", "当原发性巨输尿管伴有输尿管扩张加重、感染和梗阻时，可能需行输尿管重建（缩小管径）及再植手术。在肾盂输尿管连接部梗阻部位，可通过开放手术，腹腔镜手术，或机器人技术进行肾盂成形术（梗阻段切除并重新吻合）。\n", "## 输尿管囊肿\n", "膀胱内输尿管末端轻微梗阻所致的囊肿脱垂可导致进行性输尿管梗阻、扩张、肾盂积水、感染、偶尔形成结石以及肾功能受损。输尿管囊肿的治疗方法包括内镜下经尿道切开和切开修补。\n", "当输尿管囊肿累及两条双输尿管的上部时，治疗取决于肾段的功能（通常发育异常）。如果受影响的肾段和输尿管没有功能或怀疑有严重的肾脏发育不良，则切除受影响的肾脏段和输尿管可能优于梗阻修复。或者，可进行同侧输尿管输尿管吻合术以绕过梗阻。\n", "在极少数情况下，输尿管可能脱出超过膀胱颈，引起膀胱出口梗阻。在女孩中，这种脱垂可能表现为阴唇间包块。\n", "\n"]}], "source": ["from crawl4ai import AsyncWebCrawler\n", "from crawl4ai import CacheMode\n", "from crawl4ai import CrawlerRunConfig\n", "from crawl4ai.content_filter_strategy import PruningContentFilter\n", "from crawl4ai.markdown_generation_strategy import DefaultMarkdownGenerator\n", "\n", "md_generator = DefaultMarkdownGenerator(\n", "    content_filter=PruningContentFilter(\n", "        threshold=0.2, threshold_type=\"fixed\"\n", "    )\n", ")\n", "\n", "# config = CrawlerRunConfig(cache_mode=CacheMode.BYPASS, markdown_generator=md_generator)\n", "\n", "config = CrawlerRunConfig(\n", "            cache_mode=CacheMode.BYPASS,\n", "            markdown_generator=md_generator,\n", "            # 指定多个目标元素\n", "            target_elements=[\"div[data-testid='topic-main-content']\"],\n", "            # 排除这些标签\n", "            excluded_tags=[\"nav\", \"footer\", \"aside\", \"script\", \"style\", \"img\"],\n", "            exclude_external_links=True,\n", "            exclude_external_images=True,\n", "        )\n", "\n", "\n", "\n", "async with AsyncWebCrawler() as crawler:\n", "    result = await crawler.arun(\n", "        \"https://www.msdmanuals.cn/professional/pediatrics/congenital-renal-and-genitourinary-anomalies/ureteral-anomalies\",\n", "        config=config,\n", "    )\n", "    print(\"Raw Markdown length:\", len(result.markdown.raw_markdown))\n", "    print(\"Fit Markdown length:\", len(result.markdown.fit_markdown))\n", "    print(result.markdown.fit_markdown)\n"]}, {"cell_type": "code", "execution_count": 4, "id": "c1203135", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<crawl4ai.extraction_strategy.JsonCssExtractionStrategy object at 0x1134727e0>\n"]}], "source": ["import os\n", "\n", "from crawl4ai import LLMConfig\n", "from crawl4ai.extraction_strategy import JsonCssExtractionStrategy\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()\n", "\n", "GOOGLE_API_KEY = os.getenv(\"GOOGLE_API_KEY\")\n", "\n", "# Generate a schema (one-time cost)\n", "html = \"\"\"\n", "body > div.wrapper > main > div > div.page-body\n", "\"\"\"\n", "\n", "# Using OpenAI (requires API token)\n", "schema = JsonCssExtractionStrategy.generate_schema(\n", "    html,\n", "    llm_config=LLMConfig(provider=\"gemini/gemini-2.0-flash\", api_token=GOOGLE_API_KEY),\n", ")\n", "\n", "# Use the schema for fast, repeated extractions\n", "strategy = JsonCssExtractionStrategy(schema)\n", "print(strategy)\n"]}, {"cell_type": "code", "execution_count": null, "id": "a0ff91f2", "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "\n", "def clean_markdown(text):\n", "    # 删除作者信息\n", "    text = re.sub(r'^作者：.*$', '', text, flags=re.MULTILINE)\n", "\n", "    # 删除审核信息\n", "    text = re.sub(r'^Reviewed By.*$', '', text, flags=re.MULTILINE)\n", "\n", "    # 删除修订信息\n", "    text = re.sub(r'^已审核/已修订.*$', '', text, flags=re.MULTILINE)\n", "\n", "    # 删除主题资源部分\n", "    text = re.sub(r'^## 主题资源$\\n(?:\\s*\\*.*$\\n)*', '', text, flags=re.MULTILINE)\n", "\n", "    # 删除测试知识部分\n", "    text = re.sub(r'^Test your Knowledge.*$', '', text, flags=re.MULTILINE)\n", "\n", "    # 清理多余的空行\n", "    text = re.sub(r'\\n\\s*\\n\\s*\\n', '\\n\\n', text)\n", "\n", "    return text.strip()"]}, {"cell_type": "code", "execution_count": 29, "id": "e7f9c307", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["已保存清理后的文件到: /Users/<USER>/work/ai-assistant/test_crawled_pages/clean.md\n"]}], "source": ["# 读取文件\n", "import os\n", "from pathlib import Path\n", "\n", "# 获取当前工作目录\n", "current_dir = Path(os.getcwd())\n", "file_path = current_dir.parent.parent / \"test_crawled_pages/临床营养_-_营养性疾病_-_MSD诊疗手册专业版_1.md\"\n", "\n", "# 读取原文件并清理\n", "with open(file_path, encoding=\"utf-8\") as f:\n", "    text = f.read()\n", "    cleaned_text = clean_markdown(text)\n", "\n", "# 保存清理后的文件\n", "clean_file_path = current_dir.parent.parent / \"test_crawled_pages/clean.md\"\n", "with open(clean_file_path, \"w\", encoding=\"utf-8\") as f:\n", "    f.write(cleaned_text)\n", "print(f\"已保存清理后的文件到: {clean_file_path}\")"]}, {"cell_type": "code", "execution_count": null, "id": "4b332b0e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "98961e39", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}