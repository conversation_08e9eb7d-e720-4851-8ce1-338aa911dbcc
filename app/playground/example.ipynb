{"cells": [{"cell_type": "code", "execution_count": 3, "id": "f84810a1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Excel文件已生成!\n"]}], "source": ["import pandas as pd\n", "import json\n", "import os\n", "\n", "# 读取 JSON 文件\n", "json_path = os.path.expanduser(\"~/Downloads/code2.json\")\n", "with open(json_path, \"r\", encoding=\"utf-8\") as f:\n", "    data = json.load(f)\n", "\n", "# 展开数据为扁平结构\n", "rows = []\n", "for pian in data[\"篇\"]:\n", "    pian_name = pian[\"名称\"]\n", "    for zhang in pian[\"章节\"]:\n", "        zhang_name = zhang[\"章名\"]\n", "        if not zhang[\"节\"]:\n", "            rows.append([pian_name, zhang_name, \"\"])\n", "        else:\n", "            for jie in zhang[\"节\"]:\n", "                rows.append([pian_name, zhang_name, jie])\n", "\n", "# 创建DataFrame\n", "df = pd.DataFrame(rows, columns=[\"篇名\", \"章名\", \"节名\"])\n", "\n", "# 保存为Excel\n", "df.to_excel(\"医学教材目录-下册.xlsx\", index=False)\n", "\n", "print(\"Excel文件已生成!\")\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}