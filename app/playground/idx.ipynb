{"cells": [{"metadata": {"ExecuteTime": {"end_time": "2025-07-15T08:21:21.421311Z", "start_time": "2025-07-15T08:21:21.419776Z"}}, "cell_type": "code", "source": "file_path = '../../data/用户体检检查项目清单.xlsx'", "id": "c3978326d72c3df4", "outputs": [], "execution_count": 4}, {"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-07-15T08:22:28.875970Z", "start_time": "2025-07-15T08:22:28.825599Z"}}, "source": ["import pandas as pd\n", "import json\n", "import pandas as pd\n", "import json\n", "import numpy as np\n", "\n", "def parse_medical_exam_excel(excel_file_path, output_json_path):\n", "    \"\"\"\n", "    解析医疗检查Excel文件并生成结构化JSON\n", "\n", "    Args:\n", "        excel_file_path: Excel文件路径\n", "        output_json_path: 输出JSON文件路径\n", "    \"\"\"\n", "\n", "    # 读取Excel文件\n", "    df = pd.read_excel(excel_file_path, header=None)\n", "\n", "    # 初始化结果字典\n", "    result = {\n", "        \"项目\": \"科室检查\",\n", "        \"检查项目\": []\n", "    }\n", "\n", "    # 定义检查项目结构\n", "    current_category = None\n", "    current_items = []\n", "\n", "    # 遍历每一行\n", "    for idx, row in df.iterrows():\n", "        # 跳过标题行\n", "        if idx == 0:\n", "            continue\n", "\n", "        # 获取各列数据\n", "        col_a = str(row[0]) if pd.notna(row[0]) else \"\"\n", "        col_b = str(row[1]) if pd.notna(row[1]) else \"\"\n", "        col_c = str(row[2]) if pd.notna(row[2]) else \"\"\n", "        col_d = str(row[3]) if pd.notna(row[3]) else \"\"\n", "        col_e = str(row[4]) if pd.notna(row[4]) else \"\"\n", "\n", "        # 判断是否为新的检查类别\n", "        if col_a and col_a not in [\"nan\", \"\"] and \"检查\" in col_a:\n", "            # 保存之前的类别数据\n", "            if current_category and current_items:\n", "                result[\"检查项目\"].append({\n", "                    \"类别\": current_category,\n", "                    \"项目列表\": current_items\n", "                })\n", "\n", "            # 开始新类别\n", "            current_category = col_a\n", "            current_items = []\n", "\n", "        # 解析检查项目\n", "        if col_b and col_b not in [\"nan\", \"\"]:\n", "            item = {\n", "                \"名称\": col_b.strip(),\n", "                \"指标\": [],\n", "                \"适用性别\": {\n", "                    \"男\": \"√\" in col_d or col_d == \"√\",\n", "                    \"女\": \"√\" in col_e or col_e == \"√\"\n", "                }\n", "            }\n", "\n", "            # 解析指标（在col_c中）\n", "            if col_c and col_c not in [\"nan\", \"\"]:\n", "                # 分割多个指标\n", "                indicators = col_c.split('\\n')\n", "                for indicator in indicators:\n", "                    if indicator.strip():\n", "                        # 解析指标格式（如：收缩压 - mmHg）\n", "                        if ' - ' in indicator or ' － ' in indicator:\n", "                            parts = indicator.replace(' － ', ' - ').split(' - ')\n", "                            if len(parts) == 2:\n", "                                item[\"指标\"].append({\n", "                                    \"名称\": parts[0].strip(),\n", "                                    \"单位\": parts[1].strip()\n", "                                })\n", "                        else:\n", "                            item[\"指标\"].append({\n", "                                \"名称\": indicator.strip(),\n", "                                \"单位\": \"无单位\"\n", "                            })\n", "\n", "            current_items.append(item)\n", "\n", "    # 添加最后一个类别\n", "    if current_category and current_items:\n", "        result[\"检查项目\"].append({\n", "            \"类别\": current_category,\n", "            \"项目列表\": current_items\n", "        })\n", "\n", "    # 处理特殊的实验室检查部分\n", "    lab_tests = []\n", "    lab_test_started = False\n", "\n", "    for idx, row in df.iterrows():\n", "        col_a = str(row[0]) if pd.notna(row[0]) else \"\"\n", "        col_c = str(row[2]) if pd.notna(row[2]) else \"\"\n", "\n", "        if \"实验室检查\" in col_a:\n", "            lab_test_started = True\n", "            continue\n", "\n", "        if lab_test_started and col_c and col_c not in [\"nan\", \"\"] and (\"HP\" in col_c or \"LP\" in col_c):\n", "            # 解析实验室检查指标\n", "            indicators = col_c.split('\\n')\n", "            for indicator in indicators:\n", "                if indicator.strip() and ' - ' in indicator:\n", "                    parts = indicator.replace(' － ', ' - ').split(' - ')\n", "                    if len(parts) == 2:\n", "                        lab_tests.append({\n", "                            \"名称\": parts[0].strip(),\n", "                            \"类型\": parts[1].strip()\n", "                        })\n", "\n", "    if lab_tests:\n", "        result[\"检查项目\"].append({\n", "            \"类别\": \"实验室检查\",\n", "            \"项目列表\": lab_tests\n", "        })\n", "\n", "    # 保存为JSON文件\n", "    with open(output_json_path, 'w', encoding='utf-8') as f:\n", "        json.dump(result, f, ensure_ascii=False, indent=2)\n", "\n", "    print(f\"JSON文件已生成：{output_json_path}\")\n", "    return result\n", "\n", "# 使用示例\n", "if __name__ == \"__main__\":\n", "    # 替换为您的实际文件路径\n", "    excel_file = file_path\n", "    output_file = \"medical_exam_structured.json\"\n", "\n", "    # 执行解析\n", "    result = parse_medical_exam_excel(excel_file, output_file)\n", "\n", "    # 打印部分结果预览\n", "    print(\"\\n生成的JSON结构预览：\")\n", "    print(json.dumps(result, ensure_ascii=False, indent=2)[:1000] + \"...\")\n"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["JSON文件已生成：medical_exam_structured.json\n", "\n", "生成的JSON结构预览：\n", "{\n", "  \"项目\": \"科室检查\",\n", "  \"检查项目\": [\n", "    {\n", "      \"类别\": \"一般检查 A\",\n", "      \"项目列表\": [\n", "        {\n", "          \"名称\": \"身高、体重、体重指数、血压（收缩压、舒张压）\",\n", "          \"指标\": [\n", "            {\n", "              \"名称\": \"收缩压\",\n", "              \"单位\": \"mmHg\"\n", "            },\n", "            {\n", "              \"名称\": \"舒张压\",\n", "              \"单位\": \"mmHg\"\n", "            }\n", "          ],\n", "          \"适用性别\": {\n", "            \"男\": true,\n", "            \"女\": true\n", "          }\n", "        },\n", "        {\n", "          \"名称\": \"病史、家族史、心率、心律、心音、肺部听诊、肝脏触诊、脾脏触诊及诊断、神经系统（膝反射、内科、胃征）\",\n", "          \"指标\": [\n", "            {\n", "              \"名称\": \"心音\",\n", "              \"单位\": \"无单位\"\n", "            },\n", "            {\n", "              \"名称\": \"心率\",\n", "              \"单位\": \"次/分\"\n", "            },\n", "            {\n", "              \"名称\": \"心律\",\n", "              \"单位\": \"无单位\"\n", "            },\n", "            {\n", "              \"名称\": \"心脏杂音\",\n", "              \"单位\": \"无单位\"\n", "            },\n", "            {\n", "              \"名称\": \"胸廓外形\",\n", "              \"单位\": \"无单位\"\n", "            },\n", "            {\n", "              \"名称\": \"肺\",\n", "              \"单位\": \"无单位\"\n", "            },\n", "    ...\n"]}], "execution_count": 5}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "65c7cc423753a929"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}