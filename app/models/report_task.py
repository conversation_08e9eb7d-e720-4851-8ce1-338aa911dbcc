"""
报告任务相关数据库模型
"""

from datetime import datetime
from decimal import Decimal
import enum

from sqlalchemy import (
    BigInteger,
    Column,
    DateTime,
    Enum,
    ForeignKey,
    Index,
    Integer,
    Numeric,
    String,
    Text,
)
from sqlalchemy.orm import relationship

from .base import Base


class TaskStatus(enum.Enum):
    """任务状态枚举"""

    # 0. 初始化
    PENDING = "pending"                         # 待处理

    # 1. OCR 处理
    OCR_PROCESSING = "ocr_doing"           # OCR处理中
    OCR_COMPLETED = "ocr_done"             # OCR完成

    # 2.报告 AI分析（meta, tabular, overall, diseases, image, 可以并行）
    META_PROCESSING = "meta_doing"         # 个人信息处理中
    META_COMPLETED = "meta_done"           # 个人信息完成
    TABULAR_PROCESSING = "tabular_doing"   # 检查项处理中
    TABULAR_COMPLETED = "tabular_done"     # 检查项完成
    OVERALL_PROCESSING = "overall_doing"   # 总检、健康建议处理中
    OVERALL_COMPLETED = "overall_done"     # 总检、健康建议完成
    IMAGE_PROCESSING = "image_doing"       # 影像数据处理中
    IMAGE_COMPLETED = "image_done"         # 影像数据完成
    EXISTING_DISEASES_PROCESSING = "diseases_doing"   # 疾病提取处理中
    EXISTING_DISEASES_COMPLETED = "diseases_done"     # 疾病提取完成

    # 3.报告 AI分析结果处理
    # 3.1 所有 AI报告分析结果都需要进行多语言处理
    TRANSLATION_PROCESSING = "translation_doing"   # 多语言翻译处理中
    TRANSLATION_COMPLETED = "translation_done"     # 多语言翻译完成

    # 3.2 只有检查项指标需要进行指标转换
    INDICATOR_PROCESSING = "indicator_doing"       # 指标转换处理中
    INDICATOR_COMPLETED = "indicator_done"         # 指标转换完成

    # 4. 处理后的所有数据调用业务接口、存储到向量数据库
    CALL_API = "call_api"  # 调用业务接口
    CALL_API_COMPLETED = "call_api_done"  # 调用业务接口完成
    STORE_VECTOR = "store_vector"  # 存储到向量数据库
    STORE_VECTOR_COMPLETED = "store_vector_done"  # 存储到向量数据库完成

    # 5.整体任务情况
    COMPLETED = "completed"  # 全部完成
    FAILED = "failed"  # 失败, 部分失败或全部失败


class LogStatus(enum.Enum):
    """日志状态枚举"""

    STARTED = "started"  # 开始
    COMPLETED = "completed"  # 完成
    FAILED = "failed"  # 失败


class ReportTask(Base):
    """报告任务表"""

    __tablename__ = "report_tasks"

    # 主键和业务标识
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    report_id = Column(String(64), unique=True, nullable=False, comment="报告唯一标识")
    report_key = Column(String(255), nullable=False, comment="TOS文件Key")
    workflow_id = Column(String(100), nullable=True, comment="Celery工作流ID")

    # 任务状态
    status = Column(Enum(TaskStatus), default=TaskStatus.PENDING, nullable=False, comment="任务状态")
    current_step = Column(Integer, default=0, comment="当前步骤(0-6)")
    total_steps = Column(Integer, default=6, comment="总步骤数")
    progress_percent = Column(Numeric(5, 2), default=Decimal("0.00"), comment="进度百分比")

    # OCR结果存储
    ocr_result_key = Column(String(255), nullable=True, comment="OCR结果TOS Key")
    ocr_error = Column(Text, nullable=True, comment="OCR错误信息")

    # Agent分析结果存储
    meta_result = Column(Text, nullable=True, comment="个人信息分析结果(JSON)")
    overall_result = Column(Text, nullable=True, comment="总体结果分析(JSON)")
    tabular_result = Column(Text, nullable=True, comment="检查项指标分析(JSON)")
    image_result = Column(Text, nullable=True, comment="影像数据分析(JSON)")

    # 最终输出
    final_json_path = Column(String(500), nullable=True, comment="最终JSON文件路径")

    # 错误信息
    error_message = Column(Text, nullable=True, comment="错误信息")
    error_step = Column(String(50), nullable=True, comment="出错步骤")
    retry_count = Column(Integer, default=0, comment="重试次数")
    max_retries = Column(Integer, default=3, comment="最大重试次数")

    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False, comment="更新时间")
    started_at = Column(DateTime, nullable=True, comment="开始处理时间")
    completed_at = Column(DateTime, nullable=True, comment="完成时间")

    # 关系
    logs = relationship("ReportTaskLog", back_populates="task", cascade="all, delete-orphan")

    # 索引
    __table_args__ = (
        Index("idx_status", "status"),
        Index("idx_created_at", "created_at"),
        Index("idx_report_id", "report_id"),
        Index("idx_workflow_id", "workflow_id"),
    )

    def __repr__(self):
        return f"<ReportTask(id={self.id}, report_id='{self.report_id}', status='{self.status.value}')>"

    @property
    def is_processing(self) -> bool:
        """是否正在处理中"""
        processing_statuses = {
            TaskStatus.OCR_PROCESSING,
            TaskStatus.META_PROCESSING,
            TaskStatus.TABULAR_PROCESSING,
            TaskStatus.OVERALL_PROCESSING,
            TaskStatus.IMAGE_PROCESSING,
            TaskStatus.WRITING_JSON,
        }
        return self.status in processing_statuses

    @property
    def is_completed(self) -> bool:
        """是否已完成"""
        return self.status == TaskStatus.COMPLETED

    @property
    def is_failed(self) -> bool:
        """是否失败"""
        return self.status == TaskStatus.FAILED

    @property
    def can_retry(self) -> bool:
        """是否可以重试"""
        return self.is_failed and self.retry_count < self.max_retries

    def get_step_name(self) -> str:
        """获取当前步骤名称"""
        step_names = ["OCR解析", "个人信息提取", "总体结果分析", "检查项指标分析", "影像数据分析", "写入JSON文件"]
        if 0 <= self.current_step < len(step_names):
            return step_names[self.current_step]
        return "未知步骤"


class ReportTaskLog(Base):
    """报告任务执行日志表"""

    __tablename__ = "report_task_logs"

    # 主键
    id = Column(BigInteger, primary_key=True, autoincrement=True)

    # 关联的报告ID
    report_id = Column(String(64), ForeignKey("report_tasks.report_id"), nullable=False, comment="报告ID")

    # 步骤信息
    step_name = Column(String(50), nullable=False, comment="步骤名称")
    status = Column(Enum(LogStatus), nullable=False, comment="执行状态")
    message = Column(Text, nullable=True, comment="执行消息")
    execution_time_ms = Column(Integer, nullable=True, comment="执行时间(毫秒)")

    # 创建时间
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")

    # 关系
    task = relationship("ReportTask", back_populates="logs")

    # 索引
    __table_args__ = (
        Index("idx_report_id", "report_id"),
        Index("idx_created_at", "created_at"),
    )

    def __repr__(self):
        return f"<ReportTaskLog(id={self.id}, report_id='{self.report_id}', step='{self.step_name}', status='{self.status.value}')>"


class TaskStep(Base):
    """任务步骤状态表"""

    __tablename__ = "task_steps"

    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True)

    # 关联的报告ID
    report_id = Column(String(64), ForeignKey("report_tasks.report_id"), nullable=False, comment="报告ID")

    # 步骤信息
    step_name = Column(String(50), nullable=False, comment="步骤名称")
    status = Column(String(50), nullable=False, comment="步骤状态")
    celery_task_id = Column(String(100), nullable=True, comment="Celery任务ID")

    # 时间信息
    start_time = Column(DateTime, nullable=True, comment="开始时间")
    end_time = Column(DateTime, nullable=True, comment="结束时间")
    duration = Column(Integer, nullable=True, comment="耗时(秒)")

    # 结果和错误信息
    result = Column(Text, nullable=True, comment="执行结果(JSON)")
    error_message = Column(Text, nullable=True, comment="错误信息")

    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False, comment="更新时间")

    # 关系
    task = relationship("ReportTask", backref="task_steps")

    # 索引和约束
    __table_args__ = (
        Index("idx_report_id", "report_id"),
        Index("idx_status", "status"),
        Index("idx_celery_task_id", "celery_task_id"),
        Index("uk_report_step", "report_id", "step_name", unique=True),
    )

    def __repr__(self):
        return f"<TaskStep(id={self.id}, report_id='{self.report_id}', step='{self.step_name}', status='{self.status}')>"


class TaskResult(Base):
    """任务结果存储表"""

    __tablename__ = "task_results"

    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True)

    # 关联的报告ID
    report_id = Column(String(64), ForeignKey("report_tasks.report_id"), nullable=False, comment="报告ID")

    # 结果信息
    result_type = Column(String(50), nullable=False, comment="结果类型")
    result_data = Column(Text, nullable=False, comment="结果数据(JSON)")
    file_path = Column(String(500), nullable=True, comment="文件路径")

    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")

    # 关系
    task = relationship("ReportTask", backref="task_results")

    # 索引
    __table_args__ = (
        Index("idx_report_type", "report_id", "result_type"),
        Index("idx_created_at", "created_at"),
    )

    def __repr__(self):
        return f"<TaskResult(id={self.id}, report_id='{self.report_id}', type='{self.result_type}')>"


class TaskError(Base):
    """任务错误记录表"""

    __tablename__ = "task_errors"

    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True)

    # 关联的报告ID
    report_id = Column(String(64), ForeignKey("report_tasks.report_id"), nullable=False, comment="报告ID")

    # 错误信息
    task_name = Column(String(100), nullable=False, comment="任务名称")
    error_type = Column(String(100), nullable=False, comment="错误类型")
    error_message = Column(Text, nullable=False, comment="错误信息")
    traceback = Column(Text, nullable=True, comment="错误堆栈")
    celery_task_id = Column(String(100), nullable=True, comment="Celery任务ID")
    retry_count = Column(Integer, default=0, comment="重试次数")

    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")

    # 关系
    task = relationship("ReportTask", backref="task_errors")

    # 索引
    __table_args__ = (
        Index("idx_report_id", "report_id"),
        Index("idx_task_name", "task_name"),
        Index("idx_error_type", "error_type"),
        Index("idx_created_at", "created_at"),
    )

    def __repr__(self):
        return f"<TaskError(id={self.id}, report_id='{self.report_id}', task='{self.task_name}', type='{self.error_type}')>"
