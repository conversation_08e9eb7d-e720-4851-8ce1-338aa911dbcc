from typing import TypeVar

from pydantic import BaseModel

T = TypeVar("T")


class ApiResponse[T](BaseModel):
    """统一 API 响应格式"""

    code: int = 0
    message: str = "success"
    data: T | None = None

    @classmethod
    def success(cls, data: T = None, message: str = "success") -> "ApiResponse[T]":
        """成功响应"""
        return cls(code=0, message=message, data=data)

    @classmethod
    def error(cls, code: int, message: str, data: T = None) -> "ApiResponse[T]":
        """错误响应"""
        return cls(code=code, message=message, data=data)


class PaginationResponse(BaseModel):
    """分页响应"""

    total: int
    page: int
    size: int
    pages: int
    items: list


class UserResponse(BaseModel):
    """用户响应模型"""

    id: int
    username: str
    email: str
    is_active: bool
    created_at: str


class ConversationResponse(BaseModel):
    """对话响应模型"""

    id: int
    user_id: int
    title: str | None = None
    created_at: str
    updated_at: str


class MessageResponse(BaseModel):
    """消息响应模型"""

    id: int
    conversation_id: int
    role: str
    content: str
    created_at: str


class ChatResponse(BaseModel):
    """聊天响应模型"""

    response: str
    model: str
    agent: str
    conversation_id: int | None = None
    timestamp: str


class AgentResponse(BaseModel):
    """代理响应模型"""

    name: str
    description: str
    model: str


class ChatStreamResponse(BaseModel):
    """聊天流式响应模型"""

    type: str  # 'content', 'error', 'stream_end'
    content: str | None = None
    error: str | None = None
    timestamp: str
    session_id: str | None = None


class ChatSyncResponse(BaseModel):
    """聊天同步响应模型"""

    content: str
    session_id: str
    model: str | None = None
    timestamp: str
