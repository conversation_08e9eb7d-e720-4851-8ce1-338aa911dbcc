from typing import Any

from pydantic import BaseModel, EmailStr


class UserCreate(BaseModel):
    """用户创建请求"""

    username: str
    email: EmailStr
    password: str


class UserLogin(BaseModel):
    """用户登录请求"""

    username: str
    password: str


class UserUpdate(BaseModel):
    """用户更新请求"""

    username: str | None = None
    email: EmailStr | None = None
    is_active: bool | None = None


class ConversationCreate(BaseModel):
    """创建对话请求"""

    title: str | None = None


class MessageCreate(BaseModel):
    """创建消息请求"""

    conversation_id: int
    role: str
    content: str


class ChatRequest(BaseModel):
    """聊天请求"""

    message: str
    conversation_id: int | None = None


class ChatStreamRequest(BaseModel):
    """聊天流式请求模型"""

    userId: str
    sessionId: str
    question: str
    extra: dict[str, Any] | None = {}


class CrawlerConfigRequest(BaseModel):
    """爬虫配置请求"""

    output_dir: str = "crawled_data"
    max_concurrent: int = 5
    retry_times: int = 3
    delay_between_requests: float = 1.0
    enable_progress_bar: bool = True
    save_intermediate_results: bool = True


class CrawlerStartRequest(BaseModel):
    """爬虫启动请求"""

    resume: bool = False
    config: CrawlerConfigRequest | None = None
