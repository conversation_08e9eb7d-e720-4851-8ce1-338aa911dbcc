"""PDF 解析相关的数据模型"""

from typing import Any

from pydantic import BaseModel, Field


class PDFParseRequest(BaseModel):
    """PDF 解析请求模型"""

    file_url: str | None = Field(None, description="PDF 文件 URL")
    page_start: int = Field(0, description="起始页码，从 0 开始", ge=0)
    page_num: int = Field(20, description="解析页数", ge=1, le=300)


class TextBlock(BaseModel):
    """文本块信息"""

    text: str = Field(..., description="文本内容")
    position: dict[str, Any] = Field(default_factory=dict, description="位置信息")
    font_size: float | None = Field(None, description="字体大小")
    label: str = Field("", description="标签")


class PageInfo(BaseModel):
    """页面信息"""

    page_number: int = Field(..., description="页码")
    text_blocks: list[TextBlock] = Field(default_factory=list, description="文本块列表")


class PDFParseResponse(BaseModel):
    """PDF 解析响应模型"""

    success: bool = Field(..., description="是否成功")
    markdown_content: str = Field("", description="Markdown 格式内容")
    total_pages: int = Field(0, description="总页数")
    pages: list[PageInfo] = Field(default_factory=list, description="页面详细信息")


class PatientInfo(BaseModel):
    """患者信息"""

    name: str | None = Field(None, description="姓名")
    gender: str | None = Field(None, description="性别")
    age: int | None = Field(None, description="年龄")
    id_card: str | None = Field(None, description="身份证号")
    phone: str | None = Field(None, description="电话")


class MedicalInfo(BaseModel):
    """医疗信息"""

    patient_info: dict[str, Any] = Field(default_factory=dict, description="患者基本信息")
    test_results: list[str] = Field(default_factory=list, description="检查结果")
    summary: str = Field("", description="总结")
    recommendations: list[str] = Field(default_factory=list, description="建议")
