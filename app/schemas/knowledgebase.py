"""知识库相关的数据模型

包含火山引擎知识库服务的请求和响应数据结构。
"""

from typing import Any

from pydantic import BaseModel, Field


class TokenUsage(BaseModel):
    """Token 使用情况模型"""

    embedding_token_usage: dict[str, int] = Field(..., description="检索向量化阶段的 token 用量")
    prompt_tokens: int = Field(..., description="向量化阶段 prompt token 数")
    completion_tokens: int = Field(..., description="向量化阶段 completion token 数")
    total_tokens: int = Field(..., description="向量化阶段总 token 数")
    rerank_token_usage: int = Field(..., description="在重排阶段的 token 用量")
    rewrite_token_usage: int = Field(..., description="query 改写的 token 用量")


class DocumentInfo(BaseModel):
    """文档信息模型"""

    doc_id: str = Field(..., description="文档 id")
    doc_name: str = Field(..., description="文档名字")
    create_time: int = Field(..., description="文档的创建时间")
    doc_type: str = Field(..., description="知识所属原始文档的类型")
    doc_meta: str = Field(..., description="文档相关元信息（字段信息的列表字符串）")
    source: str = Field(..., description="知识来源类型（如 tos、lark、tos_fe）")
    title: str = Field(..., description="知识所属文档的标题")
    url: str = Field(..., description="原始文档的公开下载链接或飞书文档链接")


class ChunkAttachment(BaseModel):
    """切片附件信息模型"""

    uuid: str = Field(..., description="附件的唯一标识")
    caption: str = Field(..., description="图片所属标题")
    type: str = Field(..., description="附件类型（如 image）")
    link: str = Field(..., description="图片的临时下载链接（有效期 10 分钟）")


class OriginalCoordinate(BaseModel):
    """原始坐标信息模型"""

    page_no: int = Field(..., description="页码")
    bbox: list[float] = Field(..., description="边界框坐标")


class KnowledgePoint(BaseModel):
    """知识点模型"""

    collection_name: str = Field(..., description="检索知识库名字")
    count: int = Field(..., description="检索返回的切片数量")
    rewrite_query: str = Field(..., description="改写的 query")
    token_usage: TokenUsage = Field(..., description="token 用量信息")
    id: str = Field(..., description="索引的主键")
    content: str = Field(..., description="切片内容")
    md_content: str | None = Field(None, description="markdown 格式的解析结果（表格切片专用）")
    html_content: str | None = Field(None, description="html 格式的解析结果（表格切片专用）")
    table_chunk_fields: list[dict[str, Any]] | None = Field(None, description="结构化数据检索返回单行全量数据")
    original_question: str | None = Field(None, description="faq 数据检索召回答案对应的原始问题")
    score: float = Field(..., description="向量化语义检索得分")
    point_id: str = Field(..., description="切片 id")
    chunk_title: str = Field(..., description="切片标题")
    chunk_id: int = Field(..., description="切片位次 id（原始文档中的位次顺序）")
    process_time: int = Field(..., description="检索耗时")
    rerank_score: float | None = Field(None, description="重排得分")
    doc_info: DocumentInfo = Field(..., description="切片所属文档信息")
    recall_position: int = Field(..., description="向量化语义检索召回位次")
    rerank_position: int = Field(..., description="重排位次")
    chunk_type: str = Field(..., description="切片类型")
    chunk_attachment: list[ChunkAttachment] | None = Field(None, description="检索召回附件的临时下载链接")
    original_coordinate: OriginalCoordinate | None = Field(None, description="切片在原文中的位置坐标")


class SearchRequest(BaseModel):
    """知识库搜索请求模型"""

    collection_name: str = Field(..., description="知识库名称")
    query: str = Field(..., max_length=8000, description="搜索查询文本")
    limit: int = Field(default=10, ge=1, le=200, description="返回结果数量")
    dense_weight: float = Field(default=0.7, ge=0.2, le=1.0, description="向量检索权重")
    rerank_switch: bool = Field(default=True, description="是否启用重排序")
    doc_filter: dict[str, Any] | None = Field(None, description="文档过滤条件")


class SearchResponse(BaseModel):
    """知识库搜索响应模型"""

    points: list[KnowledgePoint] = Field(..., description="搜索结果列表")
    total: int = Field(..., description="总结果数")
    query_time: float | None = Field(None, description="查询耗时(毫秒)")


class CollectionInfo(BaseModel):
    """知识库信息模型"""

    collection_name: str = Field(..., description="知识库名称")
    description: str | None = Field(None, description="知识库描述")
    created_time: str | None = Field(None, description="创建时间")
    updated_time: str | None = Field(None, description="更新时间")
    document_count: int | None = Field(None, description="文档数量")
    status: str | None = Field(None, description="状态")


class CreateCollectionRequest(BaseModel):
    """创建知识库请求模型"""

    collection_name: str = Field(..., description="知识库名称")
    description: str | None = Field(None, description="知识库描述")
    config: dict[str, Any] | None = Field(None, description="配置参数")


class UploadDocumentRequest(BaseModel):
    """上传文档请求模型"""

    collection_name: str = Field(..., description="知识库名称")
    document_name: str = Field(..., description="文档名称")
    document_content: str = Field(..., description="文档内容")
    document_url: str | None = Field(None, description="文档URL")
    metadata: dict[str, Any] | None = Field(None, description="文档元数据")


class DeleteDocumentRequest(BaseModel):
    """删除文档请求模型"""

    collection_name: str = Field(..., description="知识库名称")
    document_id: str = Field(..., description="文档ID")


class OperationResponse(BaseModel):
    """通用操作响应模型"""

    success: bool = Field(..., description="操作是否成功")
    message: str | None = Field(None, description="响应消息")
    data: dict[str, Any] | None = Field(None, description="响应数据")
