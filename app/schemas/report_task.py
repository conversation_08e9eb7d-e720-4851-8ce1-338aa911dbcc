"""
报告任务相关的Pydantic模式定义
"""

from datetime import datetime
from decimal import Decimal

from pydantic import BaseModel, Field


class ReportParseRequest(BaseModel):
    """提交报告解析任务请求"""

    report_id: str = Field(..., description="报告唯一标识", min_length=1, max_length=64)
    report_key: str = Field(..., description="TOS文件存储Key", min_length=1, max_length=255)


class ReportParseResponse(BaseModel):
    """提交报告解析任务响应"""

    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    message: str = Field(..., description="响应消息")


class TaskRestartRequest(BaseModel):
    """重新执行任务请求"""

    from_step: int | None = Field(None, description="从指定步骤开始，不指定则从头开始", ge=0, le=5)


class TaskRestartResponse(BaseModel):
    """重新执行任务响应"""

    report_id: str = Field(..., description="报告ID")
    status: str = Field(..., description="任务状态")
    message: str = Field(..., description="响应消息")
    restarted_from_step: int = Field(..., description="从第几步开始重新执行")


class StepDetail(BaseModel):
    """步骤详情"""

    step_index: int = Field(..., description="步骤索引")
    step_name: str = Field(..., description="步骤名称")
    status: str = Field(..., description="步骤状态")
    completed: bool = Field(..., description="是否完成")
    error_message: str | None = Field(None, description="错误信息")


class TaskStatusResponse(BaseModel):
    """任务状态查询响应"""

    report_id: str = Field(..., description="报告ID")
    status: str = Field(..., description="任务状态")
    current_step: int = Field(..., description="当前步骤")
    total_steps: int = Field(..., description="总步骤数")
    progress_percent: Decimal = Field(..., description="进度百分比")

    step_details: list[StepDetail] = Field(..., description="步骤详情列表")

    # 结果存储路径
    ocr_result_key: str | None = Field(None, description="OCR结果TOS Key")
    final_json_path: str | None = Field(None, description="最终JSON文件路径")

    # 错误信息
    error_message: str | None = Field(None, description="错误信息")
    error_step: str | None = Field(None, description="出错步骤")
    retry_count: int = Field(..., description="重试次数")

    # 时间信息
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    started_at: datetime | None = Field(None, description="开始处理时间")
    completed_at: datetime | None = Field(None, description="完成时间")


class TaskSummary(BaseModel):
    """任务摘要信息"""

    report_id: str = Field(..., description="报告ID")
    status: str = Field(..., description="任务状态")
    progress_percent: Decimal = Field(..., description="进度百分比")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    processing_time_seconds: int | None = Field(None, description="处理时长(秒)")


class TaskListRequest(BaseModel):
    """任务列表查询请求"""

    status: str | None = Field(None, description="按状态筛选")
    page: int = Field(1, description="页码", ge=1)
    size: int = Field(20, description="每页大小", ge=1, le=100)
    sort_by: str = Field("created_at", description="排序字段")
    sort_order: str = Field("desc", description="排序方向")


class TaskListResponse(BaseModel):
    """任务列表查询响应"""

    tasks: list[TaskSummary] = Field(..., description="任务列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    pages: int = Field(..., description="总页数")



class TaskLogEntry(BaseModel):
    """任务日志条目"""

    id: int = Field(..., description="日志ID")
    step_name: str = Field(..., description="步骤名称")
    status: str = Field(..., description="执行状态")
    message: str | None = Field(None, description="执行消息")
    execution_time_ms: int | None = Field(None, description="执行时间(毫秒)")
    created_at: datetime = Field(..., description="创建时间")


class TaskLogsResponse(BaseModel):
    """任务日志查询响应"""

    report_id: str = Field(..., description="报告ID")
    logs: list[TaskLogEntry] = Field(..., description="日志列表")


class OCRResultResponse(BaseModel):
    """OCR结果响应"""

    success: bool = Field(..., description="是否成功")
    report_id: str = Field(..., description="报告ID")
    markdown: str = Field(..., description="Markdown格式内容")
    detail: dict | None = Field(None, description="详细信息")
    pretty: list | None = Field(None, description="清洗后数据")
    tos_keys: dict[str, str] = Field(..., description="TOS存储键值对")


class AgentResultResponse(BaseModel):
    """Agent分析结果响应"""

    success: bool = Field(..., description="是否成功")
    agent_type: str = Field(..., description="Agent类型")
    result: dict = Field(..., description="分析结果")
    execution_time_ms: int = Field(..., description="执行时间(毫秒)")


class FinalResultResponse(BaseModel):
    """最终结果响应"""

    report_id: str = Field(..., description="报告ID")
    json_path: str = Field(..., description="JSON文件路径")
    processing_info: dict = Field(..., description="处理信息")
    ocr_result: dict = Field(..., description="OCR结果")
    analysis_results: dict = Field(..., description="分析结果集合")
