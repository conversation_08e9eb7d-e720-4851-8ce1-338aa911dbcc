"""
Celery配置模块
"""

from celery import Celery
from kombu import Queue
from app.core.config import settings

# 创建Celery应用实例
celery_app = Celery('health_report_workflow')

# Celery配置
celery_app.conf.update(
    # Broker和Backend配置
    broker_url=getattr(settings, 'CELERY_BROKER_URL', settings.REDIS_URL),
    result_backend=getattr(settings, 'CELERY_RESULT_BACKEND', settings.REDIS_URL.replace('/0', '/1'))
    
    # 任务序列化配置
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    
    # 时区设置
    timezone='UTC',
    enable_utc=True,
    
    # 任务路由配置
    task_routes={
        'app.tasks.ocr_tasks.*': {'queue': 'ocr_queue'},
        'app.tasks.ai_tasks.*': {'queue': 'ai_queue'},
        'app.tasks.post_process_tasks.*': {'queue': 'post_queue'},
        'app.tasks.storage_tasks.*': {'queue': 'api_queue'},
        'app.tasks.workflow_tasks.*': {'queue': 'workflow_queue'},
    },
    
    # 队列定义
    task_queues=(
        Queue('ocr_queue', routing_key='ocr'),
        Queue('ai_queue', routing_key='ai'),
        Queue('post_queue', routing_key='post'),
        Queue('api_queue', routing_key='api'),
        Queue('workflow_queue', routing_key='workflow'),
    ),
    
    # 任务超时设置
    task_soft_time_limit=getattr(settings, 'CELERY_TASK_SOFT_TIME_LIMIT', 1800),
    task_time_limit=getattr(settings, 'CELERY_TASK_TIME_LIMIT', 2400)
    
    # 重试和确认设置
    task_acks_late=True,
    worker_prefetch_multiplier=1,
    
    # 结果过期时间
    result_expires=3600,  # 1小时后过期
    
    # 任务压缩
    task_compression='gzip',
    result_compression='gzip',
    
    # 工作进程设置
    worker_max_tasks_per_child=getattr(settings, 'CELERY_WORKER_MAX_TASKS_PER_CHILD', 1000)
    worker_disable_rate_limits=False,
    
    # 监控设置
    worker_send_task_events=True,
    task_send_sent_event=True,
    
    # 错误处理
    task_reject_on_worker_lost=True,
    task_ignore_result=False,
)

# 自动发现任务模块
celery_app.autodiscover_tasks([
    'app.tasks.ocr_tasks',
    'app.tasks.ai_tasks', 
    'app.tasks.post_process_tasks',
    'app.tasks.storage_tasks',
    'app.tasks.workflow_tasks',
])