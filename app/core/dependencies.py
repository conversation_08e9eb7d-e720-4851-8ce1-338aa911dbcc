"""简化的依赖模块

提供简单的客户端创建函数，不使用复杂的依赖注入。
"""

from collections.abc import AsyncGenerator
from contextlib import contextmanager

import redis.asyncio as redis
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from structlog import get_logger

from app.client.redis_client import redis_manager
from app.core.config import settings

logger = get_logger(__name__)

# 创建全局 MySQL 引擎
mysql_engine = create_engine(
    settings.mysql_url,
    pool_size=settings.MYSQL_POOL_SIZE,
    max_overflow=settings.MYSQL_MAX_OVERFLOW,
    pool_timeout=settings.MYSQL_POOL_TIMEOUT,
    pool_recycle=settings.MYSQL_POOL_RECYCLE,
    pool_pre_ping=settings.MYSQL_POOL_PRE_PING,
)

# 会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=mysql_engine)


@contextmanager
def get_mysql_session():
    """获取 MySQL 会话"""
    session = SessionLocal()
    try:
        yield session
        session.commit()
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()


def get_mysql_engine():
    """获取 MySQL 引擎（用于 Agno）"""
    return mysql_engine


async def get_redis() -> AsyncGenerator[redis.Redis, None]:
    """获取 Redis 客户端"""
    redis_client = await redis_manager.get_redis_client()
    try:
        yield redis_client
    finally:
        pass


async def cleanup_dependencies():
    """清理所有依赖资源"""
    await redis_manager.close()
    mysql_engine.dispose()
    logger.info("所有依赖资源已清理")
