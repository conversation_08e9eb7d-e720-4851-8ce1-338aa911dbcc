"""
数据库初始化和管理
"""

from contextlib import asynccontextmanager

from structlog import get_logger

logger = get_logger(__name__)


@asynccontextmanager
async def lifespan_handler():
    """应用生命周期管理"""
    try:
        # 启动任务管理器工作线程
        from app.services.report_task_manager import get_task_manager

        task_manager = get_task_manager()
        await task_manager.start_workers(num_workers=3)

        logger.info("应用启动完成")
        yield

    except Exception as e:
        logger.error("应用启动失败", error=str(e))
        raise
    finally:
        # 应用关闭时的清理工作
        logger.info("应用正在关闭")
