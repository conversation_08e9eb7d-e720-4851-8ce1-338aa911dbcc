from pydantic import ConfigDict
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # 配置模型设置：忽略 .env 中存在但类中未定义的字段
    model_config = ConfigDict(env_file=".env", extra="ignore")  # 忽略额外的字段而不是报错

    # 应用配置
    APP_NAME: str = "ai-assistant"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False

    # LLM 模型配置
    LLM_MODEL: str = "doubao-1-5-pro-32k-250115"
    LLM_BASE_URL: str = "https://ark.cn-beijing.volces.com/api/v3"
    ARK_API_KEY: str | None = None

    # Redis 配置
    REDIS_URL: str = "redis://localhost:6379/0"
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: str | None = None

    # MySQL 配置
    MYSQL_HOST: str = "localhost"
    MYSQL_PORT: int = 3306
    MYSQL_USER: str = "root"
    MYSQL_PASSWORD: str = ""

    # 任务系统配置
    OCR_CACHE_SIZE: int = 100
    OCR_CACHE_EXPIRE_MINUTES: int = 60
    OCR_TIMEOUT_SECONDS: int = 300
    AGENT_TIMEOUT_SECONDS: int = 180
    TASK_WORKER_COUNT: int = 3

    # Celery配置
    CELERY_BROKER_URL: str = "redis://localhost:6379/0"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/1"
    CELERY_TASK_SOFT_TIME_LIMIT: int = 1800  # 30分钟
    CELERY_TASK_TIME_LIMIT: int = 2400       # 40分钟
    CELERY_WORKER_CONCURRENCY: int = 4
    CELERY_WORKER_MAX_TASKS_PER_CHILD: int = 1000
    
    # Worker配置
    OCR_WORKER_CONCURRENCY: int = 2
    AI_WORKER_CONCURRENCY: int = 4
    POST_WORKER_CONCURRENCY: int = 3
    API_WORKER_CONCURRENCY: int = 2
    
    # 任务轮询配置
    TASK_POLLING_INTERVAL: int = 5  # 秒
    TASK_POLLING_BATCH_SIZE: int = 10

    # 监控配置
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 9090
    MYSQL_DATABASE: str = "ai_assistant"
    MYSQL_CHARSET: str = "utf8mb4"

    # MySQL 连接池配置
    MYSQL_POOL_SIZE: int = 10
    MYSQL_MAX_OVERFLOW: int = 20
    MYSQL_POOL_TIMEOUT: int = 30
    MYSQL_POOL_RECYCLE: int = 3600
    MYSQL_POOL_PRE_PING: bool = True

    @property
    def mysql_url(self) -> str:
        """构造 MySQL 连接 URL"""
        return f"mysql+pymysql://{self.MYSQL_USER}:{self.MYSQL_PASSWORD}@{self.MYSQL_HOST}:{self.MYSQL_PORT}/{self.MYSQL_DATABASE}?charset={self.MYSQL_CHARSET}"  # type: ignore

    # 日志配置
    LOG_LEVEL: str = "INFO"

    # 火山引擎配置
    VOLCENGINE_ACCESS_KEY: str | None = None
    VOLCENGINE_SECRET_KEY: str | None = None
    VOLCENGINE_REGION: str = "cn-beijing"
    VOLCENGINE_ENDPOINT: str | None = None

    # 连接池配置
    VOLCENGINE_MAX_HOSTS: int = 4
    VOLCENGINE_MAX_POOL_SIZE: int | None = None  # 默认为 cpu_count() * 5
    VOLCENGINE_CONNECT_TIMEOUT: int = 30
    VOLCENGINE_READ_TIMEOUT: int = 30

    # 重试配置
    VOLCENGINE_RETRY_COUNT: int = 3
    VOLCENGINE_RETRY_DELAY: float = 1.0

    # 是否启用双栈网络(IPv4 + IPv6)
    VOLCENGINE_ENABLE_DUAL_STACK: bool = False

    # 火山引擎知识库配置
    VOLCENGINE_KB_HOST: str = "api-knowledgebase.mlp.cn-beijing.volces.com"
    VOLCENGINE_KB_SCHEME: str = "https"
    VOLCENGINE_KB_CONNECTION_TIMEOUT: int = 30
    VOLCENGINE_KB_SOCKET_TIMEOUT: int = 30

    # 火山引擎语音转文本(STT)配置
    VOLCENGINE_STT_APP_ID: str | None = None
    VOLCENGINE_STT_ACCESS_TOKEN: str | None = None
    VOLCENGINE_STT_ENDPOINT: str = "https://openspeech.bytedance.com/api/v3/auc/bigmodel/recognize/flash"
    VOLCENGINE_STT_RESOURCE_ID: str = "volc.bigasr.auc_turbo"
    VOLCENGINE_STT_MODEL_NAME: str = "bigmodel"

    # 火山引擎TOS对象存储配置
    TOS_ENDPOINT: str = "https://tos-cn-beijing.volces.com"
    TOS_REGION: str = "cn-beijing"
    TOS_BUCKET_NAME: str | None = None
    TOS_MAX_CONCURRENCY: int = 50
    TOS_CONNECTION_TIME: int = 30
    TOS_READ_TIMEOUT: int = 30


settings = Settings()
