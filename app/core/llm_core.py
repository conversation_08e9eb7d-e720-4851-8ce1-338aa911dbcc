from dataclasses import dataclass
from enum import Enum
import os

from .config import settings


class ModelType(str, Enum):
    """模型类型枚举"""

    DOUBAO_SEED_1_6_250615 = "doubao-seed-1-6-250615"
    DOUBAO_1_5_PRO_256K_250115 = "doubao-1-5-pro-256k-250115"
    DOUBAO_1_5_LITE_32K_250115 = "doubao-1-5-lite-32k-250115"
    DOUBAO_SEED_1_6_THINKING_250715 = "doubao-seed-1-6-thinking-250715"


@dataclass
class ModelConfig:
    """模型配置类"""

    name: str
    model_type: ModelType
    max_tokens: int
    temperature: float = 0.0

    @property
    def api_params(self) -> dict:
        """获取 API 调用参数"""
        return {
            "model": self.model_type.value,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
        }


class ModelManager:
    """模型管理器"""

    def __init__(self):
        self.models = self._initialize_models()
        # agno 角色映射
        self.role_map = {
            "system": "system",
            "user": "user",
            "assistant": "assistant",
            "tool": "tool",
            "developer": "system",  # 将 developer 映射为 system
            "expert": "assistant",  # 也可以映射其他自定义角色
        }

    def _initialize_models(self) -> dict[str, ModelConfig]:
        """初始化模型配置"""
        return {
            "doubao_seed": ModelConfig(
                name="豆包Seed 1.6",
                model_type=ModelType.DOUBAO_SEED_1_6_250615,
                max_tokens=32000,  # 最大输出token 16k
                temperature=0.0,
            ),
            "doubao_pro_256k": ModelConfig(
                name="豆包1.5专业版256K",
                model_type=ModelType.DOUBAO_1_5_PRO_256K_250115,
                max_tokens=256000,
                temperature=0.0,
            ),
            "doubao_lite_32k": ModelConfig(
                name="豆包1.5轻量版32K",
                model_type=ModelType.DOUBAO_1_5_LITE_32K_250115,
                max_tokens=32000,
                temperature=0.0,
            ),
            "doubao_seed_thinking": ModelConfig(
                name="豆包Seed 1.6 Thinking",
                model_type=ModelType.DOUBAO_SEED_1_6_THINKING_250715,
                max_tokens=32000,  # 最大输出token 32k
                temperature=0.0,
            ),
        }

    def get_model(self, model_name: str) -> ModelConfig | None:
        """获取模型配置"""
        return self.models.get(model_name)

    def get_default_model(self) -> ModelConfig:
        """获取默认模型配置"""
        return self.models["doubao_pro_256k"]

    def list_models(self) -> list[str]:
        """列出所有可用模型"""
        return list(self.models.keys())

    def get_api_config(self, model_name: str | None = None) -> dict:
        """获取 API 配置"""
        model = self.get_model(model_name) if model_name else self.get_default_model()
        return {
            "base_url": settings.LLM_BASE_URL,
            "api_key": settings.ARK_API_KEY or os.environ.get("ARK_API_KEY"),
            "model": model.model_type.value,
            **model.api_params,
        }

    def map_role(self, role: str) -> str:
        """映射角色"""
        return self.role_map.get(role, role)


# 全局模型管理器实例
model_manager = ModelManager()


def get_model_manager() -> ModelManager:
    """获取模型管理器实例"""
    return model_manager


def get_default_model_config() -> dict:
    """获取默认模型配置"""
    return model_manager.get_api_config()


def get_role_map() -> dict[str, str]:
    """获取角色映射"""
    return model_manager.role_map


def get_agent_model_config(model_name: str = "doubao_seed") -> dict:
    """获取 Agent 模型配置

    Args:
        model_name: 模型名称，默认为 doubao_seed

    Returns:
        适用于 OpenAIChat 的配置字典
    """
    model_config = model_manager.get_model(model_name)
    if not model_config:
        model_config = model_manager.get_default_model()

    return {
        "id": model_config.model_type.value,
        "base_url": settings.LLM_BASE_URL,
        "api_key": settings.ARK_API_KEY or os.environ.get("ARK_API_KEY"),
        "temperature": model_config.temperature,
        "timeout": 100000,
        "max_tokens": model_config.max_tokens,
        "role_map": model_manager.role_map,
    }


def thinking_params(thinking_type: str = "auto") -> dict:
    """配置模型的深度思考能力

    Args:
        thinking_type: 思考类型,可选值:
            - disabled: 不使用深度思考能力(默认)
            - enabled: 使用深度思考能力
            - auto: 模型自行判断是否使用深度思考能力

    Returns:
        包含思考配置的字典
    """
    return {"extra_body": {"thinking": {"type": thinking_type}}}
