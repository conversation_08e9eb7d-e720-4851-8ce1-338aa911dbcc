"""
模板引擎 - 基于 Jinja2
"""

from dataclasses import dataclass
import json
from pathlib import Path
from typing import Any

from jinja2 import Environment, FileSystemLoader, select_autoescape
from jinja2.exceptions import TemplateError, TemplateNotFound
from pydantic import BaseModel, Field, ValidationError
import structlog

logger = structlog.get_logger(__name__)


class TemplateContext(BaseModel):
    """模板上下文数据模型"""

    agent_name: str = Field(..., description="智能体名称")
    description: str = Field(..., description="智能体描述")
    additional_instructions: str | None = Field(None, description="额外指令")

    # 动态变量
    reference_interval: str | None = Field(None, description="参考区间")

    class Config:
        extra = "allow"  # 允许额外字段


@dataclass
class TemplateInfo:
    """模板信息"""

    name: str
    path: str
    description: str
    version: str
    variables: list[str]
    parent_template: str | None = None


class TemplateEngine:
    """模板引擎 - 基于 Jinja2 Environment"""

    def __init__(self, template_dir: str | Path):
        self.template_dir = Path(template_dir)
        self._env = self._create_environment()
        self._template_cache: dict[str, Any] = {}

        # 注册自定义过滤器
        self._register_filters()

        logger.info(f"模板引擎初始化完成，模板目录: {self.template_dir}")

    def _create_environment(self) -> Environment:
        """创建 Jinja2 环境"""
        env = Environment(
            loader=FileSystemLoader(str(self.template_dir)),
            autoescape=select_autoescape(["html", "xml"]),
            trim_blocks=True,
            lstrip_blocks=True,
            keep_trailing_newline=True,
        )

        # 配置全局函数
        env.globals.update(
            {
                "range": range,
                "len": len,
                "enumerate": enumerate,
            }
        )

        return env

    def _register_filters(self):
        """注册自定义过滤器"""

        def json_format(value, indent=2):
            """JSON 格式化过滤器"""
            if isinstance(value, str):
                try:
                    value = json.loads(value)
                except json.JSONDecodeError:
                    return value
            return json.dumps(value, ensure_ascii=False, indent=indent)

        def truncate_words(value, length=50):
            """截断文字过滤器"""
            if len(value) <= length:
                return value
            return value[:length] + "..."

        self._env.filters["json_format"] = json_format
        self._env.filters["truncate_words"] = truncate_words

    def get_template(self, template_name: str):
        """获取模板（带缓存）"""
        if template_name in self._template_cache:
            return self._template_cache[template_name]

        try:
            template = self._env.get_template(template_name)
            self._template_cache[template_name] = template
            return template
        except TemplateNotFound as e:
            logger.error(f"模板未找到: {template_name}")
            raise TemplateError(f"模板 '{template_name}' 不存在") from e

    def render(self, template_name: str, context: dict[str, Any] | TemplateContext | None = None, **kwargs) -> str:
        """渲染模板"""
        try:
            template = self.get_template(template_name)

            # 处理上下文数据
            if context is None:
                context = {}
            elif isinstance(context, TemplateContext):
                context = context.model_dump()

            # 合并 kwargs
            context.update(kwargs)

            # 渲染模板
            result = template.render(**context)

            logger.debug(f"模板 '{template_name}' 渲染成功")
            return result

        except TemplateError as e:
            logger.error(f"模板渲染失败: {template_name}, 错误: {e}")
            raise
        except Exception as e:
            logger.error(f"模板渲染异常: {template_name}, 错误: {e}")
            raise TemplateError(f"模板渲染失败: {e}") from e

    def validate_context(self, context: dict[str, Any]) -> TemplateContext:
        """验证模板上下文"""
        try:
            return TemplateContext(**context)
        except ValidationError as e:
            logger.error(f"模板上下文验证失败: {e}")
            raise TemplateError(f"上下文验证失败: {e}") from e

    def list_templates(self) -> list[str]:
        """列出所有可用模板"""
        templates = []
        for template_file in self.template_dir.rglob("*.j2"):
            # 计算相对路径作为模板名
            template_name = str(template_file.relative_to(self.template_dir))
            templates.append(template_name)
        return sorted(templates)

    def reload_templates(self):
        """重新加载模板（清除缓存）"""
        self._template_cache.clear()
        logger.info("模板缓存已清除，模板已重新加载")


# 全局模板引擎实例
_template_engine: TemplateEngine | None = None


def get_template_engine() -> TemplateEngine:
    """获取全局模板引擎实例"""
    global _template_engine
    if _template_engine is None:
        from pathlib import Path

        template_dir = Path(__file__).parent.parent / "templates"
        _template_engine = TemplateEngine(template_dir)
    return _template_engine
