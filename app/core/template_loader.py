"""
模板加载器 - 负责智能体模板的配置和管理
"""

from dataclasses import dataclass
from enum import Enum
from typing import Any

import structlog

from app.core.template_engine import TemplateContext, TemplateEngine, get_template_engine

logger = structlog.get_logger(__name__)


class AgentType(str, Enum):
    """智能体类型枚举"""

    MEDICAL_SEARCH = "medical_search"  # AI 小助手
    META_EXTRACTOR = "meta_extractor"  # report: 1-个人及机构信息提取
    OVERALL_RESULT = "overall_result"  # report: 2-健康建议、总检信息提取
    TABULAR_PARSER = "tabular_parser"  # report: 3-检查项、报告单指标数据提取
    MEDICAL_IMAGING_PARSER = "img_parser"  # report: 4-影像数据提取（心电图、超声、CT 等）
    EXISTING_DISEASES = "existing_diseases"  # report: 5-已存在疾病列表提取


@dataclass
class AgentTemplateConfig:
    """智能体模板配置"""

    agent_type: AgentType
    template_file: str
    agent_name: str
    description: str
    additional_instructions: str | None = None
    version: str = "1.0.0"

    def to_context(self, **kwargs) -> TemplateContext:
        """转换为模板上下文"""
        context_data = {
            "agent_name": self.agent_name,
            "description": self.description,
            "additional_instructions": self.additional_instructions,
            **kwargs,
        }

        return TemplateContext(**context_data)


class TemplateLoader:
    """模板加载器"""

    def __init__(self, template_engine: TemplateEngine | None = None):
        self.template_engine = template_engine or get_template_engine()
        self._configs: dict[AgentType, AgentTemplateConfig] = {}
        self._load_configs()

    def _load_configs(self):
        """加载智能体模板配置"""
        # 简化配置，principles 和 requirements 维护在模板文件中
        configs = {
            AgentType.MEDICAL_SEARCH: AgentTemplateConfig(
                agent_type=AgentType.MEDICAL_SEARCH,
                template_file="agents/medical_search.j2",
                agent_name="专业的医疗信息搜索助手",
                description="专门从可靠的医疗资源中提供准确的医疗信息",
            ),
            AgentType.META_EXTRACTOR: AgentTemplateConfig(
                agent_type=AgentType.META_EXTRACTOR,
                template_file="agents/meta_extractor.j2",
                agent_name="专业的个人信息提取专家",
                description="负责从体检报告中提取受检人信息和体检机构信息",
            ),
            AgentType.OVERALL_RESULT: AgentTemplateConfig(
                agent_type=AgentType.OVERALL_RESULT,
                template_file="agents/overall_result.j2",
                agent_name="经验丰富的医疗健康体检报告分析专家",
                description="你是一位经验丰富的医疗健康体检报告分析专家，具备深厚的医学知识和数据处理能力。负责解析体检报告的总体结果、汇总信息和健康建议等数据提取工作。",
            ),
            AgentType.TABULAR_PARSER: AgentTemplateConfig(
                agent_type=AgentType.TABULAR_PARSER,
                template_file="agents/tabular_parser.j2",
                agent_name="检查项目明细Agent",
                description="你是一位经验丰富的医疗健康体检报告分析专家，具备深厚的医学知识和数据处理能力。现需要你对一份通过OCR技术识别的体检报告进行精准的分析和信息提取。",
            ),
            AgentType.MEDICAL_IMAGING_PARSER: AgentTemplateConfig(
                agent_type=AgentType.MEDICAL_IMAGING_PARSER,
                template_file="agents/img_parser.j2",
                agent_name="专业的医疗报告影像资料解析专家",
                description="你是一位经验丰富的医疗健康体检报告影像资料分析专家，具备深厚的医学影像知识和数据处理能力。现需要你对一份通过OCR技术识别的体检报告进行精准的分析和信息提取。",
            ),
            AgentType.EXISTING_DISEASES: AgentTemplateConfig(
                agent_type=AgentType.EXISTING_DISEASES,
                template_file="agents/existing_diseases.j2",
                agent_name="专业的疾病信息提取专家",
                description="你是一位经验丰富的医疗健康体检报告分析专家，具备深厚的医学知识和疾病分类能力。负责从体检报告中识别和提取受检者已存在的疾病信息。",
            ),
        }

        self._configs = configs
        logger.info(f"已加载 {len(configs)} 个智能体模板配置")

    def get_config(self, agent_type: AgentType) -> AgentTemplateConfig:
        """获取智能体模板配置"""
        if agent_type not in self._configs:
            raise ValueError(f"未找到智能体类型 {agent_type} 的配置")
        return self._configs[agent_type]

    def render_prompt(self, agent_type: AgentType, context_vars: dict[str, Any] | None = None) -> str:
        """渲染智能体提示"""
        config = self.get_config(agent_type)

        # 创建模板上下文
        context = config.to_context(**(context_vars or {}))

        # 渲染模板
        prompt = self.template_engine.render(config.template_file, context)

        logger.debug(f"智能体 {agent_type} 提示渲染完成")
        return prompt

    def get_system_prompt(self, agent_type: AgentType) -> str:
        """获取系统提示（兼容旧接口）"""
        return self.render_prompt(agent_type)

    def get_user_prompt(self, agent_type: AgentType, **context_vars) -> str:
        """获取用户提示（支持动态变量）"""
        return self.render_prompt(agent_type, context_vars)

    def list_agent_types(self) -> list[AgentType]:
        """列出所有可用的智能体类型"""
        return list(self._configs.keys())

    def reload_config(self):
        """重新加载配置"""
        self._configs.clear()
        self._load_configs()
        self.template_engine.reload_templates()
        logger.info("模板配置已重新加载")


# 全局模板加载器实例
_template_loader: TemplateLoader | None = None


def get_template_loader() -> TemplateLoader:
    """获取全局模板加载器实例"""
    global _template_loader
    if _template_loader is None:
        _template_loader = TemplateLoader()
    return _template_loader
