"""
Celery Worker管理器
"""

import asyncio
import logging
import os
import signal
import subprocess
import sys
import threading
import time
from typing import Dict, List, Optional

from app.core.config import settings

logger = logging.getLogger(__name__)


class WorkerProcess:
    """单个Worker进程的封装"""
    
    def __init__(self, name: str, queues: str, concurrency: int, pool: str = 'prefork'):
        self.name = name
        self.queues = queues
        self.concurrency = concurrency
        self.pool = pool
        self.process: Optional[subprocess.Popen] = None
        self.start_time: Optional[float] = None
        self.restart_count = 0
        self.max_restarts = 5
        
    def start(self):
        """启动Worker进程"""
        if self.process and self.process.poll() is None:
            logger.warning(f"Worker {self.name} is already running")
            return
        
        cmd = [
            sys.executable, '-m', 'celery', 
            '-A', 'app.core.celery_config:celery_app', 
            'worker',
            '--loglevel=info',
            f'--hostname={self.name}@%h',
            f'--queues={self.queues}',
            f'--concurrency={self.concurrency}',
            f'--pool={self.pool}',
            '--without-gossip',
            '--without-mingle',
            '--without-heartbeat'
        ]
        
        try:
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env=os.environ.copy()
            )
            self.start_time = time.time()
            
            logger.info(f"Started worker {self.name} with PID {self.process.pid}")
            
        except Exception as e:
            logger.error(f"Failed to start worker {self.name}: {e}")
            raise
    
    def stop(self, timeout: int = 10):
        """停止Worker进程"""
        if not self.process:
            return
        
        try:
            # 发送SIGTERM信号
            self.process.terminate()
            
            # 等待进程结束
            try:
                self.process.wait(timeout=timeout)
                logger.info(f"Worker {self.name} stopped gracefully")
            except subprocess.TimeoutExpired:
                # 强制杀死进程
                self.process.kill()
                self.process.wait()
                logger.warning(f"Worker {self.name} was forcefully killed")
                
        except Exception as e:
            logger.error(f"Error stopping worker {self.name}: {e}")
        finally:
            self.process = None
            self.start_time = None
    
    def is_alive(self) -> bool:
        """检查Worker进程是否存活"""
        return self.process is not None and self.process.poll() is None
    
    def restart(self):
        """重启Worker进程"""
        if self.restart_count >= self.max_restarts:
            logger.error(f"Worker {self.name} has reached maximum restart limit ({self.max_restarts})")
            return False
        
        logger.info(f"Restarting worker {self.name} (attempt {self.restart_count + 1})")
        
        self.stop()
        time.sleep(2)  # 等待2秒再重启
        
        try:
            self.start()
            self.restart_count += 1
            return True
        except Exception as e:
            logger.error(f"Failed to restart worker {self.name}: {e}")
            return False
    
    def get_status(self) -> Dict:
        """获取Worker状态信息"""
        return {
            'name': self.name,
            'queues': self.queues,
            'concurrency': self.concurrency,
            'pool': self.pool,
            'pid': self.process.pid if self.process else None,
            'is_alive': self.is_alive(),
            'start_time': self.start_time,
            'restart_count': self.restart_count,
            'uptime': time.time() - self.start_time if self.start_time else 0
        }


class WorkerManager:
    """Celery Worker管理器"""
    
    def __init__(self):
        self.workers: Dict[str, WorkerProcess] = {}
        self.running = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.beat_process: Optional[subprocess.Popen] = None
        self._lock = threading.Lock()
        
        # 定义Worker配置
        self.worker_configs = [
            {
                'name': 'ocr_worker',
                'queues': 'ocr_queue',
                'concurrency': getattr(settings, 'OCR_WORKER_CONCURRENCY', 2),
                'pool': 'prefork'
            },
            {
                'name': 'ai_worker',
                'queues': 'ai_queue',
                'concurrency': getattr(settings, 'AI_WORKER_CONCURRENCY', 4),
                'pool': 'prefork'
            },
            {
                'name': 'post_worker',
                'queues': 'post_queue',
                'concurrency': getattr(settings, 'POST_WORKER_CONCURRENCY', 3),
                'pool': 'prefork'
            },
            {
                'name': 'api_worker',
                'queues': 'api_queue',
                'concurrency': getattr(settings, 'API_WORKER_CONCURRENCY', 2),
                'pool': 'prefork'
            },
            {
                'name': 'workflow_worker',
                'queues': 'workflow_queue',
                'concurrency': 2,
                'pool': 'prefork'
            }
        ]
    
    def start_workers(self):
        """启动所有Worker进程"""
        with self._lock:
            if self.running:
                logger.warning("Workers are already running")
                return
            
            logger.info("Starting Celery workers...")
            
            # 创建并启动所有Worker
            for config in self.worker_configs:
                worker = WorkerProcess(**config)
                try:
                    worker.start()
                    self.workers[config['name']] = worker
                except Exception as e:
                    logger.error(f"Failed to start worker {config['name']}: {e}")
            
            # 启动Beat调度器
            self._start_beat()
            
            # 启动监控线程
            self.running = True
            self.monitor_thread = threading.Thread(target=self._monitor_workers, daemon=True)
            self.monitor_thread.start()
            
            logger.info(f"Started {len(self.workers)} workers successfully")
    
    def stop_workers(self):
        """停止所有Worker进程"""
        with self._lock:
            if not self.running:
                return
            
            logger.info("Stopping Celery workers...")
            
            self.running = False
            
            # 停止Beat调度器
            self._stop_beat()
            
            # 停止所有Worker
            for worker in self.workers.values():
                worker.stop()
            
            self.workers.clear()
            
            # 等待监控线程结束
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=5)
            
            logger.info("All workers stopped")
    
    def restart_worker(self, worker_name: str) -> bool:
        """重启指定的Worker"""
        with self._lock:
            worker = self.workers.get(worker_name)
            if not worker:
                logger.error(f"Worker {worker_name} not found")
                return False
            
            return worker.restart()
    
    def get_worker_status(self, worker_name: str = None) -> Dict:
        """获取Worker状态"""
        if worker_name:
            worker = self.workers.get(worker_name)
            if worker:
                return worker.get_status()
            else:
                return {'error': f'Worker {worker_name} not found'}
        else:
            return {
                'workers': {name: worker.get_status() for name, worker in self.workers.items()},
                'beat_running': self.beat_process is not None and self.beat_process.poll() is None,
                'manager_running': self.running,
                'total_workers': len(self.workers)
            }
    
    def _start_beat(self):
        """启动Celery Beat调度器"""
        try:
            cmd = [
                sys.executable, '-m', 'celery',
                '-A', 'app.core.celery_config:celery_app',
                'beat',
                '--loglevel=info'
            ]
            
            self.beat_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env=os.environ.copy()
            )
            
            logger.info(f"Started Celery Beat with PID {self.beat_process.pid}")
            
        except Exception as e:
            logger.error(f"Failed to start Celery Beat: {e}")
    
    def _stop_beat(self):
        """停止Celery Beat调度器"""
        if self.beat_process:
            try:
                self.beat_process.terminate()
                self.beat_process.wait(timeout=10)
                logger.info("Celery Beat stopped")
            except subprocess.TimeoutExpired:
                self.beat_process.kill()
                self.beat_process.wait()
                logger.warning("Celery Beat was forcefully killed")
            except Exception as e:
                logger.error(f"Error stopping Celery Beat: {e}")
            finally:
                self.beat_process = None
    
    def _monitor_workers(self):
        """监控Worker健康状态"""
        logger.info("Worker monitoring started")
        
        while self.running:
            try:
                # 检查每个Worker的状态
                for name, worker in list(self.workers.items()):
                    if not worker.is_alive():
                        logger.warning(f"Worker {name} is not alive, attempting restart...")
                        
                        if worker.restart():
                            logger.info(f"Worker {name} restarted successfully")
                        else:
                            logger.error(f"Failed to restart worker {name}, removing from pool")
                            del self.workers[name]
                
                # 检查Beat进程
                if self.beat_process and self.beat_process.poll() is not None:
                    logger.warning("Celery Beat process died, restarting...")
                    self._start_beat()
                
                time.sleep(30)  # 每30秒检查一次
                
            except Exception as e:
                logger.error(f"Error in worker monitoring: {e}")
                time.sleep(10)
        
        logger.info("Worker monitoring stopped")
    
    def health_check(self) -> Dict:
        """健康检查"""
        healthy_workers = sum(1 for worker in self.workers.values() if worker.is_alive())
        total_workers = len(self.workers)
        
        return {
            'status': 'healthy' if healthy_workers == total_workers else 'degraded',
            'healthy_workers': healthy_workers,
            'total_workers': total_workers,
            'beat_running': self.beat_process is not None and self.beat_process.poll() is None,
            'manager_running': self.running
        }


# 全局Worker管理器实例
_worker_manager: Optional[WorkerManager] = None
_worker_manager_lock = threading.Lock()


def get_worker_manager() -> WorkerManager:
    """获取Worker管理器实例（单例模式）"""
    global _worker_manager
    if _worker_manager is None:
        with _worker_manager_lock:
            if _worker_manager is None:
                _worker_manager = WorkerManager()
    return _worker_manager


# 信号处理器
def signal_handler(signum, frame):
    """处理系统信号"""
    logger.info(f"Received signal {signum}, shutting down workers...")
    manager = get_worker_manager()
    manager.stop_workers()
    sys.exit(0)


# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)