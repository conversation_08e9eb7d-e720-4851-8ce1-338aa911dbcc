"""
Prompt 管理器
基于 Jinja2 模板引擎，提供灵活的动态提示管理
"""

from typing import Any

from .template_loader import AgentType, TemplateLoader, get_template_loader


class PromptManager:
    def __init__(self, template_loader: Template<PERSON>oader | None = None):
        self.template_loader = template_loader or get_template_loader()

    def get_system_prompt(self, agent_type: AgentType) -> str:
        """获取系统 prompt"""
        return self.template_loader.get_system_prompt(agent_type)

    def get_user_prompt(self, agent_type: AgentType, **kwargs) -> str:
        """获取用户 prompt（支持动态变量）"""
        return self.template_loader.get_user_prompt(agent_type, **kwargs)

    def get_dynamic_prompt(self, agent_type: AgentType, context_vars: dict[str, Any] | None = None) -> str:
        """获取动态渲染的 prompt"""
        return self.template_loader.render_prompt(agent_type, context_vars)

    def list_agent_types(self) -> list[AgentType]:
        """列出所有可用的 Agent 类型"""
        return self.template_loader.list_agent_types()

    def reload_prompts(self):
        """重新加载所有提示模板"""
        self.template_loader.reload_config()


# 全局 prompt 管理器实例
prompt_manager = PromptManager()
