"""
任务轮询器 - 数据库驱动的任务提交系统
"""

import asyncio
import logging
import threading
import time
from typing import List, Dict, Optional

from sqlalchemy.orm import Session
from app.core.config import settings
from app.models import get_db, ReportTask, TaskStatus
from app.tasks.workflow_tasks import create_report_workflow

logger = logging.getLogger(__name__)


class TaskPoller:
    """任务轮询器 - 从数据库轮询待处理任务并提交到Celery"""
    
    def __init__(self):
        self.running = False
        self.polling_thread: Optional[threading.Thread] = None
        self.polling_interval = getattr(settings, 'TASK_POLLING_INTERVAL', 5)  # 默认5秒
        self.batch_size = getattr(settings, 'TASK_POLLING_BATCH_SIZE', 10)  # 默认每次处理10个任务
        self._lock = threading.Lock()
        
        # 统计信息
        self.stats = {
            'total_polled': 0,
            'total_submitted': 0,
            'total_errors': 0,
            'last_poll_time': None,
            'last_error_time': None,
            'last_error_message': None
        }
    
    def start_polling(self):
        """开始轮询任务"""
        with self._lock:
            if self.running:
                logger.warning("Task poller is already running")
                return
            
            self.running = True
            self.polling_thread = threading.Thread(target=self._polling_loop, daemon=True)
            self.polling_thread.start()
            
            logger.info(f"Task poller started with interval {self.polling_interval}s, batch size {self.batch_size}")
    
    def stop_polling(self):
        """停止轮询任务"""
        with self._lock:
            if not self.running:
                return
            
            self.running = False
            
            if self.polling_thread and self.polling_thread.is_alive():
                self.polling_thread.join(timeout=10)
            
            logger.info("Task poller stopped")
    
    def _polling_loop(self):
        """轮询循环"""
        logger.info("Task polling loop started")
        
        while self.running:
            try:
                self.stats['last_poll_time'] = time.time()
                
                # 获取待处理任务
                pending_tasks = self._fetch_pending_tasks()
                self.stats['total_polled'] += len(pending_tasks)
                
                if pending_tasks:
                    logger.info(f"Found {len(pending_tasks)} pending tasks")
                    
                    # 提交任务到Celery
                    submitted_count = self._submit_tasks(pending_tasks)
                    self.stats['total_submitted'] += submitted_count
                    
                    logger.info(f"Successfully submitted {submitted_count}/{len(pending_tasks)} tasks")
                
                # 等待下次轮询
                time.sleep(self.polling_interval)
                
            except Exception as e:
                self.stats['total_errors'] += 1
                self.stats['last_error_time'] = time.time()
                self.stats['last_error_message'] = str(e)
                
                logger.error(f"Error in polling loop: {e}")
                
                # 出错后等待更长时间再重试
                time.sleep(min(self.polling_interval * 2, 30))
        
        logger.info("Task polling loop stopped")
    
    def _fetch_pending_tasks(self) -> List[Dict]:
        """从数据库获取待处理任务"""
        try:
            db = next(get_db())
            try:
                # 查询待处理的任务
                tasks = db.query(ReportTask).filter(
                    ReportTask.status == TaskStatus.PENDING
                ).limit(self.batch_size).all()
                
                # 转换为字典格式
                task_list = []
                for task in tasks:
                    task_list.append({
                        'report_id': task.report_id,
                        'report_key': task.report_key,
                        'created_at': task.created_at,
                        'retry_count': task.retry_count
                    })
                
                return task_list
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Failed to fetch pending tasks: {e}")
            return []
    
    def _submit_tasks(self, tasks: List[Dict]) -> int:
        """提交任务到Celery"""
        submitted_count = 0
        
        for task in tasks:
            try:
                report_id = task['report_id']
                report_key = task['report_key']
                
                # 更新任务状态为处理中
                if self._update_task_status(report_id, TaskStatus.OCR_PROCESSING):
                    # 提交到Celery工作流
                    result = create_report_workflow.delay(
                        report_id=report_id,
                        report_key=report_key
                    )
                    
                    # 保存工作流ID
                    self._save_workflow_id(report_id, result.id)
                    
                    logger.info(f"Submitted task {report_id} to Celery, workflow_id: {result.id}")
                    submitted_count += 1
                else:
                    logger.warning(f"Failed to update status for task {report_id}")
                    
            except Exception as e:
                logger.error(f"Failed to submit task {task['report_id']}: {e}")
                # 将任务状态重置为待处理
                self._update_task_status(task['report_id'], TaskStatus.PENDING)
        
        return submitted_count
    
    def _update_task_status(self, report_id: str, status: TaskStatus) -> bool:
        """更新任务状态"""
        try:
            db = next(get_db())
            try:
                task = db.query(ReportTask).filter(ReportTask.report_id == report_id).first()
                if task:
                    task.status = status
                    if status == TaskStatus.OCR_PROCESSING and not task.started_at:
                        from datetime import datetime
                        task.started_at = datetime.utcnow()
                    
                    db.commit()
                    return True
                else:
                    logger.warning(f"Task {report_id} not found")
                    return False
                    
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Failed to update task status for {report_id}: {e}")
            return False
    
    def _save_workflow_id(self, report_id: str, workflow_id: str):
        """保存工作流ID"""
        try:
            db = next(get_db())
            try:
                task = db.query(ReportTask).filter(ReportTask.report_id == report_id).first()
                if task:
                    task.workflow_id = workflow_id
                    db.commit()
                    
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Failed to save workflow ID for {report_id}: {e}")
    
    def get_stats(self) -> Dict:
        """获取轮询统计信息"""
        stats = self.stats.copy()
        stats['running'] = self.running
        stats['polling_interval'] = self.polling_interval
        stats['batch_size'] = self.batch_size
        
        # 计算运行时间
        if stats['last_poll_time']:
            stats['last_poll_ago'] = time.time() - stats['last_poll_time']
        
        if stats['last_error_time']:
            stats['last_error_ago'] = time.time() - stats['last_error_time']
        
        return stats
    
    def force_poll(self) -> Dict:
        """强制执行一次轮询（用于调试和测试）"""
        if not self.running:
            return {'error': 'Poller is not running'}
        
        try:
            logger.info("Force polling triggered")
            
            pending_tasks = self._fetch_pending_tasks()
            if pending_tasks:
                submitted_count = self._submit_tasks(pending_tasks)
                return {
                    'success': True,
                    'found_tasks': len(pending_tasks),
                    'submitted_tasks': submitted_count
                }
            else:
                return {
                    'success': True,
                    'found_tasks': 0,
                    'submitted_tasks': 0,
                    'message': 'No pending tasks found'
                }
                
        except Exception as e:
            logger.error(f"Force poll failed: {e}")
            return {'error': str(e)}


# 全局任务轮询器实例
_task_poller: Optional[TaskPoller] = None
_task_poller_lock = threading.Lock()


def get_task_poller() -> TaskPoller:
    """获取任务轮询器实例（单例模式）"""
    global _task_poller
    if _task_poller is None:
        with _task_poller_lock:
            if _task_poller is None:
                _task_poller = TaskPoller()
    return _task_poller