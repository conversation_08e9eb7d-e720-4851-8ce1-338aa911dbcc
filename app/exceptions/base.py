class AppAssistantException(Exception):
    """自定义异常基类"""

    def __init__(self, code: int, message: str, detail: str | None = None):
        self.code = code
        self.message = message
        self.detail = detail
        super().__init__(self.message)


class AuthenticationError(AppAssistantException):
    """认证异常"""

    def __init__(self, message: str = "认证失败", detail: str | None = None):
        super().__init__(code=10001, message=message, detail=detail)


class AuthorizationError(AppAssistantException):
    """授权异常"""

    def __init__(self, message: str = "权限不足", detail: str | None = None):
        super().__init__(code=10002, message=message, detail=detail)


class ValidationError(AppAssistantException):
    """验证异常"""

    def __init__(self, message: str = "数据验证失败", detail: str | None = None):
        super().__init__(code=10003, message=message, detail=detail)


class NotFoundError(AppAssistantException):
    """资源不存在异常"""

    def __init__(self, message: str = "资源不存在", detail: str | None = None):
        super().__init__(code=10004, message=message, detail=detail)


class DatabaseError(AppAssistantException):
    """数据库异常"""

    def __init__(self, message: str = "数据库操作失败", detail: str | None = None):
        super().__init__(code=10005, message=message, detail=detail)


class AIServiceError(AppAssistantException):
    """AI 服务异常"""

    def __init__(self, message: str = "AI 服务调用失败", detail: str | None = None):
        super().__init__(code=10006, message=message, detail=detail)
