from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from starlette.exceptions import HTTPException as StarletteHTTPException

from app.exceptions.base import AppAssistantException
from app.middleware.traceid import get_trace_id
from app.utils.logger import get_logger

logger = get_logger(__name__)


def setup_exception_handlers(app: FastAPI):
    """设置全局异常处理器"""

    @app.exception_handler(AppAssistantException)
    async def app_assistant_exception_handler(request: Request, exc: AppAssistantException):
        logger.error(
            f"AppAssistant exception occurred: {exc.message}",
            extra={
                "trace_id": get_trace_id(),
                "exception_code": exc.code,
                "exception_detail": exc.detail,
                "request_path": request.url.path,
                "request_method": request.method,
            },
        )
        return JSONResponse(
            status_code=400,
            content={"code": exc.code, "message": exc.message, "data": None},
        )

    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        logger.warning(
            f"HTTP exception occurred: {exc.detail}",
            extra={
                "trace_id": get_trace_id(),
                "status_code": exc.status_code,
                "request_path": request.url.path,
                "request_method": request.method,
            },
        )
        return JSONResponse(
            status_code=exc.status_code,
            content={"code": exc.status_code, "message": exc.detail, "data": None},
        )

    @app.exception_handler(StarletteHTTPException)
    async def starlette_http_exception_handler(request: Request, exc: StarletteHTTPException):
        logger.warning(
            f"Starlette HTTP exception occurred: {exc.detail}",
            extra={
                "trace_id": get_trace_id(),
                "status_code": exc.status_code,
                "request_path": request.url.path,
                "request_method": request.method,
            },
        )
        return JSONResponse(
            status_code=exc.status_code,
            content={"code": exc.status_code, "message": exc.detail, "data": None},
        )

    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(request: Request, exc: RequestValidationError):
        logger.warning(
            f"Validation error occurred: {exc.errors()}",
            extra={
                "trace_id": get_trace_id(),
                "validation_errors": exc.errors(),
                "request_path": request.url.path,
                "request_method": request.method,
            },
        )
        return JSONResponse(
            status_code=422,
            content={
                "code": 10003,
                "message": "数据验证失败",
                "data": {"errors": exc.errors()},
            },
        )

    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        logger.error(
            f"Unexpected error occurred: {exc!s}",
            extra={
                "trace_id": get_trace_id(),
                "exception_type": type(exc).__name__,
                "request_path": request.url.path,
                "request_method": request.method,
            },
            exc_info=True,
        )
        return JSONResponse(
            status_code=500,
            content={"code": 50000, "message": "服务器内部错误", "data": None},
        )
