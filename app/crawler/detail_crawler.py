"""医学分类详情爬虫

重构自原始的 detail_crawler.py，增加错误处理和重试机制
"""

import asyncio
import json
from urllib.parse import urljoin, urlparse

from crawl4ai import AsyncWebCrawler, CacheMode, CrawlerRunConfig
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy


class DetailCrawler:
    """医学分类详情爬虫"""

    def __init__(self, config=None):
        self.config = config
        self.retry_times = getattr(config, "retry_times", 3) if config else 3
        self.delay_between_requests = getattr(config, "delay_between_requests", 1.0) if config else 1.0

        # 详情页面提取Schema
        self.detail_schema = {
            "name": "Medical Sections",
            "baseSelector": 'div[data-testid="AccordionSection"]',
            "fields": [
                {
                    "name": "SectionId",
                    "selector": "a",
                    "type": "text",
                },
                {
                    "name": "SectionData",
                    "type": "list",
                    "selector": 'div[data-testid="dataTree"] ul > li',
                    "fields": [
                        {"name": "title", "selector": "a", "type": "text"},
                        {
                            "name": "url",
                            "selector": "a",
                            "type": "attribute",
                            "attribute": "href",
                        },
                    ],
                },
            ],
        }

        # 配置爬虫运行参数
        self.crawler_config = CrawlerRunConfig(
            cache_mode=CacheMode.BYPASS,
            extraction_strategy=JsonCssExtractionStrategy(self.detail_schema, verbose=False),
            wait_for='css:[data-testid="AccordionSection"]',  # 等待Section加载
            scan_full_page=True,
            scroll_delay=0.5,
        )

    async def crawl_detail_page(self, category_url: str) -> list[dict] | None:
        """爬取单个分类的详情页面

        Args:
            category_url: 分类页面URL

        Returns:
            Optional[List[Dict]]: 详情数据列表，失败时返回None
        """
        for attempt in range(self.retry_times):
            try:
                print(f"📋 正在爬取详情页面: {category_url} (尝试 {attempt + 1}/{self.retry_times})")

                async with AsyncWebCrawler(verbose=False) as crawler:
                    result = await crawler.arun(url=category_url, config=self.crawler_config)

                if result.success and result.extracted_content:
                    details = self._process_detail_result(result.extracted_content, category_url)
                    if details:
                        print(f"✅ 成功提取 {len(details)} 个详情分组")
                        return details
                    print("⚠️ 详情提取结果为空")
                else:
                    print(f"❌ 详情提取失败: {result.error_message if result.error_message else '未知错误'}")

                # 如果不是最后一次尝试，等待后重试
                if attempt < self.retry_times - 1:
                    wait_time = (attempt + 1) * 2
                    print(f"⏳ 等待 {wait_time} 秒后重试...")
                    await asyncio.sleep(wait_time)

            except Exception as e:
                print(f"❌ 爬取详情页面时发生异常: {e!s}")
                if attempt < self.retry_times - 1:
                    wait_time = (attempt + 1) * 2
                    print(f"⏳ 等待 {wait_time} 秒后重试...")
                    await asyncio.sleep(wait_time)
                else:
                    print(f"❌ 详情页面爬取失败: {category_url}")
                    return None

        return None

    def _process_detail_result(self, extracted_content: str, source_url: str) -> list[dict] | None:
        """处理详情提取结果

        Args:
            extracted_content: 提取的原始内容
            source_url: 来源URL，用于处理相对链接

        Returns:
            Optional[List[Dict]]: 处理后的详情列表
        """
        try:
            data = json.loads(extracted_content)

            if not isinstance(data, list):
                print("⚠️ 详情提取结果格式不正确，期望列表格式")
                return None

            processed_details = []
            base_url = self._get_base_url(source_url)

            for section in data:
                if not isinstance(section, dict) or "SectionData" not in section:
                    continue

                section_id = section.get("SectionId", "").strip()
                section_data = section.get("SectionData", [])

                if not section_id or not section_data:
                    continue

                # 处理section内的数据，去重并标准化URL
                processed_section_data = self._process_section_data(section_data, base_url)

                if processed_section_data:
                    processed_details.append({"SectionId": section_id, "SectionData": processed_section_data})

            return processed_details if processed_details else None

        except json.JSONDecodeError as e:
            print(f"❌ 详情JSON解析失败: {e}")
            print("原始内容预览:")
            print(extracted_content[:500] + "..." if len(extracted_content) > 500 else extracted_content)
            return None
        except Exception as e:
            print(f"❌ 处理详情提取结果时发生错误: {e}")
            return None

    def _process_section_data(self, section_data: list[dict], base_url: str) -> list[dict]:
        """处理section数据，去重并标准化URL

        Args:
            section_data: section原始数据
            base_url: 基础URL

        Returns:
            List[Dict]: 处理后的数据
        """
        seen_urls = set()
        unique_section_data = []

        for item in section_data:
            if not isinstance(item, dict):
                continue

            title = item.get("title", "").strip()
            url = item.get("url", "").strip()

            if not title or not url:
                continue

            # 标准化URL
            if url.startswith("/"):
                url = base_url + url
            elif not url.startswith("http"):
                url = urljoin(base_url, url)

            # 去掉锚点部分进行去重
            base_url_for_dedup = url.split("#")[0]

            # 如果这个基础URL还没有见过，则保留这个条目
            if base_url_for_dedup and base_url_for_dedup not in seen_urls:
                seen_urls.add(base_url_for_dedup)

                # 更新URL为去掉锚点的版本
                unique_section_data.append({"title": title, "url": base_url_for_dedup})

        return unique_section_data

    def _get_base_url(self, url: str) -> str:
        """从URL中提取基础URL

        Args:
            url: 完整URL

        Returns:
            str: 基础URL
        """
        parsed = urlparse(url)
        return f"{parsed.scheme}://{parsed.netloc}"

    async def crawl_multiple_details(self, category_urls: list[str]) -> list[dict]:
        """批量爬取多个分类的详情页面

        Args:
            category_urls: 分类URL列表

        Returns:
            List[Dict]: 所有详情数据的汇总列表
        """
        print(f"📋 开始批量爬取 {len(category_urls)} 个分类的详情...")

        all_details = []
        failed_urls = []

        for i, url in enumerate(category_urls):
            print(f"\n处理进度: {i + 1}/{len(category_urls)}")

            details = await self.crawl_detail_page(url)

            if details:
                all_details.extend(details)
            else:
                failed_urls.append(url)

            # 添加延迟避免请求过快
            if i < len(category_urls) - 1:  # 最后一个不需要延迟
                await asyncio.sleep(self.delay_between_requests)

        print("\n✅ 批量爬取完成:")
        print(f"  成功: {len(category_urls) - len(failed_urls)}/{len(category_urls)}")
        print(f"  失败: {len(failed_urls)}")
        print(f"  总详情数: {len(all_details)}")

        if failed_urls:
            print("\n❌ 失败的URL:")
            for url in failed_urls:
                print(f"  - {url}")

        return all_details

    def save_details(self, details: list[dict], output_file: str = "medical_details.json"):
        """保存详情数据到文件

        Args:
            details: 详情数据列表
            output_file: 输出文件名
        """
        try:
            from pathlib import Path

            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)

            with open(output_path, "w", encoding="utf-8") as f:
                json.dump(details, f, ensure_ascii=False, indent=2)

            print(f"📁 详情数据已保存至: {output_path}")

        except Exception as e:
            print(f"❌ 保存详情数据失败: {e}")


# 兼容原有接口的独立函数
async def main(url: str):
    """兼容原有接口的函数"""
    crawler = DetailCrawler()
    details = await crawler.crawl_detail_page(url)

    if details:
        print("处理后的结果:")
        print(json.dumps(details, ensure_ascii=False, indent=2))

    return details


if __name__ == "__main__":
    # 测试代码
    async def test():
        crawler = DetailCrawler()
        test_url = "https://www.msdmanuals.cn/professional/nutritional-disorders"

        details = await crawler.crawl_detail_page(test_url)

        if details:
            crawler.save_details(details, "test_details.json")
            print("\n📋 获取到的详情分组:")
            for detail in details:
                section_id = detail.get("SectionId", "")
                section_data = detail.get("SectionData", [])
                print(f"  - {section_id}: {len(section_data)} 个项目")
        else:
            print("❌ 未能获取到任何详情信息")

    asyncio.run(test())
