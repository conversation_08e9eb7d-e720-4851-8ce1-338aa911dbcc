"""爬虫结果报告生成器

生成详细的爬取报告和站点地图
"""

# ruff: noqa

import json
import xml.etree.ElementTree as ET
from datetime import datetime
from pathlib import Path


class ReportGenerator:
    """爬虫报告生成器"""

    def __init__(self, base_output_dir: Path):
        self.base_output_dir = Path(base_output_dir)

    async def generate_detailed_report(self, crawl_result, report_dir: Path):
        """生成详细的爬取报告

        Args:
            crawl_result: 爬取结果对象
            report_dir: 报告输出目录
        """
        report_dir.mkdir(parents=True, exist_ok=True)

        # 生成主报告
        await self._generate_main_report(crawl_result, report_dir)

        # 生成统计报告
        await self._generate_statistics_report(crawl_result, report_dir)

        # 生成失败URL报告
        if crawl_result.failed_urls:
            await self._generate_failed_urls_report(crawl_result.failed_urls, report_dir)

        print(f"📊 详细报告已生成至: {report_dir}")

    async def _generate_main_report(self, crawl_result, report_dir: Path):
        """生成主报告"""
        report_file = report_dir / "crawl_report.md"

        duration_str = self._format_duration(crawl_result.duration)
        success_rate = self._calculate_success_rate(crawl_result)

        content = f"""# 医学信息爬虫报告

## 基本信息
- **爬取时间**: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
- **总耗时**: {duration_str}
- **输出目录**: {crawl_result.output_directory}
- **爬取状态**: {"✅ 成功" if crawl_result.success else "❌ 失败"}

## 爬取统计

### 数据概览
| 类型 | 数量 |
|------|------|
| 医学主题分类 | {crawl_result.total_categories} |
| 详情分组 | {crawl_result.total_details} |
| 页面内容 | {crawl_result.total_pages} |
| 失败URL | {len(crawl_result.failed_urls)} |

### 成功率分析
- **整体成功率**: {success_rate:.1f}%
- **分类爬取**: {"✅" if crawl_result.total_categories > 0 else "❌"}
- **详情爬取**: {"✅" if crawl_result.total_details > 0 else "❌"}
- **内容爬取**: {"✅" if crawl_result.total_pages > 0 else "❌"}

## 性能指标
- **平均处理速度**: {self._calculate_avg_speed(crawl_result):.2f} 页面/秒
- **数据完整性**: {self._calculate_data_integrity(crawl_result):.1f}%

## 文件结构
```
{crawl_result.output_directory}/
├── categories.json          # 医学主题分类数据
├── details.json             # 详情分组数据
├── pages_info.json          # 页面信息汇总
├── pages/                   # 页面内容目录
│   ├── *.md                 # Markdown格式的页面内容
└── reports/                 # 报告目录
    ├── crawl_report.md      # 本报告
    ├── statistics.json      # 统计数据
    ├── sitemap.xml          # 站点地图
    └── failed_urls.txt      # 失败URL列表
```

---
*报告生成时间: {datetime.now().isoformat()}*
"""

        with open(report_file, "w", encoding="utf-8") as f:
            f.write(content)

    async def _generate_statistics_report(self, crawl_result, report_dir: Path):
        """生成统计数据报告"""
        stats_file = report_dir / "statistics.json"

        statistics = {
            "crawl_metadata": {
                "start_time": datetime.now().isoformat(),
                "duration_seconds": crawl_result.duration,
                "success": crawl_result.success,
                "output_directory": crawl_result.output_directory,
            },
            "data_counts": {
                "total_categories": crawl_result.total_categories,
                "total_details": crawl_result.total_details,
                "total_pages": crawl_result.total_pages,
                "failed_urls": len(crawl_result.failed_urls),
            },
            "performance_metrics": {
                "avg_speed_pages_per_second": self._calculate_avg_speed(crawl_result),
                "success_rate_percentage": self._calculate_success_rate(crawl_result),
                "data_integrity_percentage": self._calculate_data_integrity(crawl_result),
            },
            "failed_urls": crawl_result.failed_urls,
        }

        with open(stats_file, "w", encoding="utf-8") as f:
            json.dump(statistics, f, ensure_ascii=False, indent=2)

    async def _generate_failed_urls_report(self, failed_urls: list[str], report_dir: Path):
        """生成失败URL报告"""
        failed_file = report_dir / "failed_urls.txt"

        content = "# 爬取失败的URL列表\n"
        content += f"# 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        content += f"# 失败数量: {len(failed_urls)}\n\n"

        for i, url in enumerate(failed_urls, 1):
            content += f"{i:4d}. {url}\n"

        with open(failed_file, "w", encoding="utf-8") as f:
            f.write(content)

    async def generate_sitemap(self, pages_data: list[dict], report_dir: Path):
        """生成站点地图

        Args:
            pages_data: 页面数据列表
            report_dir: 报告输出目录
        """
        report_dir.mkdir(parents=True, exist_ok=True)

        # 生成XML格式的站点地图
        await self._generate_xml_sitemap(pages_data, report_dir)

        # 生成HTML格式的站点地图
        await self._generate_html_sitemap(pages_data, report_dir)

        # 生成JSON格式的站点地图
        await self._generate_json_sitemap(pages_data, report_dir)

        print(f"🗺️ 站点地图已生成至: {report_dir}")

    async def _generate_xml_sitemap(self, pages_data: list[dict], report_dir: Path):
        """生成XML格式的站点地图"""
        sitemap_file = report_dir / "sitemap.xml"

        # 创建XML根元素
        urlset = ET.Element("urlset")
        urlset.set("xmlns", "http://www.sitemaps.org/schemas/sitemap/0.9")

        for page in pages_data:
            if not page.get("success", False) or not page.get("url"):
                continue

            url_elem = ET.SubElement(urlset, "url")

            # 添加URL
            loc_elem = ET.SubElement(url_elem, "loc")
            loc_elem.text = page["url"]

            # 添加最后修改时间
            lastmod_elem = ET.SubElement(url_elem, "lastmod")
            lastmod_elem.text = datetime.now().strftime("%Y-%m-%d")

            # 添加更改频率
            changefreq_elem = ET.SubElement(url_elem, "changefreq")
            changefreq_elem.text = "monthly"

            # 添加优先级
            priority_elem = ET.SubElement(url_elem, "priority")
            priority_elem.text = "0.8"

        # 写入文件
        tree = ET.ElementTree(urlset)
        ET.indent(tree, space="  ", level=0)
        tree.write(sitemap_file, encoding="utf-8", xml_declaration=True)

    async def _generate_html_sitemap(self, pages_data: list[dict], report_dir: Path):
        """生成HTML格式的站点地图"""
        sitemap_file = report_dir / "sitemap.html"

        # 按section分组
        sections = {}
        for page in pages_data:
            if not page.get("success", False):
                continue

            section = page.get("section", "未分类")
            if section not in sections:
                sections[section] = []
            sections[section].append(page)

        html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>医学信息站点地图</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        h1 {{ color: #2c3e50; }}
        h2 {{ color: #34495e; border-bottom: 1px solid #ecf0f1; }}
        .section {{ margin: 20px 0; }}
        .page-list {{ list-style-type: none; padding: 0; }}
        .page-item {{
            background: #f8f9fa;
            margin: 5px 0;
            padding: 10px;
            border-radius: 5px;
        }}
        .page-link {{
            color: #3498db;
            text-decoration: none;
            font-weight: bold;
        }}
        .page-link:hover {{ color: #2980b9; }}
        .page-info {{
            color: #7f8c8d;
            font-size: 0.9em;
            margin-top: 5px;
        }}
        .stats {{
            background: #e8f4f8;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }}
    </style>
</head>
<body>
    <h1>医学信息站点地图</h1>

    <div class="stats">
        <h3>统计信息</h3>
        <p>总分类数: {len(sections)}</p>
        <p>总页面数: {len([p for p in pages_data if p.get("success", False)])}</p>
        <p>生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
    </div>
"""

        for section, pages in sections.items():
            html_content += f"""
    <div class="section">
        <h2>{section} ({len(pages)} 页面)</h2>
        <ul class="page-list">
"""

            for page in pages:
                title = page.get("title", "未命名页面")
                url = page.get("url", "#")
                filepath = page.get("filepath", "")

                html_content += f"""
            <li class="page-item">
                <a href="{url}" class="page-link" target="_blank">{title}</a>
                <div class="page-info">
                    URL: {url}<br>
                    本地文件: {filepath}
                </div>
            </li>
"""

            html_content += """
        </ul>
    </div>
"""

        html_content += """
</body>
</html>
"""

        with open(sitemap_file, "w", encoding="utf-8") as f:
            f.write(html_content)

    async def _generate_json_sitemap(self, pages_data: list[dict], report_dir: Path):
        """生成JSON格式的站点地图"""
        sitemap_file = report_dir / "sitemap.json"

        # 按section分组
        sections = {}
        for page in pages_data:
            if not page.get("success", False):
                continue

            section = page.get("section", "未分类")
            if section not in sections:
                sections[section] = []

            sections[section].append(
                {
                    "title": page.get("title", ""),
                    "url": page.get("url", ""),
                    "filepath": page.get("filepath", ""),
                    "filename": page.get("filename", ""),
                    "content_length": page.get("final_content_length", 0),
                }
            )

        sitemap_data = {
            "generated_at": datetime.now().isoformat(),
            "total_sections": len(sections),
            "total_pages": len([p for p in pages_data if p.get("success", False)]),
            "sections": sections,
        }

        with open(sitemap_file, "w", encoding="utf-8") as f:
            json.dump(sitemap_data, f, ensure_ascii=False, indent=2)

    def _format_duration(self, duration: float) -> str:
        """格式化持续时间"""
        if duration < 60:
            return f"{duration:.1f} 秒"
        if duration < 3600:
            minutes = int(duration // 60)
            seconds = duration % 60
            return f"{minutes} 分 {seconds:.1f} 秒"
        hours = int(duration // 3600)
        minutes = int((duration % 3600) // 60)
        seconds = duration % 60
        return f"{hours} 小时 {minutes} 分 {seconds:.1f} 秒"

    def _calculate_success_rate(self, crawl_result) -> float:
        """计算成功率"""
        total_attempted = (
            crawl_result.total_categories
            + crawl_result.total_details
            + crawl_result.total_pages
            + len(crawl_result.failed_urls)
        )

        if total_attempted == 0:
            return 0.0

        successful = crawl_result.total_categories + crawl_result.total_details + crawl_result.total_pages

        return (successful / total_attempted) * 100

    def _calculate_avg_speed(self, crawl_result) -> float:
        """计算平均处理速度"""
        if crawl_result.duration <= 0:
            return 0.0

        total_items = crawl_result.total_categories + crawl_result.total_details + crawl_result.total_pages

        return total_items / crawl_result.duration

    def _calculate_data_integrity(self, crawl_result) -> float:
        """计算数据完整性"""
        # 简单的完整性检查：是否所有阶段都有数据
        stages_completed = 0
        total_stages = 3

        if crawl_result.total_categories > 0:
            stages_completed += 1
        if crawl_result.total_details > 0:
            stages_completed += 1
        if crawl_result.total_pages > 0:
            stages_completed += 1

        return (stages_completed / total_stages) * 100

    async def generate_summary_report(self, session_dir: Path):
        """生成会话总结报告

        Args:
            session_dir: 会话目录
        """
        summary_file = session_dir / "SESSION_SUMMARY.md"

        # 收集文件信息
        files_info = []
        if (session_dir / "categories.json").exists():
            files_info.append("✅ categories.json - 医学主题分类")
        if (session_dir / "details.json").exists():
            files_info.append("✅ details.json - 详情分组数据")
        if (session_dir / "pages_info.json").exists():
            files_info.append("✅ pages_info.json - 页面信息汇总")

        pages_dir = session_dir / "pages"
        if pages_dir.exists():
            md_files = list(pages_dir.glob("*.md"))
            files_info.append(f"✅ pages/ - {len(md_files)} 个Markdown文件")

        reports_dir = session_dir / "reports"
        if reports_dir.exists():
            files_info.append("✅ reports/ - 详细报告和站点地图")

        content = f"""# 爬虫会话总结

## 会话信息
- **会话ID**: {session_dir.name}
- **生成时间**: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
- **输出目录**: {session_dir}

## 生成的文件

{chr(10).join(files_info)}

## 使用说明

### 查看分类数据
```bash
cat {session_dir}/categories.json
```

### 查看详情数据
```bash
cat {session_dir}/details.json
```

### 浏览页面内容
```bash
ls {session_dir}/pages/
```

### 查看详细报告
```bash
open {session_dir}/reports/crawl_report.md
```

### 查看站点地图
```bash
open {session_dir}/reports/sitemap.html
```

---
*自动生成于 {datetime.now().isoformat()}*
"""

        with open(summary_file, "w", encoding="utf-8") as f:
            f.write(content)

        print(f"📋 会话总结已生成: {summary_file}")


if __name__ == "__main__":
    # 测试代码
    async def test():
        from dataclasses import dataclass

        @dataclass
        class MockCrawlResult:
            success: bool = True
            total_categories: int = 10
            total_details: int = 50
            total_pages: int = 200
            failed_urls: list = None
            duration: float = 300.5
            output_directory: str = "/test/output"

            def __post_init__(self):
                if self.failed_urls is None:
                    self.failed_urls = []

        result = MockCrawlResult()
        generator = ReportGenerator(Path("/test"))

        await generator.generate_detailed_report(result, Path("/test/reports"))
        print("测试完成")

    import asyncio

    asyncio.run(test())
