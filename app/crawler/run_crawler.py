#!/usr/bin/env python3
"""医学信息爬虫主执行脚本

简单易用的命令行界面，用于启动和管理爬虫
"""

import argparse
import asyncio
from pathlib import Path
import sys

from .crawler_manager import CrawlerConfig, CrawlResult, MedicalCrawlerManager
from .state_manager import CrawlerStateManager


def print_banner():
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                  Medical Information Crawler                 ║
╠══════════════════════════════════════════════════════════════╣
║  包含: 分类 → 详情 → 页面内容 → Markdown文件                     ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)


def create_config_from_args(args) -> CrawlerConfig:
    """从命令行参数创建配置"""
    return CrawlerConfig(
        output_dir=args.output_dir,
        max_concurrent=args.concurrent,
        retry_times=args.retry,
        delay_between_requests=args.delay,
        enable_progress_bar=not args.no_progress,
        save_intermediate_results=not args.no_intermediate,
    )


async def run_crawl(args):
    """运行爬虫"""
    print_banner()

    # 创建配置
    config = create_config_from_args(args)

    # 显示配置信息
    print("🔧 爬虫配置:")
    print(f"  📁 输出目录: {config.output_dir}")
    print(f"  🔄 并发数: {config.max_concurrent}")
    print(f"  🔁 重试次数: {config.retry_times}")
    print(f"  ⏱️ 请求间隔: {config.delay_between_requests}秒")
    print(f"  📊 进度条: {'开启' if config.enable_progress_bar else '关闭'}")
    print()

    # 创建爬虫管理器
    crawler_manager = MedicalCrawlerManager(config)

    try:
        # 运行爬虫
        result = await crawler_manager.run_full_crawl(resume=args.resume)

        # 显示结果
        print_crawl_summary(result)

        return 0

    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断爬虫，正在保存状态...")
        crawler_manager.stop_crawl()
        print("💾 状态已保存，可以使用 --resume 参数恢复")
        return 1

    except Exception as e:
        print(f"\n\n❌ 爬虫运行失败: {e!s}")
        return 1


def print_crawl_summary(result: CrawlResult):
    """打印爬虫结果摘要"""
    print("\n" + "=" * 60)
    print("📊 爬虫完成摘要")
    print("=" * 60)

    if result.success:
        print("✅ 状态: 成功完成")
    else:
        print("❌ 状态: 部分失败")

    print(f"📋 医学主题分类: {result.total_categories}")
    print(f"📂 详情分组: {result.total_details}")
    print(f"📄 页面内容: {result.total_pages}")
    print(f"⏱️ 总耗时: {result.duration:.1f}秒")

    if result.failed_urls:
        print(f"❌ 失败URL: {len(result.failed_urls)}")

    print(f"📁 输出目录: {result.output_directory}")
    print("\n💡 提示: 查看 reports/ 目录获取详细报告和站点地图")


async def show_status(args):
    """显示爬虫状态"""
    state_file = Path(args.output_dir) / "crawler_state.json"
    state_manager = CrawlerStateManager(state_file)

    state = state_manager.load_state()

    if state is None:
        print("📋 当前没有运行中的爬虫会话")
        return 0

    resume_info = state_manager.get_resume_info()

    print("📊 爬虫状态信息")
    print("=" * 40)
    print(f"会话ID: {resume_info['session_id']}")
    print(f"开始时间: {resume_info['start_time']}")
    print(f"当前阶段: {resume_info.get('current_stage', '未知')}")
    print()

    print("阶段完成情况:")
    completed = resume_info["completed_stages"]
    print(f"  分类爬取: {'✅' if completed['categories'] else '⏳'}")
    print(f"  详情爬取: {'✅' if completed['details'] else '⏳'}")
    print(f"  内容爬取: {'✅' if completed['pages'] else '⏳'}")
    print()

    stats = resume_info["statistics"]
    print("统计信息:")
    print(f"  分类数: {stats['total_categories']}")
    print(f"  详情数: {stats['total_details']}")
    print(f"  页面数: {stats['total_pages']}")
    print(f"  失败数: {stats['failed_urls']}")

    if resume_info["can_resume"]:
        print("\n💡 可以使用 --resume 参数继续爬虫")

    return 0


async def clear_state(args):
    """清除爬虫状态"""
    state_file = Path(args.output_dir) / "crawler_state.json"
    state_manager = CrawlerStateManager(state_file)

    if state_file.exists():
        state_manager.clear_state()
        print("🗑️ 爬虫状态已清除")
    else:
        print("📋 没有找到爬虫状态文件")

    return 0


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="医学信息爬虫系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 开始新的爬虫
  python run_crawler.py start

  # 从中断处恢复爬虫
  python run_crawler.py start --resume

  # 查看爬虫状态
  python run_crawler.py status

  # 清除爬虫状态
  python run_crawler.py clear

  # 自定义配置
  python run_crawler.py start --output-dir ./my_data --concurrent 3 --delay 2
        """,
    )

    subparsers = parser.add_subparsers(dest="command", help="可用命令")

    # start 命令
    start_parser = subparsers.add_parser("start", help="开始爬虫")
    start_parser.add_argument("--output-dir", default="crawled_data", help="输出目录 (默认: crawled_data)")
    start_parser.add_argument("--concurrent", type=int, default=5, help="并发数 (默认: 5)")
    start_parser.add_argument("--retry", type=int, default=3, help="重试次数 (默认: 3)")
    start_parser.add_argument("--delay", type=float, default=1.0, help="请求间隔秒数 (默认: 1.0)")
    start_parser.add_argument("--resume", action="store_true", help="从上次中断处恢复")
    start_parser.add_argument("--no-progress", action="store_true", help="禁用进度条")
    start_parser.add_argument("--no-intermediate", action="store_true", help="不保存中间结果")

    # status 命令
    status_parser = subparsers.add_parser("status", help="查看爬虫状态")
    status_parser.add_argument("--output-dir", default="crawled_data", help="输出目录 (默认: crawled_data)")

    # clear 命令
    clear_parser = subparsers.add_parser("clear", help="清除爬虫状态")
    clear_parser.add_argument("--output-dir", default="crawled_data", help="输出目录 (默认: crawled_data)")

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return 1

    try:
        if args.command == "start":
            return asyncio.run(run_crawl(args))
        if args.command == "status":
            return asyncio.run(show_status(args))
        if args.command == "clear":
            return asyncio.run(clear_state(args))
        parser.print_help()
        return 1

    except KeyboardInterrupt:
        print("\n用户中断")
        return 1
    except Exception as e:
        print(f"程序执行失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
