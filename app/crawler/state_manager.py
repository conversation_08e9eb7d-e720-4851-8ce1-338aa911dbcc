"""爬虫状态管理器

负责管理爬虫的运行状态，支持暂停和恢复功能
"""

from dataclasses import asdict, dataclass
from datetime import datetime
import json
from pathlib import Path


@dataclass
class CrawlerState:
    """爬虫状态数据结构"""

    session_id: str
    start_time: str
    end_time: str | None = None
    status: str = "running"  # running, paused, completed, failed
    current_stage: str = "init"  # init, categories, details, pages, completed

    # 各阶段数据
    categories: list[dict] = None
    details: list[dict] = None
    pages: list[dict] = None

    # 各阶段完成状态
    categories_completed: bool = False
    details_completed: bool = False
    pages_completed: bool = False

    # 错误记录
    failed_urls: list[dict] = None
    error_message: str | None = None

    # 统计信息
    total_categories: int = 0
    total_details: int = 0
    total_pages: int = 0

    def __post_init__(self):
        if self.categories is None:
            self.categories = []
        if self.details is None:
            self.details = []
        if self.pages is None:
            self.pages = []
        if self.failed_urls is None:
            self.failed_urls = []


class CrawlerStateManager:
    """爬虫状态管理器"""

    def __init__(self, state_file: Path):
        self.state_file = Path(state_file)
        self.state_file.parent.mkdir(parents=True, exist_ok=True)
        self.current_state: CrawlerState | None = None

    def create_new_session(self, session_id: str) -> CrawlerState:
        """创建新的爬虫会话"""
        self.current_state = CrawlerState(
            session_id=session_id, start_time=datetime.now().isoformat(), status="running", current_stage="init"
        )
        self.save_state()
        return self.current_state

    def load_state(self) -> CrawlerState | None:
        """加载之前的状态"""
        if not self.state_file.exists():
            return None

        try:
            with open(self.state_file, encoding="utf-8") as f:
                data = json.load(f)
                self.current_state = CrawlerState(**data)
                return self.current_state
        except Exception as e:
            print(f"⚠️ 加载状态文件失败: {e}")
            return None

    def save_state(self):
        """保存当前状态"""
        if self.current_state is None:
            return

        try:
            with open(self.state_file, "w", encoding="utf-8") as f:
                json.dump(asdict(self.current_state), f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"⚠️ 保存状态文件失败: {e}")

    def get_state(self) -> dict:
        """获取当前状态"""
        if self.current_state is None:
            return {}
        return asdict(self.current_state)

    def update_stage(self, stage: str):
        """更新当前阶段"""
        if self.current_state:
            self.current_state.current_stage = stage
            self.save_state()

    def update_categories(self, categories: list[dict]):
        """更新分类数据"""
        if self.current_state:
            self.current_state.categories = categories
            self.current_state.categories_completed = True
            self.current_state.total_categories = len(categories)
            self.current_state.current_stage = "categories"
            self.save_state()

    def update_details(self, details: list[dict]):
        """更新详情数据"""
        if self.current_state:
            self.current_state.details = details
            self.current_state.details_completed = True
            self.current_state.total_details = len(details)
            self.current_state.current_stage = "details"
            self.save_state()

    def update_pages(self, pages: list[dict]):
        """更新页面数据"""
        if self.current_state:
            self.current_state.pages = pages
            self.current_state.pages_completed = True
            self.current_state.total_pages = len(pages)
            self.current_state.current_stage = "pages"
            self.save_state()

    def add_failed_url(self, url: str, error: str):
        """添加失败的URL"""
        if self.current_state:
            self.current_state.failed_urls.append({"url": url, "error": error, "timestamp": datetime.now().isoformat()})
            self.save_state()

    def get_failed_urls(self) -> list[str]:
        """获取失败的URL列表"""
        if self.current_state:
            return [item["url"] for item in self.current_state.failed_urls]
        return []

    def mark_session_completed(self):
        """标记会话完成"""
        if self.current_state:
            self.current_state.status = "completed"
            self.current_state.end_time = datetime.now().isoformat()
            self.current_state.current_stage = "completed"
            self.save_state()

    def mark_session_failed(self, error: str):
        """标记会话失败"""
        if self.current_state:
            self.current_state.status = "failed"
            self.current_state.error_message = error
            self.current_state.end_time = datetime.now().isoformat()
            self.save_state()

    def mark_session_paused(self):
        """标记会话暂停"""
        if self.current_state:
            self.current_state.status = "paused"
            self.save_state()

    def resume_session(self):
        """恢复会话"""
        if self.current_state:
            self.current_state.status = "running"
            self.save_state()

    def get_resume_info(self) -> dict:
        """获取恢复信息"""
        if self.current_state is None:
            return {"can_resume": False}

        return {
            "can_resume": True,
            "session_id": self.current_state.session_id,
            "start_time": self.current_state.start_time,
            "current_stage": self.current_state.current_stage,
            "completed_stages": {
                "categories": self.current_state.categories_completed,
                "details": self.current_state.details_completed,
                "pages": self.current_state.pages_completed,
            },
            "statistics": {
                "total_categories": self.current_state.total_categories,
                "total_details": self.current_state.total_details,
                "total_pages": self.current_state.total_pages,
                "failed_urls": len(self.current_state.failed_urls),
            },
        }

    def clear_state(self):
        """清除状态"""
        if self.state_file.exists():
            self.state_file.unlink()
        self.current_state = None
