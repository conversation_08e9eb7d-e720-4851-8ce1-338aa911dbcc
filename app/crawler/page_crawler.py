"""页面内容爬虫

重构自原始的 page_detail_crawler.py，增加错误处理和重试机制
"""

import asyncio
from pathlib import Path
import re
from urllib.parse import urlparse

from crawl4ai import AsyncWebCrawler, CacheMode, CrawlerRunConfig
from crawl4ai.content_filter_strategy import Pruning<PERSON>ontentFilter
from crawl4ai.markdown_generation_strategy import DefaultM<PERSON>downGenerator


def clean_markdown(text):
    # # 删除作者信息
    # text = re.sub(r"^作者：.*$", "", text, flags=re.MULTILINE)

    # # 删除审核信息
    # text = re.sub(r"^Reviewed By.*$", "", text, flags=re.MULTILINE)

    # # 删除修订信息
    # text = re.sub(r"^已审核/已修订.*$", "", text, flags=re.MULTILINE)

    # # 删除主题资源部分
    # text = re.sub(r"^## 主题资源$\n(?:\s*\*.*$\n)*", "", text, flags=re.MULTILINE)

    # # 删除测试知识部分
    # text = re.sub(r"^Test your Knowledge.*$", "", text, flags=re.MULTILINE)

    # # 删除'进行患者培训'
    # text = re.sub(r"^看法 进行患者培训.*$", "", text, flags=re.MULTILINE)

    # text = re.sub(r"^参考文献.*$", "", text, flags=re.MULTILINE)

    # # 清理多余的空行
    # text = re.sub(r"\n\s*\n\s*\n", "\n\n", text)

    return text.strip()


class PageCrawler:
    """页面内容爬虫类"""

    def __init__(self, config=None):
        self.config = config
        self.retry_times = getattr(config, "retry_times", 3) if config else 3
        self.delay_between_requests = getattr(config, "delay_between_requests", 1.0) if config else 1.0

        # 默认输出目录
        self.output_dir = Path("crawled_pages")

        # 配置markdown生成器
        self.md_generator = DefaultMarkdownGenerator(
            content_filter=PruningContentFilter(threshold=0.1, threshold_type="fixed"),
            options={
                "ignore_links": True,  # 忽略所有超链接
            },
        )

        # 配置爬虫运行参数
        self.crawler_config = CrawlerRunConfig(
            cache_mode=CacheMode.BYPASS,
            markdown_generator=self.md_generator,
            # 指定目标元素
            target_elements=["div[data-testid='topic-main-content']"],
            # 排除这些标签
            excluded_tags=["nav", "footer", "aside", "script", "style", "img"],
            exclude_external_links=True,
            exclude_external_images=True,
        )

    def set_output_dir(self, output_dir: Path):
        """设置输出目录"""
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

    def _sanitize_filename(self, title: str) -> str:
        """清理文件名，移除不合法字符

        Args:
            title: 原始标题

        Returns:
            str: 清理后的文件名
        """
        # 移除HTML标签
        title = re.sub(r"<[^>]+>", "", title)
        # 移除或替换不合法字符
        title = re.sub(r'[<>:"/\\|?*]', "_", title)
        # 移除多余空格并替换为下划线
        title = re.sub(r"\s+", "_", title.strip())
        # 移除连续的下划线
        title = re.sub(r"_+", "_", title)
        # 移除开头和结尾的下划线
        title = title.strip("_")
        # 限制长度
        if len(title) > 100:
            title = title[:100]
        # 确保文件名不为空
        if not title:
            title = "untitled"
        return title

    def _generate_filename(self, url: str, title: str, custom_filename: str | None = None) -> str:
        """生成文件名

        Args:
            url: 页面URL
            title: 页面标题
            custom_filename: 自定义文件名

        Returns:
            str: 生成的文件名
        """
        if custom_filename:
            filename = self._sanitize_filename(custom_filename)
        elif title:
            filename = self._sanitize_filename(title)
        else:
            # 使用URL路径作为文件名
            parsed_url = urlparse(url)
            path_parts = [part for part in parsed_url.path.split("/") if part]
            if path_parts:
                filename = self._sanitize_filename("_".join(path_parts[-2:]))  # 取最后两部分
            else:
                filename = "untitled"

        return filename

    def _get_unique_filepath(self, base_filename: str) -> Path:
        """获取唯一的文件路径，如果文件已存在则添加数字后缀

        Args:
            base_filename: 基础文件名

        Returns:
            Path: 唯一的文件路径
        """
        output_path = self.output_dir / f"{base_filename}.md"

        # 如果文件已存在，添加数字后缀
        counter = 1
        while output_path.exists():
            output_path = self.output_dir / f"{base_filename}_{counter}.md"
            counter += 1

        return output_path

    async def crawl_page(self, url: str, custom_filename: str | None = None) -> dict:
        """爬取单个页面

        Args:
            url: 要爬取的URL
            custom_filename: 自定义文件名（不含扩展名）

        Returns:
            Dict: 包含爬取结果的字典
        """
        # 确保输出目录存在
        self.output_dir.mkdir(parents=True, exist_ok=True)

        for attempt in range(self.retry_times):
            try:
                print(f"📄 正在爬取页面: {url} (尝试 {attempt + 1}/{self.retry_times})")

                async with AsyncWebCrawler(verbose=False) as crawler:
                    result = await crawler.arun(url, config=self.crawler_config)

                if result.success and result.markdown:
                    # 获取页面标题并移除"MSD诊疗手册专业版"
                    title = result.metadata.get("title", "") if result.metadata else ""
                    if title:
                        title = title.replace("- MSD诊疗手册专业版", "").strip()

                    # 生成文件名
                    filename = self._generate_filename(url, title, custom_filename)

                    # 获取唯一的文件路径
                    output_path = self._get_unique_filepath(filename)

                    # 准备要保存的内容
                    markdown_content = result.markdown.fit_markdown
                    markdown_content = clean_markdown(markdown_content)
                    # 如果内容太短，可能爬取不完整
                    if len(markdown_content.strip()) < 100:
                        print(f"⚠️ 页面内容可能不完整，长度: {len(markdown_content)}")
                        # 可以选择使用原始markdown
                        if hasattr(result.markdown, "raw_markdown") and len(result.markdown.raw_markdown) > len(
                            markdown_content
                        ):
                            markdown_content = result.markdown.raw_markdown

                    # 添加元数据头部
                    metadata_header = self._generate_metadata_header(url, title, result.metadata)
                    full_content = metadata_header + "\n\n" + markdown_content

                    # 写入文件
                    with open(output_path, "w", encoding="utf-8") as f:
                        f.write(full_content)

                    print(f"✅ 页面爬取成功: {title or url}")

                    # 返回结果信息
                    return {
                        "url": url,
                        "title": title,
                        "filename": output_path.name,
                        "filepath": str(output_path),
                        "raw_markdown_length": (
                            len(result.markdown.raw_markdown) if hasattr(result.markdown, "raw_markdown") else 0
                        ),
                        "fit_markdown_length": len(result.markdown.fit_markdown),
                        "final_content_length": len(full_content),
                        "success": True,
                        "metadata": result.metadata,
                    }
                error_msg = result.error_message if result.error_message else "页面内容为空或爬取失败"
                print(f"❌ 页面爬取失败: {error_msg}")

                # 如果不是最后一次尝试，等待后重试
                if attempt < self.retry_times - 1:
                    wait_time = (attempt + 1) * 2
                    print(f"⏳ 等待 {wait_time} 秒后重试...")
                    await asyncio.sleep(wait_time)

            except Exception as e:
                print(f"❌ 爬取页面时发生异常: {e!s}")
                if attempt < self.retry_times - 1:
                    wait_time = (attempt + 1) * 2
                    print(f"⏳ 等待 {wait_time} 秒后重试...")
                    await asyncio.sleep(wait_time)
                else:
                    return {"url": url, "error": str(e), "success": False}

        return {"url": url, "error": f"在 {self.retry_times} 次尝试后仍无法爬取页面", "success": False}

    def _generate_metadata_header(self, url: str, title: str, metadata: dict | None = None) -> str:
        """生成markdown文件的元数据头部

        Args:
            url: 页面URL
            title: 页面标题
            metadata: 页面元数据

        Returns:
            str: 元数据头部内容
        """
        header_lines = ["---", f"title: {title or 'Untitled'}", f"url: {url}"]

        if metadata:
            # 添加其他有用的元数据
            # if metadata.get('description'):
            #     header_lines.append(f"description: {metadata['description']}")
            if metadata.get("keywords"):
                header_lines.append(f"keywords: {metadata['keywords']}")
            if metadata.get("language"):
                header_lines.append(f"language: {metadata['language']}")

        header_lines.append("---")

        return "\n".join(header_lines)

    async def crawl_multiple_pages(self, page_urls: list[str], progress_callback=None) -> list[dict]:
        """批量爬取多个页面

        Args:
            page_urls: URL列表
            progress_callback: 进度回调函数

        Returns:
            List[Dict]: 爬取结果列表
        """
        print(f"📄 开始批量爬取 {len(page_urls)} 个页面...")

        results = []
        successful = 0
        failed = 0

        for i, url in enumerate(page_urls):
            if progress_callback:
                progress_callback(i + 1, len(page_urls), url)

            result = await self.crawl_page(url)
            results.append(result)

            if result["success"]:
                successful += 1
            else:
                failed += 1

            # 添加延迟避免请求过快
            if i < len(page_urls) - 1:  # 最后一个不需要延迟
                await asyncio.sleep(self.delay_between_requests)

        print("\n✅ 批量爬取完成:")
        print(f"  成功: {successful}/{len(page_urls)}")
        print(f"  失败: {failed}")

        return results

    def get_crawled_files_info(self) -> list[dict]:
        """获取已爬取文件的信息

        Returns:
            List[Dict]: 文件信息列表
        """
        if not self.output_dir.exists():
            return []

        files_info = []
        for file_path in self.output_dir.glob("*.md"):
            try:
                stat = file_path.stat()
                files_info.append(
                    {
                        "filename": file_path.name,
                        "filepath": str(file_path),
                        "size": stat.st_size,
                        "created_time": stat.st_ctime,
                        "modified_time": stat.st_mtime,
                    }
                )
            except Exception as e:
                print(f"⚠️ 获取文件信息失败: {file_path} - {e}")

        return files_info


# 兼容原有接口的独立函数
async def main(test_url: str):
    """测试函数"""
    crawler = PageCrawler()
    crawler.set_output_dir(Path("test_crawled_pages"))

    result = await crawler.crawl_page(test_url)

    if result["success"]:
        print(f"✅ 成功爬取: {result['title']}")
        print(f"📄 文件保存为: {result['filename']}")
        print(f"📊 内容长度: {result['final_content_length']}")
        print(f"💾 文件路径: {result['filepath']}")
    else:
        print(f"❌ 爬取失败: {result['error']}")


if __name__ == "__main__":
    asyncio.run(
        main(
            "https://www.msdmanuals.cn/professional/pediatrics/symptoms-in-infants-and-children/colic#%E8%AF%84%E4%BC%B0_v1082292_zh"
        )
    )
