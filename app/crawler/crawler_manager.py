"""医学信息爬虫管理器

这是一个统一的爬虫管理器，负责协调整个爬虫流程：
1. 爬取医学主题大分类
2. 爬取每个分类的子分类
3. 爬取具体页面内容并保存为Markdown
"""

import asyncio
from dataclasses import dataclass
from datetime import datetime
import json
from pathlib import Path
import time

from .category_crawler import CategoryCrawler
from .detail_crawler import DetailCrawler
from .page_crawler import PageCrawler
from .progress_tracker import ProgressTracker
from .report_generator import ReportGenerator
from .state_manager import CrawlerStateManager


@dataclass
class CrawlerConfig:
    """爬虫配置"""

    output_dir: str = "crawled_data"
    max_concurrent: int = 5
    retry_times: int = 3
    delay_between_requests: float = 1.0
    enable_progress_bar: bool = True
    save_intermediate_results: bool = True


@dataclass
class CrawlResult:
    """爬取结果"""

    success: bool
    total_categories: int = 0
    total_details: int = 0
    total_pages: int = 0
    failed_urls: list[str] = None
    duration: float = 0.0
    output_directory: str = ""

    def __post_init__(self):
        if self.failed_urls is None:
            self.failed_urls = []


class MedicalCrawlerManager:
    """医学信息爬虫管理器"""

    def __init__(self, config: CrawlerConfig = None):
        self.config = config or CrawlerConfig()
        self.output_dir = Path(self.config.output_dir)
        self.output_dir.mkdir(exist_ok=True)

        # 初始化各个组件
        self.state_manager = CrawlerStateManager(self.output_dir / "crawler_state.json")
        self.progress_tracker = ProgressTracker(enabled=self.config.enable_progress_bar)
        self.report_generator = ReportGenerator(self.output_dir)

        # 初始化各个爬虫
        self.category_crawler = CategoryCrawler(self.config)
        self.detail_crawler = DetailCrawler(self.config)
        self.page_crawler = PageCrawler(self.config)

        # 运行状态
        self.is_running = False
        self.start_time = None
        self.session_id = None

    async def run_full_crawl(self, resume: bool = False) -> CrawlResult:
        """运行完整的爬虫流程

        Args:
            resume: 是否从上次中断处继续

        Returns:
            CrawlResult: 爬取结果
        """
        if self.is_running:
            raise RuntimeError("爬虫已在运行中")

        self.is_running = True
        self.start_time = time.time()
        self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")

        try:
            # 加载或创建状态
            if resume:
                self.state_manager.load_state()
                print(f"📋 从上次会话恢复爬虫，会话ID: {self.session_id}")
            else:
                self.state_manager.create_new_session(self.session_id)
                print(f"🚀 开始新的爬虫会话，会话ID: {self.session_id}")

            # 创建会话输出目录
            session_dir = self.output_dir / self.session_id
            session_dir.mkdir(exist_ok=True)

            # 阶段1: 爬取分类
            print("\n" + "=" * 50)
            print("🔍 阶段 1: 爬取医学主题分类")
            print("=" * 50)
            categories = await self._crawl_categories()

            # 阶段2: 爬取详情
            print("\n" + "=" * 50)
            print("📋 阶段 2: 爬取分类详情")
            print("=" * 50)
            details = await self._crawl_details(categories)

            # 阶段3: 爬取页面内容
            print("\n" + "=" * 50)
            print("📄 阶段 3: 爬取页面内容")
            print("=" * 50)
            pages = await self._crawl_pages(details)

            # 生成结果
            duration = time.time() - self.start_time
            result = CrawlResult(
                success=True,
                total_categories=len(categories),
                total_details=len(details),
                total_pages=len(pages),
                failed_urls=self.state_manager.get_failed_urls(),
                duration=duration,
                output_directory=str(session_dir),
            )

            # 保存最终状态
            self.state_manager.mark_session_completed()

            # 生成报告
            await self._generate_reports(result)

            print(f"\n✅ 爬虫完成！总耗时: {duration:.2f}秒")
            print(f"📁 输出目录: {session_dir}")

            return result

        except Exception as e:
            print(f"\n❌ 爬虫过程中发生错误: {e!s}")
            self.state_manager.mark_session_failed(str(e))
            raise
        finally:
            self.is_running = False

    async def _crawl_categories(self) -> list[dict]:
        """爬取分类信息"""
        state = self.state_manager.get_state()

        if state.get("categories_completed", False):
            print("📋 分类信息已存在，跳过此步骤")
            return state.get("categories", [])

        print("🔍 开始爬取医学主题分类...")
        categories = await self.category_crawler.extract_medical_sections()

        if not categories:
            raise RuntimeError("未能获取到任何分类信息")

        # 保存中间结果
        if self.config.save_intermediate_results:
            categories_file = self.output_dir / self.session_id / "categories.json"
            categories_file.parent.mkdir(exist_ok=True)
            with open(categories_file, "w", encoding="utf-8") as f:
                json.dump(categories, f, ensure_ascii=False, indent=2)

        # 更新状态
        self.state_manager.update_categories(categories)

        print(f"✅ 成功获取 {len(categories)} 个分类")
        return categories

    async def _crawl_details(self, categories: list[dict]) -> list[dict]:
        """爬取详情信息"""
        state = self.state_manager.get_state()

        if state.get("details_completed", False):
            print("📋 详情信息已存在，跳过此步骤")
            return state.get("details", [])

        print(f"📋 开始爬取 {len(categories)} 个分类的详情...")

        # 初始化进度追踪
        self.progress_tracker.start_stage("details", len(categories))

        all_details = []

        # 控制并发数
        semaphore = asyncio.Semaphore(self.config.max_concurrent)

        async def crawl_single_detail(category: dict) -> list[dict] | None:
            async with semaphore:
                try:
                    # 添加延迟
                    await asyncio.sleep(self.config.delay_between_requests)

                    details = await self.detail_crawler.crawl_detail_page(category["url"])

                    if details:
                        self.progress_tracker.update_progress(f"完成: {category['title']}")
                        return details
                    self.state_manager.add_failed_url(category["url"], "详情爬取失败")
                    return None

                except Exception as e:
                    self.state_manager.add_failed_url(category["url"], str(e))
                    self.progress_tracker.update_progress(f"失败: {category['title']}")
                    return None

        # 并发爬取
        tasks = [crawl_single_detail(cat) for cat in categories]
        results = await asyncio.gather(*tasks)

        # 收集结果
        for result in results:
            if result:
                all_details.extend(result)

        self.progress_tracker.finish_stage()

        # 保存中间结果
        if self.config.save_intermediate_results:
            details_file = self.output_dir / self.session_id / "details.json"
            with open(details_file, "w", encoding="utf-8") as f:
                json.dump(all_details, f, ensure_ascii=False, indent=2)

        # 更新状态
        self.state_manager.update_details(all_details)

        print(f"✅ 成功获取 {len(all_details)} 个详情页面")
        return all_details

    async def _crawl_pages(self, details: list[dict]) -> list[dict]:
        """爬取页面内容"""
        state = self.state_manager.get_state()

        if state.get("pages_completed", False):
            print("📄 页面内容已存在，跳过此步骤")
            return state.get("pages", [])

        # 展开所有页面URL
        all_page_urls = []
        for detail in details:
            if "SectionData" in detail:
                for item in detail["SectionData"]:
                    if item.get("url"):
                        all_page_urls.append(
                            {"url": item["url"], "title": item.get("title", ""), "section": detail.get("SectionId", "")}
                        )

        print(f"📄 开始爬取 {len(all_page_urls)} 个页面内容...")

        # 初始化进度追踪
        self.progress_tracker.start_stage("pages", len(all_page_urls))

        # 创建页面输出目录
        pages_dir = self.output_dir / self.session_id / "pages"
        pages_dir.mkdir(exist_ok=True)

        crawled_pages = []

        # 控制并发数
        semaphore = asyncio.Semaphore(self.config.max_concurrent)

        async def crawl_single_page(page_info: dict) -> dict | None:
            async with semaphore:
                try:
                    # 添加延迟
                    await asyncio.sleep(self.config.delay_between_requests)

                    # 设置输出目录
                    self.page_crawler.set_output_dir(pages_dir)

                    result = await self.page_crawler.crawl_page(
                        page_info["url"], custom_filename=f"{page_info['section']}_{page_info['title']}"
                    )

                    if result["success"]:
                        result.update(page_info)
                        self.progress_tracker.update_progress(f"完成: {page_info['title']}")
                        return result
                    self.state_manager.add_failed_url(page_info["url"], result.get("error", "页面爬取失败"))
                    return None

                except Exception as e:
                    self.state_manager.add_failed_url(page_info["url"], str(e))
                    self.progress_tracker.update_progress(f"失败: {page_info['title']}")
                    return None

        # 并发爬取
        tasks = [crawl_single_page(page) for page in all_page_urls]
        results = await asyncio.gather(*tasks)

        # 收集结果
        for result in results:
            if result:
                crawled_pages.append(result)

        self.progress_tracker.finish_stage()

        # 保存中间结果
        if self.config.save_intermediate_results:
            pages_file = self.output_dir / self.session_id / "pages_info.json"
            with open(pages_file, "w", encoding="utf-8") as f:
                json.dump(crawled_pages, f, ensure_ascii=False, indent=2)

        # 更新状态
        self.state_manager.update_pages(crawled_pages)

        print(f"✅ 成功爬取 {len(crawled_pages)} 个页面")
        return crawled_pages

    async def _generate_reports(self, result: CrawlResult):
        """生成报告"""
        print("\n📊 生成爬取报告...")

        report_dir = self.output_dir / self.session_id / "reports"
        report_dir.mkdir(exist_ok=True)

        # 生成详细报告
        await self.report_generator.generate_detailed_report(result, report_dir)

        # 生成站点地图
        await self.report_generator.generate_sitemap(self.state_manager.get_state().get("pages", []), report_dir)

        print("✅ 报告生成完成")

    def get_progress(self) -> dict:
        """获取当前进度"""
        return {
            "is_running": self.is_running,
            "session_id": self.session_id,
            "state": self.state_manager.get_state(),
            "progress": self.progress_tracker.get_current_progress(),
        }

    def stop_crawl(self):
        """停止爬虫"""
        if self.is_running:
            self.is_running = False
            self.state_manager.save_state()
            print("🛑 爬虫已停止")
