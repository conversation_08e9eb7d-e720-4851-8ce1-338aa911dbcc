"""医学主题分类爬虫

重构自原始的 category_crawler.py，增加错误处理和重试机制
"""

import asyncio
import json
from pathlib import Path

from crawl4ai import AsyncWebCrawler, CrawlerRunConfig, JsonCssExtractionStrategy

from .schema import medical_sections_schema


class CategoryCrawler:
    """医学主题分类爬虫"""

    def __init__(self, config=None):
        self.config = config
        self.retry_times = getattr(config, "retry_times", 3) if config else 3
        self.delay_between_requests = getattr(config, "delay_between_requests", 1.0) if config else 1.0

        self.base_url = "https://www.msdmanuals.cn"
        self.target_url = "https://www.msdmanuals.cn/professional/health-topics"

        # 配置提取策略
        self.extraction_strategy = JsonCssExtractionStrategy(medical_sections_schema, verbose=False)

        # 配置爬虫运行参数
        self.crawler_config = CrawlerRunConfig(
            extraction_strategy=self.extraction_strategy,
            scan_full_page=True,
            scroll_delay=0.5,
            wait_for="css:main",  # 等待主要内容加载
        )

    async def extract_medical_sections(self) -> list[dict]:
        """提取医学主题页面的分类信息

        Returns:
            List[Dict]: 分类信息列表，格式: [{"title": "传染病", "url": "https://..."}]
        """
        for attempt in range(self.retry_times):
            try:
                print(f"🔍 正在爬取医学主题分类... (尝试 {attempt + 1}/{self.retry_times})")

                async with AsyncWebCrawler(verbose=False) as crawler:
                    result = await crawler.arun(url=self.target_url, config=self.crawler_config)

                if result.success and result.extracted_content:
                    sections = self._process_extraction_result(result.extracted_content)
                    if sections:
                        print(f"✅ 成功提取 {len(sections)} 个医学主题分类")
                        return sections
                    print("⚠️ 提取结果为空")
                else:
                    print(f"❌ 提取失败: {result.error_message if result.error_message else '未知错误'}")

                # 如果不是最后一次尝试，等待后重试
                if attempt < self.retry_times - 1:
                    wait_time = (attempt + 1) * 2  # 递增等待时间
                    print(f"⏳ 等待 {wait_time} 秒后重试...")
                    await asyncio.sleep(wait_time)

            except Exception as e:
                print(f"❌ 爬取过程中发生异常: {e!s}")
                if attempt < self.retry_times - 1:
                    wait_time = (attempt + 1) * 2
                    print(f"⏳ 等待 {wait_time} 秒后重试...")
                    await asyncio.sleep(wait_time)
                else:
                    raise e

        raise RuntimeError(f"在 {self.retry_times} 次尝试后仍无法获取分类信息")

    def _process_extraction_result(self, extracted_content: str) -> list[dict]:
        """处理提取结果

        Args:
            extracted_content: 提取的原始内容

        Returns:
            List[Dict]: 处理后的分类列表
        """
        try:
            data = json.loads(extracted_content)

            if not isinstance(data, list):
                print("⚠️ 提取结果格式不正确，期望列表格式")
                return []

            sections = []
            processed_urls = set()  # 用于去重

            for item in data:
                if not isinstance(item, dict):
                    continue

                title = item.get("title", "").strip()
                url = item.get("url", "").strip()

                if not title or not url:
                    continue

                # 处理URL
                if url.startswith("/"):
                    url = self.base_url + url
                elif not url.startswith("http"):
                    continue  # 跳过无效URL

                # 去重
                if url in processed_urls:
                    continue
                processed_urls.add(url)

                # 过滤掉非专业版链接
                if "/professional/" not in url:
                    continue

                sections.append({"title": title, "url": url})

            return sections

        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            print("原始内容预览:")
            print(extracted_content[:500] + "..." if len(extracted_content) > 500 else extracted_content)
            return []
        except Exception as e:
            print(f"❌ 处理提取结果时发生错误: {e}")
            return []

    def save_categories(self, categories: list[dict], output_file: str = "medical_categories.json"):
        """保存分类数据到文件

        Args:
            categories: 分类列表
            output_file: 输出文件名
        """
        try:
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)

            with open(output_path, "w", encoding="utf-8") as f:
                json.dump(categories, f, ensure_ascii=False, indent=2)

            print(f"📁 分类数据已保存至: {output_path}")

        except Exception as e:
            print(f"❌ 保存分类数据失败: {e}")


# 兼容原有接口的独立函数
async def extract_medical_sections():
    """兼容原有接口的函数"""
    crawler = CategoryCrawler()
    return await crawler.extract_medical_sections()


if __name__ == "__main__":

    async def main():
        crawler = CategoryCrawler()
        categories = await crawler.extract_medical_sections()

        if categories:
            # 保存结果
            crawler.save_categories(categories)

            print("\n📋 获取到的分类:")
            for cat in categories:
                print(f"  - {cat['title']}: {cat['url']}")
        else:
            print("❌ 未能获取到任何分类信息")

    asyncio.run(main())
