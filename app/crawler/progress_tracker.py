"""进度追踪器

显示爬虫的实时进度信息
"""

from dataclasses import dataclass
import time


@dataclass
class ProgressInfo:
    """进度信息"""

    stage: str
    total: int
    completed: int
    current_item: str
    start_time: float
    estimated_remaining: float | None = None

    @property
    def progress_percentage(self) -> float:
        """进度百分比"""
        if self.total == 0:
            return 0.0
        return (self.completed / self.total) * 100

    @property
    def elapsed_time(self) -> float:
        """已用时间（秒）"""
        return time.time() - self.start_time

    @property
    def rate(self) -> float:
        """处理速率（项目/秒）"""
        if self.elapsed_time == 0:
            return 0.0
        return self.completed / self.elapsed_time

    @property
    def eta(self) -> float | None:
        """预计剩余时间（秒）"""
        if self.rate == 0 or self.completed == 0:
            return None
        remaining = self.total - self.completed
        return remaining / self.rate


class ProgressTracker:
    """进度追踪器"""

    def __init__(self, enabled: bool = True):
        self.enabled = enabled
        self.current_progress: ProgressInfo | None = None
        self._last_update_time = 0
        self._update_interval = 1.0  # 1秒更新一次显示

    def start_stage(self, stage: str, total: int):
        """开始一个新阶段"""
        if not self.enabled:
            return

        self.current_progress = ProgressInfo(
            stage=stage, total=total, completed=0, current_item="", start_time=time.time()
        )
        self._print_progress()

    def update_progress(self, current_item: str = ""):
        """更新进度"""
        if not self.enabled or self.current_progress is None:
            return

        self.current_progress.completed += 1
        self.current_progress.current_item = current_item

        # 控制更新频率
        current_time = time.time()
        if current_time - self._last_update_time >= self._update_interval:
            self._print_progress()
            self._last_update_time = current_time

    def finish_stage(self):
        """完成当前阶段"""
        if not self.enabled or self.current_progress is None:
            return

        self.current_progress.completed = self.current_progress.total
        self._print_progress()
        print()  # 换行

    def _print_progress(self):
        """打印进度信息"""
        if self.current_progress is None:
            return

        progress = self.current_progress

        # 构建进度条
        bar_length = 30
        filled_length = int(bar_length * progress.progress_percentage // 100)
        bar = "█" * filled_length + "░" * (bar_length - filled_length)

        # 构建信息字符串
        info_parts = [
            f"[{bar}]",
            f"{progress.progress_percentage:.1f}%",
            f"({progress.completed}/{progress.total})",
            f"⏱️ {progress.elapsed_time:.1f}s",
        ]

        # 添加速率信息
        if progress.rate > 0:
            if progress.rate < 1:
                info_parts.append(f"📈 {progress.rate:.2f}/s")
            else:
                info_parts.append(f"📈 {progress.rate:.1f}/s")

        # 添加ETA
        if progress.eta:
            eta_min = int(progress.eta // 60)
            eta_sec = int(progress.eta % 60)
            if eta_min > 0:
                info_parts.append(f"⏰ ETA: {eta_min}m{eta_sec}s")
            else:
                info_parts.append(f"⏰ ETA: {eta_sec}s")

        # 添加当前项目
        if progress.current_item:
            info_parts.append(f"📋 {progress.current_item}")

        # 打印（使用\r覆盖当前行）
        print(f"\r{progress.stage}: {' '.join(info_parts)}", end="", flush=True)

    def get_current_progress(self) -> dict | None:
        """获取当前进度信息"""
        if self.current_progress is None:
            return None

        return {
            "stage": self.current_progress.stage,
            "total": self.current_progress.total,
            "completed": self.current_progress.completed,
            "percentage": self.current_progress.progress_percentage,
            "elapsed_time": self.current_progress.elapsed_time,
            "rate": self.current_progress.rate,
            "eta": self.current_progress.eta,
            "current_item": self.current_progress.current_item,
        }

    def set_enabled(self, enabled: bool):
        """设置是否启用进度显示"""
        self.enabled = enabled
