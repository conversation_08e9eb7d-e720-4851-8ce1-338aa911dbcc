from agno.agent import Agent
from agno.memory.v2.db.redis import RedisMemoryDb
from agno.memory.v2.memory import Memory
from agno.models.openai import OpenAIChat
from agno.storage.agent.singlestore import SingleStoreAgentStorage
from agno.tools.wikipedia import WikipediaTools

from app.core.config import settings
from app.core.dependencies import get_mysql_engine
from app.core.llm_core import get_role_map
from app.core.template_loader import AgentType, get_template_loader

# 获取模板加载器
template_loader = get_template_loader()


def get_medical_agent(
    user_id: str | None = None,
    session_id: str | None = None,
    debug_mode: bool = True,
) -> Agent:
    # 获取医疗搜索 Agent 的 prompt
    prompt = template_loader.get_config(AgentType.MEDICAL_SEARCH)

    # 获取 MySQL 数据库引擎
    db_engine = get_mysql_engine()
    llm_model = OpenAIChat(
        id=settings.LLM_MODEL, base_url=settings.LLM_BASE_URL, api_key=settings.ARK_API_KEY, role_map=get_role_map()
    )

    return Agent(
        name="Medical Assistant Agent",
        agent_id="medical_assistant_agent",
        user_id=user_id,
        session_id=session_id,
        model=llm_model,
        tools=[WikipediaTools()],
        description=prompt.description,
        instructions=template_loader.render_prompt(AgentType.MEDICAL_SEARCH),
        # 将上下文添加到用户提示中
        add_context=True,
        # 在消息中添加会话状态变量, 使 `current_user_id` 在指令中可用
        add_state_in_messages=True,
        # 在 MySQL 表中存储聊天历史和会话状态
        storage=SingleStoreAgentStorage(
            table_name="medical_assistant_agent_sessions",
            db_engine=db_engine,
            schema="ai_assistant",
        ),
        # 将聊天历史记录添加到发送给模型的消息中
        add_history_to_messages=True,
        num_history_runs=10,
        # 添加工具以在需要时读取聊天历史
        read_chat_history=True,
        # 启用智能记忆功能,使Agent能够为用户提供个性化响应
        memory=Memory(
            model=llm_model,
            db=RedisMemoryDb(
                prefix="agno_memory",
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                db=settings.REDIS_DB,
            ),
            delete_memories=True,
            clear_memories=True,
        ),
        enable_agentic_memory=True,
        # -*- 其他设置 -*-
        # 使用markdown格式化响应
        markdown=True,
        # 在指令中添加当前日期和时间
        add_datetime_to_instructions=True,
        # 显示调试日志
        debug_mode=debug_mode,
    )
