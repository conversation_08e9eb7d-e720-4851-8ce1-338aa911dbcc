"""
多Agent体检报告解析系统 - 基于 Agno Workflow
"""

from dataclasses import dataclass
import json
from pathlib import Path
from typing import Any

from agno.agent import Agent
from agno.memory import Memory
from agno.memory.workflow import WorkflowMemory
from agno.models.openai import OpenAILike
from agno.run.response import (
    RunResponseContentEvent,
    RunResponseEvent,
)
from agno.storage.base import Storage
from agno.utils.log import logger
from agno.workflow import Workflow

from app.core.llm_core import get_agent_model_config, thinking_params
from app.core.template_loader import AgentType, get_template_loader

from .models import (
    ExistingDiseasesResponse,
    MedicalImageResponse,
    OverallResultResponse,
    PersonalInfo,
    TabularParserResponse,
)


@dataclass(init=False)
class ReportAnalysisWorkflow(Workflow):
    """体检报告分析工作流"""

    description: str = "智能体检报告分析工作流，使用多个专门的Agent协同解析体检报告"

    def __init__(
        self,
        *,
        name: str | None = None,
        workflow_id: str | None = None,
        description: str | None = None,
        user_id: str | None = None,
        session_id: str | None = None,
        session_name: str | None = None,
        session_state: dict[str, Any] | None = None,
        memory: WorkflowMemory | Memory | None = None,
        storage: Storage | None = None,
        extra_data: dict[str, Any] | None = None,
        debug_mode: bool = False,
        monitoring: bool = False,
        telemetry: bool = True,
        app_id: str | None = None,
    ):
        super().__init__(
            name=name,
            workflow_id=workflow_id,
            description=description,
            user_id=user_id,
            session_id=session_id,
            session_name=session_name,
            session_state=session_state,
            memory=memory,
            storage=storage,
            extra_data=extra_data,
            debug_mode=debug_mode,
            monitoring=monitoring,
            telemetry=telemetry,
            app_id=app_id,
        )

        # 创建共享的模型实例，使用统一的配置入口
        shared_model = OpenAILike(**get_agent_model_config(), request_params=thinking_params())

        # 获取模板加载器
        template_loader = get_template_loader()

        # 1.个人信息提取器
        meta_config = template_loader.get_config(AgentType.META_EXTRACTOR)
        self.meta_extractor = Agent(
            model=OpenAILike(**get_agent_model_config(), request_params=thinking_params("disabled")),
            description=meta_config.description,
            instructions=template_loader.render_prompt(AgentType.META_EXTRACTOR),
            response_model=PersonalInfo,
            use_json_mode=True,
            debug_mode=debug_mode,
            enable_agentic_memory=False,  # 禁用代理内存
            enable_user_memories=False,  # 禁用用户记忆
            enable_session_summaries=False,  # 禁用会话摘要
            add_history_to_messages=False,  # 禁用历史消息添加
        )

        # 2.总体结果解析器(健康建议、总检等)
        overall_config = template_loader.get_config(AgentType.OVERALL_RESULT)
        prompt = template_loader.render_prompt(
            AgentType.OVERALL_RESULT,
            context_vars={
                "health_assessment_range": "健康，亚健康，慢性病，重大疾病",
                "risk_level_range": "良好，轻度风险，中度风险，中高风险，高风险"
            },
        )
        self.overall_result_parser = Agent(
            model=shared_model,
            description=overall_config.description,
            instructions=prompt,
            response_model=OverallResultResponse,
            use_json_mode=True,
            debug_mode=debug_mode,
        )

        # 3.检查项目agent
        # 读取 app目录同级的 data/medical_exam_structured.json 作为 reference_interval 的值
        current_dir = Path(__file__).parent.parent.parent  # 从 tests/llm/ 回到项目根目录
        json_file_path = current_dir / "data" / "medical_exam_structured.json"

        with open(json_file_path, encoding="utf-8") as f:
            reference_interval = json.load(f)

        tabular_config = template_loader.get_config(AgentType.TABULAR_PARSER)

        prompt = template_loader.render_prompt(
            AgentType.TABULAR_PARSER,
            context_vars={"reference_interval": json.dumps(reference_interval, ensure_ascii=False)},
        )

        self.tabular_parser = Agent(
            model=OpenAILike(**get_agent_model_config("doubao_seed_thinking")),
            description=tabular_config.description,
            instructions=prompt,
            response_model=TabularParserResponse,
            use_json_mode=True,
            debug_mode=debug_mode,
            telemetry=False,
        )

        # 4.存在的疾病列表
        disease_config = template_loader.get_config(AgentType.EXISTING_DISEASES)
        prompt = template_loader.render_prompt(
            AgentType.EXISTING_DISEASES,
            # context_vars={"disease_range": ""}, # TODO: 从数据库读取
        )
        self.existing_diseases_parser = Agent(
            model=shared_model,
            description=disease_config.description,
            instructions=prompt,
            response_model=ExistingDiseasesResponse,
            use_json_mode=True,
            debug_mode=debug_mode,
        )

        # 5.影像资料数据提取
        image_config = template_loader.get_config(AgentType.MEDICAL_IMAGING_PARSER)
        self.medical_img_parser = Agent(
            model=shared_model,
            description=image_config.description,
            instructions=template_loader.render_prompt(AgentType.MEDICAL_IMAGING_PARSER),
            response_model=MedicalImageResponse,
            use_json_mode=True,
            debug_mode=debug_mode,
        )
        
        # 6.多语言翻译Agent
        # translation_config = template_loader.get_config(AgentType.TRANSLATION)
        # self.translation_agent = Agent(
        #     model=shared_model,
        #     description=translation_config.description,
        #     instructions=template_loader.render_prompt(AgentType.TRANSLATION),
        # )
        
        # 7.指标转换Agent
        # indicator_config = template_loader.get_config(AgentType.INDICATOR_CONVERTER)
        # self.indicator_converter = Agent(
        #     model=shared_model,
        #     description=indicator_config.description,
        #     instructions=template_loader.render_prompt(AgentType.INDICATOR_CONVERTER),
        # )

    def get_agent(self, agent_type: AgentType) -> Agent:
        """根据类型获取对应的Agent"""
        agent_map = {
            AgentType.META_EXTRACTOR: self.meta_extractor,
            AgentType.OVERALL_RESULT: self.overall_result_parser,
            AgentType.TABULAR_PARSER: self.tabular_parser,
            AgentType.MEDICAL_IMAGING_PARSER: self.medical_img_parser,
            AgentType.EXISTING_DISEASES: self.existing_diseases_parser,
        }
        return agent_map[agent_type]

    def run_single_agent(self, agent_type: AgentType, input_text: str) -> RunResponseEvent:
        """运行单个Agent"""
        logger.info(f"开始运行单个Agent: {agent_type}")

        agent = self.get_agent(agent_type)
        response = agent.run(input_text, stream=False)
        return RunResponseContentEvent(run_id=self.run_id, content=response.content)


# 全局工作流实例，用于避免重复初始化
_workflow_instance = None


def get_workflow_instance() -> ReportAnalysisWorkflow:
    """获取全局工作流实例（单例模式）"""
    global _workflow_instance
    if _workflow_instance is None:
        _workflow_instance = ReportAnalysisWorkflow()
    return _workflow_instance
