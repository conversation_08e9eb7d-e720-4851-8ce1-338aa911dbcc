"""
体检报告解析的数据模型定义
"""

from pydantic import BaseModel, Field


class PersonalInfo(BaseModel):
    """体检个人信息"""

    name: str = Field(..., description="姓名")
    gender: str | None = Field(None, description="性别: 1-男 2-女")
    age: int | None = Field(None, description="年龄")
    birth_date: str | None = Field(None, description="出生日期")
    ethnicity: str | None = Field(None, description="民族")
    phone: str | None = Field(None, description="本人电话")
    id_card: str | None = Field(None, description="身份证号")
    education: str | None = Field(None, description="文化程度")
    occupation: str | None = Field(None, description="职业")
    marital_status: str | None = Field(None, description="婚姻状况")
    exam_number: str | None = Field(None, description="体检编号")
    exam_date: str | None = Field(None, description="体检日期")
    medical_center_name: str | None = Field(None, description="体检机构名称")


# --------------------------------------------------------------


class LabResultItem(BaseModel):
    """健康体检检查项目明细"""

    category: str | None = Field(None, description="项目类别, 如 一般检查，妇科检查等")
    item_name: str = Field(..., description="检查项目名称, 如 血压，心率等")
    result_value: str | None = Field(None, description="检查结果值")
    reference_range: str | None = Field(None, description="参考范围")
    unit: str | None = Field(None, description="检查结果单位")
    is_abnormal: str | None = Field(None, description="是否异常, 如 是/否")
    result_flag: str | None = Field(None, description="结果标记:正常/偏高/偏低/异常")
    remark: str | None = Field(None, description="备注说明")


class TabularParserResponse(BaseModel):
    """健康体检检查项目响应模型"""

    lab_results: list[LabResultItem] = Field(default_factory=list, description="健康体检检查项目列表")


# --------------------------------------------------------------
class RiskModel(BaseModel):
    """风险评估模型"""
    
    risk_name: str = Field(..., description="风险名称，如心血管疾病风险")
    risk_evaluation: str = Field(..., description="风险评估，如血压持续升高，建议进一步检查")


class DiseaseModel(BaseModel):
    """疾病模型"""
    
    category: str = Field(..., description="疾病分类，如心血管疾病、内分泌疾病等")
    name: str = Field(..., description="疾病名称，如高血压、糖尿病等")
    en_category: str | None = Field(None, description="疾病英文分类，如 Cardiovascular Disease、Endocrine Disease等")
    en_name: str | None = Field(None, description="疾病英文名称，如 Hypertension、Diabetes等")


class ExistingDiseasesResponse(BaseModel):
    """已存在的疾病列表"""
    
    existing_diseases: list[DiseaseModel] = Field(default_factory=list, description="已存在的疾病列表")


class OverallResultResponse(BaseModel):
    """总体结果解析器响应模型, 关于健康建议、总检结论等"""

    health_assessment: str = Field(..., description="整体健康评估, 只需要标记最合适的一个标签", examples=["健康", "亚健康", "慢性病", "重大疾病"])
    main_findings: list[str] = Field(default_factory=list, description="主要发现")
    abnormal_indicators: list[str] = Field(default_factory=list, description="异常指标项列表", examples=["高血压", "高血糖", "高血脂"])
    dietary_advice: list[str] = Field(default_factory=list, description="饮食建议列表")
    exercise_advice: list[str] = Field(default_factory=list, description="运动建议列表")
    risk_level: str = Field(..., description="风险等级, 如 良好、轻度风险、中度风险、中高风险、高风险")
    risk_assessment: list[RiskModel] = Field(default_factory=list, description="风险评估列表")


# --------------------------------------------------------------
class MedicalImageResult(BaseModel):
    """医学影像检查结果"""

    type: str = Field(..., description="检查类型, 如 超声、CT等")
    parts: str = Field(..., description="检查部位, 如 肝脏、肺部等")
    is_abnormal: str = Field(..., description="是否异常, 如 是/否", alias="isAbnormal")
    diagnosis_result: list[str] = Field(
        default_factory=list, description="诊断结果, 如 良性肿瘤、恶性肿瘤等", alias="diagnosisResult"
    )
    imgs: list[str] = Field(default_factory=list, description="相关图片链接")

    class Config:
        populate_by_name = True  # 允许同时使用字段名和别名来初始化模型


class MedicalImageResponse(BaseModel):
    """影像资料数据提取响应模型, 关于影像检查结果"""

    result: list[MedicalImageResult] = Field(default_factory=list, description="医学影像检查结果列表")


# --------------------------------------------------------------
