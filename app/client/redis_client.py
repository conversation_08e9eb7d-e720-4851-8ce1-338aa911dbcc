"""Redis 客户端管理器

提供 Redis 连接管理、连接池管理、健康检查等功能。
支持异步操作，自动重连，统一的错误处理。
"""

from fastapi import HTTPException
import redis.asyncio as redis
from structlog import get_logger

from app.core.config import settings

logger = get_logger(__name__)


class RedisManager:
    """Redis 连接管理器"""

    def __init__(self):
        self._redis_client: redis.Redis | None = None

    async def get_redis_client(self) -> redis.Redis:
        """获取 Redis 客户端

        Returns:
            redis.Redis: Redis 异步客户端实例

        Raises:
            HTTPException: 当连接失败时抛出
        """
        if self._redis_client is None:
            try:
                self._redis_client = redis.Redis(
                    host=settings.REDIS_HOST,
                    port=settings.REDIS_PORT,
                    db=settings.REDIS_DB,
                    password=settings.REDIS_PASSWORD if settings.REDIS_PASSWORD else None,
                    decode_responses=True,
                    socket_connect_timeout=5,
                    socket_timeout=5,
                    retry_on_timeout=True,
                    health_check_interval=30,
                )

                # 测试连接
                await self._redis_client.ping()
                logger.info(
                    "Redis 连接建立成功", host=settings.REDIS_HOST, port=settings.REDIS_PORT, db=settings.REDIS_DB
                )

            except redis.ConnectionError as e:
                logger.error("Redis 连接失败", host=settings.REDIS_HOST, port=settings.REDIS_PORT, error=str(e))
                raise HTTPException(status_code=503, detail=f"Redis 连接失败: {e!s}")
            except Exception as e:
                logger.error("Redis 初始化失败", error=str(e))
                raise HTTPException(status_code=500, detail=f"Redis 初始化失败: {e!s}")

        return self._redis_client

    async def close(self):
        """关闭 Redis 连接"""
        if self._redis_client:
            await self._redis_client.close()
            self._redis_client = None
            logger.info("Redis 连接已关闭")

    async def health_check(self) -> bool:
        """Redis 健康检查

        Returns:
            bool: 连接是否健康
        """
        try:
            if self._redis_client:
                await self._redis_client.ping()
                return True
            return False
        except Exception as e:
            logger.warning("Redis 健康检查失败", error=str(e))
            return False

    async def get_info(self) -> dict:
        """获取 Redis 服务器信息

        Returns:
            dict: Redis 服务器信息
        """
        try:
            client = await self.get_redis_client()
            info = await client.info()
            return {
                "redis_version": info.get("redis_version"),
                "used_memory": info.get("used_memory_human"),
                "connected_clients": info.get("connected_clients"),
                "uptime_in_seconds": info.get("uptime_in_seconds"),
                "keyspace_hits": info.get("keyspace_hits"),
                "keyspace_misses": info.get("keyspace_misses"),
            }
        except Exception as e:
            logger.error("获取 Redis 信息失败", error=str(e))
            raise HTTPException(status_code=500, detail=f"获取 Redis 信息失败: {e!s}")


# Redis 管理器单例实例
redis_manager = RedisManager()
