"""火山引擎 KMS 客户端封装

提供简单的接口来获取 KMS 密钥值，使用单例客户端避免重复初始化。
"""

from structlog import get_logger
import volcenginesdkkms
from volcenginesdkkms.models import GetSecretValueRequest

from app.client.client_manager import get_volcengine_client

logger = get_logger(__name__)


def get_secret_value(secret_name: str, version_id: str | None = None) -> str:
    """获取密钥值

    Args:
        secret_name: 密钥名称
        version_id: 版本ID，如果为None则获取最新版本

    Returns:
        str: 密钥值
    """
    try:
        # 获取火山引擎客户端单例
        volcengine_client = get_volcengine_client()
        api_client = volcengine_client.get_client()

        # 创建 KMS API 客户端
        kms_api = volcenginesdkkms.KMSApi(api_client)

        # 创建请求
        request = GetSecretValueRequest(secret_name=secret_name, version_id=version_id)

        # 调用 API
        response = kms_api.get_secret_value(request)

        logger.info("Successfully retrieved secret value", secret_name=secret_name, version_id=response.version_id)

        return response.secret_value

    except Exception as e:
        logger.error("Failed to get secret value", secret_name=secret_name, error=str(e))
        raise
