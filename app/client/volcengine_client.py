"""火山引擎客户端封装

使用线程安全的单例模式，避免重复创建客户端实例。
"""

import multiprocessing
import threading
from typing import Optional

from structlog import get_logger
import volcenginesdkcore

from app.core.config import settings

logger = get_logger(__name__)


class VolcengineClient:
    """火山引擎客户端（线程安全单例）"""

    _instance: Optional["VolcengineClient"] = None
    _lock = threading.Lock()

    def __new__(cls) -> "VolcengineClient":
        """线程安全的单例实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """初始化配置（只执行一次）"""
        if not self._initialized:
            self._configuration: volcenginesdkcore.Configuration | None = None
            self._setup_configuration()
            self._initialized = True

    def _setup_configuration(self) -> None:
        """设置火山引擎客户端配置"""
        if not settings.VOLCENGINE_ACCESS_KEY or not settings.VOLCENGINE_SECRET_KEY:
            raise ValueError("VOLCENGINE_ACCESS_KEY and VOLCENGINE_SECRET_KEY must be set")

        try:
            # 创建配置对象
            self._configuration = volcenginesdkcore.Configuration()

            # 基础认证配置
            self._configuration.ak = settings.VOLCENGINE_ACCESS_KEY
            self._configuration.sk = settings.VOLCENGINE_SECRET_KEY
            self._configuration.region = settings.VOLCENGINE_REGION

            # 端点配置
            if settings.VOLCENGINE_ENDPOINT:
                self._configuration.endpoint = settings.VOLCENGINE_ENDPOINT

            # 连接池配置
            self._configuration.max_hosts = settings.VOLCENGINE_MAX_HOSTS

            # 设置连接池大小，默认为 cpu_count() * 5
            max_pool_size = settings.VOLCENGINE_MAX_POOL_SIZE
            if max_pool_size is None:
                max_pool_size = multiprocessing.cpu_count() * 5
            self._configuration.max_pool_size = max_pool_size

            # 超时配置
            self._configuration.connect_timeout = settings.VOLCENGINE_CONNECT_TIMEOUT
            self._configuration.read_timeout = settings.VOLCENGINE_READ_TIMEOUT

            # 重试配置
            self._configuration.retry_count = settings.VOLCENGINE_RETRY_COUNT
            self._configuration.retry_delay = settings.VOLCENGINE_RETRY_DELAY

            # 双栈网络配置
            if settings.VOLCENGINE_ENABLE_DUAL_STACK:
                self._configuration.enable_dual_stack = True

            # 设置为全局默认配置
            volcenginesdkcore.Configuration.set_default(self._configuration)

            logger.info("Volcengine client configured successfully", region=settings.VOLCENGINE_REGION)

        except Exception as e:
            logger.error("Failed to configure Volcengine client", error=str(e))
            raise

    def get_client(self) -> volcenginesdkcore.ApiClient:
        """获取API客户端"""
        return volcenginesdkcore.ApiClient(self._configuration)

    def get_configuration(self) -> volcenginesdkcore.Configuration | None:
        """获取当前配置对象"""
        return self._configuration

    @classmethod
    def get_instance(cls) -> "VolcengineClient":
        """获取单例实例"""
        return cls()


# 便捷的工厂函数
def get_volcengine_client() -> VolcengineClient:
    """获取火山引擎客户端单例"""
    return VolcengineClient.get_instance()
