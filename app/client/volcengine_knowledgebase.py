"""火山引擎知识库服务封装

提供知识库的创建、管理、搜索等功能，使用单例模式避免重复初始化。
"""

import threading
from typing import Any, Optional

from structlog import get_logger
from volcengine.viking_knowledgebase import VikingKnowledgeBaseService
from volcengine.viking_knowledgebase.Point import Point

from app.client.client_manager import get_volcengine_client
from app.core.config import settings
from app.schemas.knowledgebase import CollectionInfo, OperationResponse

logger = get_logger(__name__)


class VikingKnowledgeBaseClient:
    """火山引擎知识库客户端（线程安全单例）"""

    _instance: Optional["VikingKnowledgeBaseClient"] = None
    _lock = threading.Lock()

    def __new__(cls) -> "VikingKnowledgeBaseClient":
        """线程安全的单例实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """初始化客户端（只执行一次）"""
        if not self._initialized:
            self._service: VikingKnowledgeBaseService | None = None
            self._setup_service()
            self._initialized = True

    def _setup_service(self) -> None:
        """设置知识库服务"""
        try:
            # 获取火山引擎客户端配置
            volcengine_client = get_volcengine_client()
            config = volcengine_client.get_configuration()

            if not config:
                raise ValueError("Volcengine client configuration is not available")

            # 创建知识库服务
            self._service = VikingKnowledgeBaseService(
                host=settings.VOLCENGINE_KB_HOST,
                scheme=settings.VOLCENGINE_KB_SCHEME,
                ak=config.ak,
                sk=config.sk,
                connection_timeout=settings.VOLCENGINE_KB_CONNECTION_TIMEOUT,
                socket_timeout=settings.VOLCENGINE_KB_SOCKET_TIMEOUT,
            )

            logger.info(
                "Viking knowledge base service initialized successfully",
                host=settings.VOLCENGINE_KB_HOST,
                scheme=settings.VOLCENGINE_KB_SCHEME,
            )

        except Exception as e:
            logger.error("Failed to initialize Viking knowledge base service", error=str(e))
            raise

    def get_service(self) -> VikingKnowledgeBaseService:
        """获取知识库服务实例"""
        if self._service is None:
            raise RuntimeError("Knowledge base service not initialized")
        return self._service

    def search_collection(
        self,
        collection_name: str,
        query: str,
        limit: int = 10,
        dense_weight: float = 0.7,
        rerank_switch: bool = True,
        doc_filter: dict[str, Any] | None = None,
        **kwargs: Any,
    ) -> list[Point]:
        """搜索知识库

        Args:
            collection_name: 知识库名称
            query: 搜索查询文本
            limit: 返回结果数量 (1-200)
            dense_weight: 向量检索权重 (0.2-1.0)
            rerank_switch: 是否启用重排序
            doc_filter: 文档过滤条件

        Returns:
            List[KnowledgePoint]: 知识点列表
        """
        try:
            service = self.get_service()

            return service.search_collection(
                collection_name=collection_name,
                query=query,
                limit=limit,
                dense_weight=dense_weight,
                rerank_switch=rerank_switch,
            )
        except Exception as e:
            logger.error(
                "Failed to search knowledge base",
                collection_name=collection_name,
                query=query[:100] + "..." if len(query) > 100 else query,
                error=str(e),
            )
            raise

    def list_collections(self) -> list[CollectionInfo]:
        """列出知识库

        Returns:
            List[CollectionInfo]: 知识库列表
        """
        try:
            service = self.get_service()

            # 调用列表接口
            collections = service.list_collections()

            # 转换为内部数据模型
            collection_infos = []
            for collection in collections:
                collection_info = CollectionInfo(
                    collection_name=collection.collection_name,
                    description=getattr(collection, "description", None),
                    created_time=getattr(collection, "created_time", None),
                    updated_time=getattr(collection, "updated_time", None),
                    document_count=getattr(collection, "document_count", None),
                    status=getattr(collection, "status", None),
                )
                collection_infos.append(collection_info)

            logger.info("Successfully listed knowledge bases", count=len(collection_infos))

            return collection_infos

        except Exception as e:
            logger.error("Failed to list knowledge bases", error=str(e))
            raise

    def upload_document(
        self,
        collection_name: str,
        document_name: str,
        document_content: str,
        document_url: str | None = None,
        metadata: dict[str, Any] | None = None,
    ) -> OperationResponse:
        """上传文档到知识库

        Args:
            collection_name: 知识库名称
            document_name: 文档名称
            document_content: 文档内容
            document_url: 文档URL
            metadata: 文档元数据

        Returns:
            OperationResponse: 操作结果
        """
        try:
            service = self.get_service()

            # 调用上传接口
            result = service.upload_document(
                collection_name=collection_name,
                document_name=document_name,
                document_content=document_content,
                document_url=document_url,
                metadata=metadata,
            )

            logger.info(
                "Successfully uploaded document to knowledge base",
                collection_name=collection_name,
                document_name=document_name,
                content_length=len(document_content),
            )

            return OperationResponse(
                success=True,
                message="Document uploaded successfully",
                data={
                    "collection_name": collection_name,
                    "document_name": document_name,
                    "result": result,
                },
            )

        except Exception as e:
            logger.error(
                "Failed to upload document to knowledge base",
                collection_name=collection_name,
                document_name=document_name,
                error=str(e),
            )
            return OperationResponse(
                success=False,
                message=f"Failed to upload document: {e!s}",
                data=None,
            )

    def delete_document(self, collection_name: str, document_id: str) -> OperationResponse:
        """从知识库删除文档

        Args:
            collection_name: 知识库名称
            document_id: 文档ID

        Returns:
            OperationResponse: 操作结果
        """
        try:
            service = self.get_service()

            # 调用删除接口
            result = service.delete_document(
                collection_name=collection_name,
                document_id=document_id,
            )

            logger.info(
                "Successfully deleted document from knowledge base",
                collection_name=collection_name,
                document_id=document_id,
            )

            return OperationResponse(
                success=True,
                message="Document deleted successfully",
                data={
                    "collection_name": collection_name,
                    "document_id": document_id,
                    "result": result,
                },
            )

        except Exception as e:
            logger.error(
                "Failed to delete document from knowledge base",
                collection_name=collection_name,
                document_id=document_id,
                error=str(e),
            )
            return OperationResponse(
                success=False,
                message=f"Failed to delete document: {e!s}",
                data=None,
            )

    @classmethod
    def get_instance(cls) -> "VikingKnowledgeBaseClient":
        """获取单例实例"""
        return cls()


# 便捷的工厂函数和快捷接口
def get_knowledgebase_client() -> VikingKnowledgeBaseClient:
    """获取知识库客户端单例"""
    return VikingKnowledgeBaseClient.get_instance()


def search_knowledgebase(
    collection_name: str,
    query: str,
    limit: int = 20,
    dense_weight: float = 0.7,
    rerank_switch: bool = True,
    doc_filter: dict[str, Any] | None = None,
    **kwargs: Any,
) -> list[Point]:
    """搜索知识库（简化接口）

    Args:
        collection_name: 知识库名称
        query: 搜索查询文本
        limit: 返回结果数量
        dense_weight: 向量检索权重
        rerank_switch: 是否启用重排序
        doc_filter: 文档过滤条件

    Returns:
        List[KnowledgePoint]: 知识点列表
    """
    client = get_knowledgebase_client()
    return client.search_collection(
        collection_name=collection_name,
        query=query,
        limit=limit,
        dense_weight=dense_weight,
        rerank_switch=rerank_switch,
        doc_filter=doc_filter,
        **kwargs,
    )


def create_knowledgebase(
    collection_name: str,
    description: str | None = None,
    config: dict[str, Any] | None = None,
) -> OperationResponse:
    """创建知识库（简化接口）

    Args:
        collection_name: 知识库名称
        description: 知识库描述
        config: 配置参数

    Returns:
        OperationResponse: 操作结果
    """
    client = get_knowledgebase_client()
    return client.create_collection(
        collection_name=collection_name,
        description=description,
        config=config,
    )


def list_knowledgebases() -> list[CollectionInfo]:
    """列出知识库（简化接口）

    Returns:
        List[CollectionInfo]: 知识库列表
    """
    client = get_knowledgebase_client()
    return client.list_collections()
