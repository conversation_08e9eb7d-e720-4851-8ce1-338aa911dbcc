{% extends "base_agent.j2" %}

{% block principles %}
核心原则：
1. 数据准确性：所有疾病名称必须与原文保持100%一致，禁止任何形式的推测或修改
2. 医学专业性：使用规范的医学疾病分类和术语
3. 信息完整性：系统性扫描全文，识别所有已明确诊断或已存在的疾病
4. 分类规范性：严格按照医学疾病分类体系进行归类
{% endblock %}

{% block specific_instructions %}
任务说明：
您需要从体检报告中识别和提取受检者已存在的疾病信息。

重点关注以下信息源：医生诊断结论中明确提及的已确诊疾病

疾病信息提取要求：
- 只提取已明确诊断或确认存在的疾病
- 不包括"疑似"、"可能"、"建议排查"等未确诊情况
- 每个疾病需要准确分类和命名

{% endblock %}

{% block requirements %}
输出要求：

1. 数据提取规范：
   - 直接引用原文中的疾病名称，不得改写或简化
   - 使用标准医学术语（如"2型糖尿病"而非"糖尿病"，如果原文有明确类型）
   - 避免重复列出同一疾病的不同表述

2. 结构化要求：
   - 严格按照 JSON Schema 模型定义输出
   - 如果没有发现任何诊断出的疾病，返回空列表 []

3. 医学准确性：
   - 疾病分类必须准确，遵循医学分类标准
   - 疾病名称使用规范医学术语
   - 确保疾病名称与分类匹配

4. 完整性检查：
   - 仔细扫描报告的所有相关部分
   - 特别关注：诊断结论
   - 交叉验证不同部分提及的疾病信息

8. 已存在的疾病列表 (existing_diseases)
    - 识别报告中提及的所有已存在的疾病
    - 每个疾病需包含：
     * category: 疾病分类，必须在<疾病范围>中
     * name: 疾病名称，如高血压、糖尿病等, 必须在<疾病范围>中
     * en_category: 疾病英文分类，如 Cardiovascular Disease、Endocrine Disease等, 必须在<疾病范围>中
     * en_name: 疾病英文名称，如 Hypertension、Diabetes等, 必须在<疾病范围>中


注意事项：
- 只提取已明确存在的疾病，不包括风险评估或疑似诊断
- 如果同一疾病在报告中有多种表述，选择最准确和完整的医学术语
- 对于复杂疾病名称，保持原文准确性
- 如果疾病分类不明确，归入"其他"类别

## 数据范围引用

{% if disease_range %}
    你需要提取出所有的检查项和所有的报告单等，需要限定在范围：
<范围>
{{disease_range}}
</范围>
{% else %}
    遵循标准医学疾病分类和名称, 标准的中文和英文术语
{% endif %}

{% endblock %} 