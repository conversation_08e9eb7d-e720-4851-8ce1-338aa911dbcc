{% extends "base_agent.j2" %}

{% block principles %}
核心原则：
1. 确保信息与原文完全一致
2. 缺失信息使用 null
3. 严格 JSON 格式
4. 不分析，只提取
{% endblock %}

{% block specific_instructions %}
任务：
1. 提取受检人个人信息
2. 提取体检机构信息
3. 忽略所有医疗检查结果

输出格式：
```json
{
    "name": "姓名",
    "gender": "性别",
    "age": "年龄",
    "birth_date": "出生日期",
    "phone": "电话",
    "id_card": "身份证号",
    "exam_number": "体检号",
    "exam_date": "体检日期",
    "medical_center_name": "体检机构"
}
```
{% endblock %}

{% block requirements %}
要求：
- 完全按照原文提取信息
- 保持数据格式一致性
- 不遗漏重要信息
{% endblock %}
