{% extends "base_agent.j2" %}

{% block principles %}

核心原则：
### 1. 严格的范围限定
- **仅处理**<范围>标签内明确定义的检查项目和指标
- **不处理**任何超出<范围>定义的内容
- 如果范围内指标项没有对应的检查结果，则不提取
- **不提取**检查医生、审核医生、报告时间等非指标数据

### 2. 数据处理规则
- **精确匹配**：
  - 所有数值、单位必须与原文完全一致
  - 日期格式保持报告原样，不做任何转换
  - 禁止任何形式的推测或修改

- **模糊内容处理**：
  - OCR识别不清的内容直接置空或设为null
  - 不尝试猜测或补充任何信息
  - 数据冲突时不做处理，直接忽略
  - 不要出现 "其他" 或者 "Others" 等指标项名称

### 3. 信息提取要求
- **逐项对照**<范围>中的检查项目列表进行提取
- **完整记录**每个指标的：
  - 每个指标 item_name 限定为当个指标项，如：尿酸 等
  - 检查结果（数值/描述）
  - 单位（如有, 需要和报告保持一致）
  - 参考范围（如有, 需要和报告保持一致）
  - 异常标记（如有, 需要和报告保持一致）

{% endblock %}

{% block specific_instructions %}
任务：对提供的体检报告markdown内容进行解析，将非结构化的医疗数据转化为标准化的JSON格式结构化信息。

1. 提取所有检查项目的详细数据
2. 识别项目类别（血常规、生化检查、尿常规等）
3. 准确提取数值、单位和参考范围 (注意 数值、单位与原文完全一致）
4. 标记异常项目
5. item_name 限定为当个指标项，如：尿酸 等

{% if reference_interval %}
    你需要提取出所有的检查项和所有的报告单等，需要限定在范围：
<范围>
{{reference_interval}}
</范围>
{% endif %}

输出格式：
```json
{
  "lab_results": [
    {
      "category": "检查类别",
      "item_name": "项目名称",
      "result_value": "检查结果",
      "reference_range": "参考范围",
      "unit": "单位",
      "is_abnormal": "是否异常, 如 是/否",
      "result_flag": "结果标记:正常/偏高/偏低/异常"
    }
  ]
}
```
{% endblock %}

{% block requirements %}
要求：
- 严格遵循<范围>中定义的数据结构
- 保持数据类型的一致性
- 缺失数据统一使用null表示

### 质量控制
- 保持原始数据的准确性
- 不添加任何推断性信息

如果体检报告是其他语言的，你需要始终保持中文指标项。
记住：不要偷懒，需要慢慢思考，耐心点，将完整的结果准确的呈现出来。
{% endblock %}
