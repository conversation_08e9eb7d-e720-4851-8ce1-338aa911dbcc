
## 🚀 快速开始

### 环境要求

- Python 3.12.4+

```bash
# 安装 playwright
python -m playwright install chromium

```

### 本地开发

#### 方式一：直接运行

1. **安装依赖**

**生产环境：**
```bash
# 仅安装运行应用所需的核心依赖
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

**开发环境：**
```bash
# 安装所有依赖（包括开发工具、测试框架等）
pip install -r requirements-dev.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```


2. **配置环境变量**
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
export DATABASE_URL="mysql+aiomysql://haneda:haneda123@localhost:3306/haneda_ai"
export REDIS_URL="redis://localhost:6379/0"
export JWT_SECRET_KEY="your-super-secret-jwt-key"
export DEBUG=true
```

4. **启动开发服务器**
```bash
# 使用 FastAPI 开发模式启动
fastapi dev app/main.py

# 或使用 uvicorn
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 或使用快速启动脚本
./scripts/start_dev.sh
```


### 访问服务

- **API 文档**: http://localhost:8000/docs
- **ReDoc 文档**: http://localhost:8000/redoc
- **健康检查**: http://localhost:8000/health
- **API 接口**: http://localhost:8000/api/v1/

## 📁 项目结构

```
ai-assistant/
├── app/                          # 应用主目录
│   ├── __init__.py
│   ├── main.py                   # FastAPI 应用入口
│   ├── api/                     # API 路由模块
│   ├── core/                    # 核心模块
│   │   ├── __init__.py
│   │   ├── config.py            # 配置管理
│   │   ├── deps.py              # 依赖注入
│   │   └── security.py          # 安全相关
│   ├── db/                      # 数据库模块
│   │   ├── __init__.py
│   │   └── session.py           # 数据库会话管理
│   ├── exceptions/              # 异常处理模块
│   │   ├── __init__.py
│   │   ├── base.py              # 基础异常类
│   │   └── handlers.py          # 异常处理器
│   ├── middleware/              # 中间件模块
│   │   ├── __init__.py
│   │   ├── logging.py           # 日志中间件
│   │   └── traceid.py           # 追踪ID中间件
│   ├── models/                  # 数据模型
│   │   ├── __init__.py
│   │   ├── task.py              # 任务模型
│   │   └── user.py              # 用户模型
│   ├── schemas/                 # Pydantic 模式
│   │   ├── __init__.py
│   │   ├── request.py           # 请求模式
│   │   ├── response.py          # 响应模式
│   │   └── task.py              # 任务模式
│   ├── services/                # 业务服务层
│   │   └── __init__.py
│   ├── tasks/                   # 任务管理模块
│   │   ├── __init__.py
│   └── utils/                   # 工具模块
│       ├── __init__.py
│       └── logger.py            # 日志工具
├── docs/                        # 文档目录
│   ├── prd.md                   # 产品需求文档
│   └── technology_stack.md      # 技术栈说明
├── scripts/                     # 脚本目录
│   ├── run_tests.sh             # 测试脚本
│   └── start_dev.sh             # 开发启动脚本
├── tests/                       # 测试目录
├── requirements.txt             # Python 依赖包
├── pytest.ini                  # Pytest 配置
└── README.md                    # 项目说明文档
```

## 🔧 开发指南

### 使用 Makefile 快速开发

项目提供了 Makefile 来简化常用的开发命令：

```bash
# 查看所有可用命令
make help

# 安装生产环境依赖
make install

# 安装开发环境依赖（推荐用于开发）
make install-dev

# 启动开发服务器
make dev

# 运行所有测试
make test

# 运行单元测试
make test-unit

# 运行集成测试
make test-integration

# 代码检查
make lint

# 格式化代码
make format

# 类型检查
make type-check

# 运行所有质量检查（代码检查 + 类型检查）
make quality

# 清理临时文件
make clean
```

### 测试命令

```bash
# 运行所有测试
pytest

# 运行单元测试
pytest tests/unit/ -v

# 运行集成测试
pytest tests/integration/ -v

# 运行特定测试文件
pytest tests/unit/test_specific.py

# 运行特定测试函数
pytest tests/unit/test_specific.py::test_function_name

# 运行测试并生成覆盖率报告
pytest --cov=app --cov-report=html
```

### API 响应格式

所有 API 接口遵循统一的响应格式：

**成功响应：**
```json
{
    "code": 0,
    "message": "success",
    "data": {}
}
```

**错误响应：**
```json
{
    "code": 10000,
    "message": "error message",
    "data": null
}
```
