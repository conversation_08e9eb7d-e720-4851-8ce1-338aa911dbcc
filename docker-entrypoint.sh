#!/bin/bash
# ============================================================
# AI Assistant Docker 容器启动脚本
# ============================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    if [[ "${DEBUG}" == "true" ]]; then
        echo -e "${BLUE}[DEBUG]${NC} $1"
    fi
}

# 检查环境变量
check_env_vars() {
    log_info "检查环境变量..."

    # 必需的环境变量
    required_vars=(
        "ARK_API_KEY"
        "VOLCENGINE_ACCESS_KEY"
        "VOLCENGINE_SECRET_KEY"
        "TOS_BUCKET_NAME"
    )

    missing_vars=()

    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            missing_vars+=("$var")
        fi
    done

    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        log_error "缺少必需的环境变量："
        for var in "${missing_vars[@]}"; do
            log_error "  - $var"
        done
        log_error "请检查 .env.docker 文件配置"
        exit 1
    fi

    log_info "环境变量检查通过"
}

# 等待依赖服务
wait_for_service() {
    local host=$1
    local port=$2
    local service_name=$3
    local timeout=${4:-30}

    log_info "等待 ${service_name} 服务启动 (${host}:${port})..."

    for ((i=1; i<=timeout; i++)); do
        if timeout 1 bash -c "echo >/dev/tcp/${host}/${port}" 2>/dev/null; then
            log_info "${service_name} 服务已就绪"
            return 0
        fi

        if [[ $i -eq $timeout ]]; then
            log_error "等待 ${service_name} 服务超时 (${timeout}秒)"
            return 1
        fi

        log_debug "等待 ${service_name}... (${i}/${timeout})"
        sleep 1
    done
}

# 数据库初始化
init_database() {
    log_info "初始化数据库..."

    # 这里可以添加数据库迁移逻辑
    # python -m app.migrations.init_db

    log_info "数据库初始化完成"
}

# 创建必要目录
create_directories() {
    log_info "创建必要的目录..."

    mkdir -p /app/logs
    mkdir -p /app/data
    mkdir -p /tmp/ai-assistant

    log_info "目录创建完成"
}

# 启动 FastAPI 应用
start_fastapi() {
    log_info "启动 FastAPI 应用..."

    # 等待依赖服务
    wait_for_service "${REDIS_HOST:-redis}" "${REDIS_PORT:-6379}" "Redis" 60
    wait_for_service "${MYSQL_HOST:-mysql}" "${MYSQL_PORT:-3306}" "MySQL" 120

    # 初始化数据库
    init_database

    # 启动 FastAPI 应用
    exec python -m uvicorn app.main:app \
        --host 0.0.0.0 \
        --port 8000 \
        --workers 1 \
        --access-log \
        --use-colors
}

# 启动 Streamlit 应用
start_streamlit() {
    log_info "启动 Streamlit 应用..."

    # 等待 FastAPI 服务
    wait_for_service "fastapi" "8000" "FastAPI" 120

    # 启动 Streamlit 应用
    exec streamlit run app/streamlit_pages/main.py \
        --server.port 8501 \
        --server.address 0.0.0.0 \
        --server.headless true \
        --browser.serverAddress 0.0.0.0 \
        --browser.gatherUsageStats false \
        --server.enableCORS false \
        --server.enableXsrfProtection false
}

# 主函数
main() {
    log_info "AI Assistant 容器启动中..."

    # 设置时区
    if [[ -n "${TZ}" ]]; then
        log_info "设置时区为: ${TZ}"
        ln -snf /usr/share/zoneinfo/${TZ} /etc/localtime
        echo ${TZ} > /etc/timezone
    fi

    # 检查环境变量
    check_env_vars

    # 创建目录
    create_directories

    # 根据参数启动不同服务
    case "${1:-app}" in
        "fastapi")
            start_fastapi
            ;;
        "streamlit")
            start_streamlit
            ;;
        "app")
            # 默认启动 FastAPI
            start_fastapi
            ;;
        *)
            log_error "未知的启动参数: $1"
            log_info "可用参数: fastapi, streamlit, app"
            exit 1
            ;;
    esac
}

# 信号处理
handle_signal() {
    log_info "接收到终止信号，正在停止服务..."
    exit 0
}

trap handle_signal SIGTERM SIGINT

# 执行主函数
main "$@"
