[project]
name = "ai-assistant"
version = "1.0.0"
description = "HDL AI Assistant with FastAPI"
readme = "README.md"
requires-python = ">=3.11"  # 要求的最低 Python 版本
authors = [
    {name = "AI Assistant Team", email = "<EMAIL>"},
]
# 生产环境运行时的核心依赖
# 这些依赖会被自动安装，是应用运行的必要组件
dependencies = [
    # Web 框架和服务器
    "fastapi>=0.104.0",             # Web 框架
    "uvicorn[standard]>=0.24.0",    # ASGI 服务器

    # 数据验证和配置
    "pydantic>=2.5.0",              # 数据验证和解析
    "pydantic-settings>=2.1.0",     # 配置管理

    # 数据库相关
    "sqlalchemy>=2.0.0",            # ORM 框架

    # 缓存和会话
    "redis>=5.0.0",                 # Redis 客户端

    # 日志和监控
    "structlog>=23.2.0",            # 结构化日志

    # 安全和认证
    "python-multipart>=0.0.6",      # 多部分表单支持
    "python-jose[cryptography]>=3.3.0",  # JWT 支持
    "passlib[bcrypt]>=1.7.4",       # 密码哈希

    # 爬虫和AI相关
    "crawl4ai>=0.2.0",          # 爬虫框架
    "agno>=1.7.2",              # 多智能体框架
    "volcengine==1.0.193",      # 火山引擎SDK
    "tos==2.8.4",               # 火山引擎TOS对象存储SDK
    "wikipedia==1.4.0",         # Wikipedia API

    # HTTP 客户端和工具
    "httpx>=0.27.0",            # 现代HTTP客户端
    "aiofiles>=23.2.0",         # 异步文件操作
    "python-dotenv>=1.0.0",     # 环境变量管理
]

# =============================================================================
# 可选依赖配置 - Optional Dependencies
# =============================================================================
# 定义开发环境所需的额外依赖，通过 pip install -e .[dev] 安装
# 这些依赖不会影响生产环境，但对开发体验很重要

[project.optional-dependencies]
dev = [
    # 代码质量工具 - Code Quality Tools
    "ruff>=0.4.0",          # 快速的 Python 代码检查器和格式化器
    "black>=24.0.0",        # 代码格式化器，确保代码风格一致
    "mypy>=1.10.0",         # 静态类型检查器
    "pre-commit>=3.7.0",    # Git 钩子管理，自动运行代码检查
    "isort>=0.6.1",         # isort 排序

    # 测试工具 - Testing Tools
    "pytest>=8.0.0",        # Python 测试框架
    "pytest-asyncio>=0.24.0",  # 异步测试支持
    "pytest-cov>=5.0.0",    # 代码覆盖率报告
    "pytest-mock>=3.14.0",  # 测试模拟工具
    "pytest-xdist>=3.5.0",  # 并行测试执行
    "factory-boy>=3.3.0",   # 测试数据工厂

    # 类型存根 - Type Stubs
    # 为第三方库提供类型提示，提高 IDE 支持
    "types-redis>=4.6.0",
    "types-requests>=2.31.0",
    "types-passlib>=1.7.0",
    "sqlalchemy-stubs>=0.4",

    # 开发工具 - Development Tools
    "ipython>=8.0.0",       # 增强的交互式 Python 解释器
    "jupyter>=1.0.0",       # Jupyter 笔记本支持
    "rich>=13.0.0",         # 美化终端输出
]

# =============================================================================
# 项目链接和入口点 - Project URLs and Entry Points
# =============================================================================

[project.urls]

# 命令行入口点配置
[project.scripts]
ai-assistant = "app.main:main"  # 定义 ai-assistant 命令

# =============================================================================
# 代码质量工具配置 - Code Quality Tools Configuration
# =============================================================================

# Black 代码格式化器配置
[tool.black]
line-length = 120  # 每行最大字符数
target-version = ['py312']  # 目标Python版本
include = '\.pyi?$'  # 包含的文件类型
exclude = '''
/(
    \.eggs          # 构建产物
  | \.git           # Git 目录
  | \.hg            # Mercurial 目录
  | \.mypy_cache    # MyPy 缓存
  | \.tox           # Tox 测试环境
  | \.venv          # 虚拟环境
  | _build          # 构建目录
  | buck-out        # Buck 构建输出
  | build           # 构建目录
  | dist            # 分发目录
  | migrations      # 数据库迁移文件
  | tests           # 测试目录
  | streamlit_pages # streamlit
  | .*\.ipynb       # Jupyter notebook 文件
)/
'''

# Ruff 代码检查和格式化工具配置
[tool.ruff]
line-length = 120  # 每行最大字符数
target-version = "py312"  # 目标Python版本
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
    "migrations",  # 数据库迁移文件通常不需要严格检查
    "tests",       # 测试目录
    "streamlit_pages", # streamlit
    "*.ipynb",  # Jupyter notebook 文件
]

# Ruff 检查规则配置
[tool.ruff.lint]
select = [
    "F",    # Pyflakes - 检查常见错误
    "E",    # pycodestyle errors - 代码风格错误
    "W",    # pycodestyle warnings - 代码风格警告
    "I",    # isort - 导入排序
    "UP",   # pyupgrade - Python 版本升级建议
    "B",    # flake8-bugbear - 常见错误模式
    "RUF",  # Ruff-specific rules - Ruff 特定规则
]
ignore = [
    "E501",  # Line too long (let black handle this) - 行长度由 black 处理
    "E731",  # Do not assign a lambda expression, use a def - 允许 lambda 表达式
    "B008",  # Do not perform function calls in argument defaults - 允许默认参数函数调用
    "B904",  # Within an `except` clause, raise exceptions with `raise ... from err` - 异常链规则
    "RUF012", # Mutable class attributes should be annotated with `typing.ClassVar` - 可变类属性规则
    "RUF002",
    "RUF003",
    "RUF001",
    "W293",
]

# 针对特定文件的规则配置
[tool.ruff.lint.per-file-ignores]
"tests/**/*.py" = [
    "B011",    # Do not call assert False - 测试文件中允许 assert False
]
"**/migrations/*.py" = [
    "E501",    # Line too long - 迁移文件允许长行
]

# Import 排序配置
[tool.ruff.lint.isort]
force-sort-within-sections = true  # 在同一部分内强制排序
force-single-line = false  # 允许多行导入
known-first-party = ["app"]  # 第一方包
known-third-party = [
    "fastapi",
    "pydantic",
    "sqlalchemy",
    "redis",
    "structlog",
    "pytest",
    "httpx",
    "crawl4ai",
    "autogen",
    "volcengine",
]

# 复杂度检查配置
[tool.ruff.lint.mccabe]
max-complexity = 20  # 最大圈复杂度

# Pylint 风格检查配置
[tool.ruff.lint.pylint]
max-args = 10  # 函数最大参数数量
max-returns = 8  # 函数最大返回语句数量
max-branches = 20  # 最大分支数量
max-statements = 80  # 最大语句数量

# =============================================================================
# MyPy 静态类型检查配置 - MyPy Configuration
# =============================================================================
# MyPy 是 Python 的静态类型检查器，帮助发现类型相关的错误

[tool.mypy]
python_version = "3.12"  # Python 版本
warn_return_any = false  # 返回 Any 类型时不警告
warn_unused_configs = true  # 未使用的配置项警告
disallow_untyped_defs = false  # 允许未类型化的函数定义
disallow_incomplete_defs = false  # 允许不完整的函数定义
check_untyped_defs = false  # 不检查未类型化的函数
disallow_untyped_decorators = false  # 允许未类型化的装饰器
no_implicit_optional = false  # 允许隐式可选类型
warn_redundant_casts = false  # 多余的类型转换警告
warn_unused_ignores = false  # 未使用的忽略注释警告
warn_no_return = false  # 函数没有返回值警告
warn_unreachable = false  # 不可达代码警告
strict_equality = false  # 严格相等比较
show_error_codes = true  # 显示错误代码
show_column_numbers = true  # 显示列号
show_error_context = true  # 显示错误上下文
exclude = [
    "^build/",
    "^dist/",
    "^venv/",
    "^.venv/",
    "^migrations/",
    "^tests/",     # 测试目录
    "^streamlit_pages/",
    ".*\\.ipynb$",  # Jupyter notebook 文件
]

# 第三方库类型忽略配置
# 对于没有类型提示的第三方库，忽略导入错误
[[tool.mypy.overrides]]
module = [
    "crawl4ai.*",
    "agno.*",
    "volcengine.*",
    "redis.*",
    "structlog.*",
    "uvicorn.*",
    "fastapi.*",
    "pydantic.*",
    "sqlalchemy.*",
    "pytest.*",
    "httpx.*",
]
ignore_missing_imports = true

# =============================================================================
# 测试配置 - Testing Configuration
# =============================================================================

[tool.pytest.ini_options]
minversion = "8.0"  # 最小 pytest 版本
testpaths = ["tests"]  # 测试文件目录
python_files = ["test_*.py", "*_test.py"]  # 测试文件模式
python_classes = ["Test*"]  # 测试类模式
python_functions = ["test_*"]  # 测试函数模式
addopts = [
    "-v",  # 详细输出
    "--strict-markers",  # 严格标记模式
    "--strict-config",  # 严格配置模式
    "--tb=short",  # 简短的回溯信息
    "--cov=app",  # 代码覆盖率目标
    "--cov-report=term-missing",  # 终端缺失行报告
    "--cov-report=html:htmlcov",  # HTML 覆盖率报告
    "--cov-report=xml:coverage.xml",  # XML 覆盖率报告
    "--cov-branch",  # 分支覆盖率
    "--cov-fail-under=60",  # 覆盖率低于60%时失败
    "--durations=10",  # 显示最慢的10个测试
    "--maxfail=10",  # 最多失败10个测试后停止
]
# 测试标记定义
markers = [
    "unit: marks tests as unit tests (deselect with '-m \"not unit\"')",  # 单元测试
    "integration: marks tests as integration tests (deselect with '-m \"not integration\"')",  # 集成测试
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",  # 慢速测试
    "smoke: marks tests as smoke tests for basic functionality",  # 冒烟测试
    "regression: marks tests as regression tests",  # 回归测试
    "parametrize: marks tests as parametrized tests",  # 参数化测试
]
asyncio_mode = "auto"  # 自动异步模式
# 警告过滤器
filterwarnings = [
    "error",  # 将警告视为错误
    "ignore::DeprecationWarning",  # 忽略弃用警告
    "ignore::PendingDeprecationWarning",  # 忽略待弃用警告
    "ignore::UserWarning:.*",  # 忽略用户警告
]

# =============================================================================
# 代码覆盖率配置 - Code Coverage Configuration
# =============================================================================
# Coverage.py 用于测量代码覆盖率

# 运行时配置
[tool.coverage.run]
source = ["app"]  # 覆盖率检查的源代码目录
omit = [
    "app/main.py",  # 排除主入口文件
    "app/__init__.py",  # 排除初始化文件
    "*/tests/*",  # 排除测试文件
    "*/streamlit_pages/*", # 排除 streamlit
    "*/venv/*",  # 排除虚拟环境
    "*/.venv/*",  # 排除虚拟环境
    "*/migrations/*",  # 排除数据库迁移
    "*/conftest.py",  # 排除测试配置
    "*.ipynb",  # 排除 Jupyter notebook 文件
]
branch = true  # 分支覆盖率
parallel = true  # 并行执行支持

# 报告配置
[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",  # 标记不覆盖的代码
    "def __repr__",  # 排除 __repr__ 方法
    "if self.debug:",  # 排除调试代码
    "if settings.DEBUG",  # 排除调试配置
    "raise AssertionError",  # 排除断言错误
    "raise NotImplementedError",  # 排除未实现错误
    "if 0:",  # 排除永远不会执行的代码
    "if __name__ == .__main__.:",  # 排除主程序入口
    "class .*\\bProtocol\\):",  # 排除协议类
    "@(abc\\.)?abstractmethod",  # 排除抽象方法
    "except ImportError:",  # 排除导入错误处理
    "except ModuleNotFoundError:",  # 排除模块未找到错误
    "if TYPE_CHECKING:",  # 排除类型检查代码
]
show_missing = true  # 显示缺失的行
skip_covered = false  # 不跳过已覆盖的文件
sort = "-cover"  # 按覆盖率排序
precision = 2  # 精确度到小数点后2位

# HTML 报告配置
[tool.coverage.html]
directory = "htmlcov"  # HTML 报告目录

# XML 报告配置
[tool.coverage.xml]
output = "coverage.xml"  # XML 报告文件

# Assert 使用配置
[tool.bandit.assert_used]
skips = ["**/test_*.py", "**/*_test.py"]  # 测试文件中允许使用 assert

# =============================================================================
# 开发工具配置 - Development Tools Configuration
# =============================================================================

# isort 导入排序工具配置
# isort 用于自动排序和格式化 Python 导入语句
[tool.isort]
profile = "black"  # 与 black 兼容的配置
multi_line_output = 3  # 多行输出模式
include_trailing_comma = true  # 包含尾随逗号
force_grid_wrap = 0  # 网格换行配置
use_parentheses = true  # 使用括号
ensure_newline_before_comments = true  # 注释前确保换行
line_length = 120  # 行长度
skip_glob = ["*.ipynb"]  # 跳过 Jupyter notebook 文件
known_first_party = ["app"]  # 第一方包
known_third_party = [
    "fastapi",
    "pydantic",
    "sqlalchemy",
    "redis",
    "structlog",
    "pytest",
    "httpx",
    "crawl4ai",
    "autogen",
    "volcengine",
]

# Vulture 死代码检测工具配置
# Vulture 用于查找未使用的代码
[tool.vulture]
exclude = ["venv/", ".venv/", "tests/", "migrations/", "*.ipynb", "streamlit_pages/"]  # 排除目录和文件
ignore_decorators = ["@app.route", "@router.get", "@router.post"]  # 忽略的装饰器
ignore_names = ["args", "kwargs", "request", "response"]  # 忽略的名称
make_whitelist = true  # 生成白名单
min_confidence = 80  # 最小置信度
paths = ["app"]  # 检查路径
sort_by_size = true  # 按大小排序
verbose = false  # 详细输出
