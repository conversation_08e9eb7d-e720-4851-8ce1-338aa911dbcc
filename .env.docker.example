# 应用配置
APP_NAME=ai-assistant
APP_VERSION=1.0.0
DEBUG=true


# Redis 配置 (Docker 内部仍使用 6379)
REDIS_URL=redis://redis:6379/0
REDIS_HOST=redis
REDIS_PORT=6379

# MySQL 配置 (Docker 内部仍使用 3306)
MYSQL_HOST=mysql
MYSQL_PORT=3306
MYSQL_USER=ai_user
MYSQL_PASSWORD=ai_password
MYSQL_DATABASE=ai_assistant
MYSQL_CHARSET=utf8mb4

# 日志配置
LOG_LEVEL=INFO

# OPENAI
OPENAI_BASE_URL="https://openrouter.ai/api/v1/"
OPENAI_API_KEY=sk-oxxx

# 火山引擎
VOLCENGINE_ACCESS_KEY=xx
VOLCENGINE_SECRET_KEY=xx
VOLCENGINE_REGION=cn-beijing

ARK_API_KEY=xxx

# Agent
AGNO_TELEMETRY=false
AGNO_MONITOR=true

# STT API 配置
VOLCENGINE_STT_APP_ID=5181068140
VOLCENGINE_STT_ACCESS_TOKEN=xxx

# TOS
TOS_BUCKET_NAME=ai-resouce
