# ============================================================
# 开发环境依赖 - Development Dependencies
# ============================================================
# 包含开发、测试、代码质量检查所需的工具
# 生产环境请使用 requirements.txt

# 首先安装生产环境依赖
-r requirements.txt

# ------------------------------------------------------------
# 代码质量和格式化工具
# ------------------------------------------------------------
# 代码格式化工具
black>=24.0.0
# 代码检查和自动修复
ruff>=0.4.0
# 类型检查
mypy>=1.10.0
# Git 预提交钩子
pre-commit>=3.7.0
# isort 代码排序
isort>=6.0.1

# ------------------------------------------------------------
# 测试框架和工具
# ------------------------------------------------------------
# 测试框架
pytest>=8.0.0
# 异步测试支持
pytest-asyncio>=0.24.0
# 测试覆盖率
pytest-cov>=5.0.0
# 测试模拟工具
pytest-mock>=3.14.0
# 并行测试
pytest-xdist>=3.5.0
# 测试数据生成
factory-boy>=3.3.0

# ------------------------------------------------------------
# 安全检查工具
# ------------------------------------------------------------
# 安全问题扫描
bandit>=1.7.0
# 依赖安全检查
safety>=3.0.0

# ------------------------------------------------------------
# 类型存根
# ------------------------------------------------------------
# Redis 类型存根
types-redis>=4.6.0
# Requests 类型存根
types-requests>=2.31.0
# Passlib 类型存根
types-passlib>=1.7.0
# SQLAlchemy 类型存根
sqlalchemy-stubs>=0.4

# ------------------------------------------------------------
# 开发工具和增强
# ------------------------------------------------------------
# 增强的Python交互式shell
ipython>=8.0.0
# Jupyter笔记本支持
jupyter>=1.0.0
# 美化终端输出
rich>=13.0.0

# ------------------------------------------------------------
# 性能和调试工具
# ------------------------------------------------------------
# 内存分析工具
memory-profiler
# 性能分析工具
line-profiler
# HTTP请求调试
httpx[cli]

# ------------------------------------------------------------
# 文档生成工具（可选）
# ------------------------------------------------------------
# API文档生成
mkdocs
mkdocs-material
# 代码文档生成
sphinx
