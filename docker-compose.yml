# ============================================================
# AI Assistant Docker Compose Configuration
# ============================================================

services:
  # ========================================
  # FastAPI 应用服务
  # ========================================
  fastapi:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    image: ai-assistant:latest
    container_name: ai-assistant-fastapi
    restart: unless-stopped
    environment:
      - APP_NAME=ai-assistant
      - DEBUG=false
    env_file:
      - .env.docker
    ports:
      - "8000:8000"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - app-temp:/tmp/ai-assistant
    networks:
      - ai-assistant-network
    depends_on:
      redis:
        condition: service_healthy
      mysql:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '0.5'
          memory: 1G
    command: ["fastapi"]

  # ========================================
  # Streamlit UI 服务
  # ========================================
  streamlit:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    image: ai-assistant:latest
    container_name: ai-assistant-streamlit
    restart: unless-stopped
    environment:
      - APP_NAME=ai-assistant-ui
      - DEBUG=false
      - FASTAPI_BASE_URL=http://fastapi:8000
    env_file:
      - .env.docker
    ports:
      - "8501:8501"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - app-temp:/tmp/ai-assistant
    networks:
      - ai-assistant-network
    depends_on:
      fastapi:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/_stcore/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.25'
          memory: 512M
    command: ["streamlit"]

  # ========================================
  # Redis 缓存服务
  # ========================================
  redis:
    image: redis:7.2-alpine
    container_name: ai-assistant-redis
    restart: unless-stopped
    ports:
      - "6380:6379"
    volumes:
      - redis-data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - ai-assistant-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 128M
    command: redis-server /usr/local/etc/redis/redis.conf

  # ========================================
  # MySQL 数据库服务
  # ========================================
  mysql:
    image: mysql:8.0
    container_name: ai-assistant-mysql
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_PASSWORD:-limbo123456}
      - MYSQL_DATABASE=${MYSQL_DATABASE:-ai_assistant}
      - MYSQL_USER=ai_user
      - MYSQL_PASSWORD=ai_password
    env_file:
      - .env.docker
    ports:
      - "3307:3306"
    volumes:
      - mysql-data:/var/lib/mysql
    networks:
      - ai-assistant-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-plimbo123456"]
      interval: 15s
      timeout: 10s
      retries: 10
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.25'
          memory: 512M

  # ========================================
  # Nginx 反向代理服务（可选）
  # ========================================
  nginx:
    image: nginx:1.25-alpine
    container_name: ai-assistant-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/ssl:/etc/nginx/ssl:ro
      - nginx-logs:/var/log/nginx
    networks:
      - ai-assistant-network
    depends_on:
      - fastapi
      - streamlit
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 64M
    profiles:
      - nginx

# ============================================================
# 网络配置
# ============================================================
networks:
  ai-assistant-network:
    driver: bridge

# ============================================================
# 数据卷配置
# ============================================================
volumes:
  # 数据库数据持久化
  mysql-data:
    driver: local

  # Redis 数据持久化
  redis-data:
    driver: local

  # 应用临时文件
  app-temp:
    driver: local

  # Nginx 日志
  nginx-logs:
    driver: local
