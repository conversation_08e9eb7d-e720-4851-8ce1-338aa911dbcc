# ============================================================
# Docker 构建忽略文件
# ============================================================

# Git 相关
.git/
.gitignore
.gitmodules

# Python 相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE 相关
.vscode/
.idea/
*.swp
*.swo
*~

# macOS 相关
.DS_Store
.AppleDouble
.LSOverride

# Windows 相关
Thumbs.db
ehthumbs.db
Desktop.ini

# 日志文件
*.log
logs/

# 临时文件和缓存
tmp/
temp/
.cache/
.pytest_cache/
.coverage
htmlcov/

# 数据和下载文件
data/
download/
crawled_pages/
test_crawled_pages/
medical_data/
backup/

# Jupyter Notebook
.ipynb_checkpoints
*.ipynb

# 测试相关
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/

# 文档构建
docs/_build/

# PyBuilder
target/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath
*.sage.py

# 环境变量文件
.env
.env.local
.env.docker
.env.production

# Spyder
.spyderproject
.spyproject

# Rope
.ropeproject

# mkdocs
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre
.pyre/

# 部署相关
docker-compose.override.yml
Dockerfile.dev
*.tar.gz
*.zip

# 配置文件（生产环境敏感信息）
config/ssl/
*.pem
*.key
*.crt

# 其他
.pytest_cache
node_modules/
*.orig
