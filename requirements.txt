# ============================================================
# 生产环境依赖 - Production Dependencies
# ============================================================
# 仅包含运行应用程序所必需的核心依赖
# 开发工具和测试框架请使用 requirements-dev.txt

# ------------------------------------------------------------
# Web 框架相关依赖
# ------------------------------------------------------------
# FastAPI Web框架及其标准扩展
fastapi[standard]==0.115.12
# ASGI服务器
uvicorn[standard]>=0.24.0
# Pydantic 数据验证
pydantic>=2.5.0
pydantic-settings>=2.1.0

# ------------------------------------------------------------
# 数据库相关依赖
# ------------------------------------------------------------
# ORM框架
sqlalchemy>=2.0.0
# MySQL异步驱动
aiomysql
# MySQL Python驱动（兼容性）
pymysql

# ------------------------------------------------------------
# 缓存相关依赖
# ------------------------------------------------------------
# Redis客户端
redis>=5.0.0
# 内存缓存工具
cachetools>=5.3.0

# ------------------------------------------------------------
# 日志相关依赖
# ------------------------------------------------------------
# 结构化日志
structlog>=23.2.0
# JSON格式日志
python-json-logger

# ------------------------------------------------------------
# HTTP 客户端和工具库
# ------------------------------------------------------------
# 异步HTTP客户端
httpx>=0.27.0
# 支持SOCKS代理的HTTP客户端
httpx[socks]
# 异步文件操作
aiofiles>=23.2.0

# ------------------------------------------------------------
# 认证和安全
# ------------------------------------------------------------
# JWT和加密
python-jose[cryptography]>=3.3.0
# 密码哈希
passlib[bcrypt]>=1.7.4
# 多部分表单支持
python-multipart>=0.0.6

# ------------------------------------------------------------
# 配置相关依赖
# ------------------------------------------------------------
# 环境变量配置
python-dotenv>=1.0.0
# YAML配置文件支持
pyyaml>=6.0

# ------------------------------------------------------------
# 模板引擎依赖
# ------------------------------------------------------------
# Jinja2 模板引擎
jinja2>=3.1.0

# ------------------------------------------------------------
# 第三方服务SDK
# ------------------------------------------------------------
# 爬虫框架(带Google搜索支持)
crawl4ai[google]>=0.3.0
# 火山引擎SDK
volcengine==1.0.193
# 火山引擎Python SDK
volcengine-python-sdk==4.0.4
# 火山引擎TOS对象存储SDK
tos==2.8.4
# Wikipedia API
wikipedia==1.4.0
# agno
agno>=1.7.2

# ------------------------------------------------------------
# 用户界面框架
# ------------------------------------------------------------
# Streamlit UI 框架
streamlit>=1.28.0

# ------------------------------------------------------------
# Python协程支持
# ------------------------------------------------------------
greenlet

# ------------------------------------------------------------
# 工具类
# ------------------------------------------------------------
img2pdf==0.6.1
