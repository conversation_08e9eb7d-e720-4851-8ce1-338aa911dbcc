# 应用配置
APP_NAME=ai-assistant
APP_VERSION=1.0.0
DEBUG=true



# Redis 配置
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
# REDIS_PASSWORD=your_redis_password_here

# MySQL 配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_password_here
MYSQL_DATABASE=ai_assistant
MYSQL_CHARSET=utf8mb4

# MySQL 连接池配置
MYSQL_POOL_SIZE=10
MYSQL_MAX_OVERFLOW=20
MYSQL_POOL_TIMEOUT=30
MYSQL_POOL_RECYCLE=3600
MYSQL_POOL_PRE_PING=true

# 日志配置
LOG_LEVEL=INFO

# 火山引擎配置
# 必填：访问密钥（从火山引擎控制台获取）
VOLCENGINE_ACCESS_KEY=your_access_key_here
VOLCENGINE_SECRET_KEY=your_secret_key_here

# 可选：地域配置
VOLCENGINE_REGION=cn-beijing

# 可选：自定义端点
# VOLCENGINE_ENDPOINT=

# 可选：连接池配置
VOLCENGINE_MAX_HOSTS=4
# VOLCENGINE_MAX_POOL_SIZE=  # 默认为 cpu_count() * 5
VOLCENGINE_CONNECT_TIMEOUT=30
VOLCENGINE_READ_TIMEOUT=30

# 可选：重试配置
VOLCENGINE_RETRY_COUNT=3
VOLCENGINE_RETRY_DELAY=1.0

# 可选：是否启用双栈网络(IPv4 + IPv6)
VOLCENGINE_ENABLE_DUAL_STACK=false

# 火山引擎语音转文本(STT)配置
# STT API 配置
VOLCENGINE_STT_APP_ID=your_stt_app_id_here
VOLCENGINE_STT_ACCESS_TOKEN=your_stt_access_token_here
VOLCENGINE_STT_ENDPOINT=https://openspeech.bytedance.com/api/v3/auc/bigmodel/recognize/flash
VOLCENGINE_STT_RESOURCE_ID=volc.bigasr.auc_turbo
VOLCENGINE_STT_MODEL_NAME=bigmodel

# 火山引擎TOS对象存储配置
TOS_ENDPOINT=https://tos-cn-beijing.volces.com
TOS_REGION=cn-beijing
TOS_BUCKET_NAME=your_bucket_name_here

# 可选：TOS性能配置
TOS_MAX_CONCURRENCY=50
TOS_CONNECT_TIMEOUT=30
TOS_READ_TIMEOUT=30
