# AI Assistant Docker 部署指南

## 项目概述

AI Assistant 是一个基于 FastAPI 和 Streamlit 的医疗报告分析系统，支持 PDF 文件的 OCR 识别、智能分析和结构化数据提取。

### 主要功能

- **FastAPI 后端服务**: 提供 API 接口，处理文件上传、OCR 解析、AI 分析
- **Streamlit 前端界面**: 用户友好的 Web 界面，支持文件上传和结果展示
- **多智能体系统**: 支持个人信息提取、总体结果分析、表格解析、影像数据提取
- **火山引擎集成**: 使用火山引擎的 LLM、TOS 对象存储、STT 语音转文本服务

## 系统要求

### 硬件要求

- **CPU**: 2 核以上
- **内存**: 8GB 以上
- **存储**: 20GB 以上可用空间
- **网络**: 稳定的互联网连接

### 软件要求

- **操作系统**: CentOS 7+ / Ubuntu 18.04+ / Red Hat 8+
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **端口**: 80, 443, 8000, 8501, 3306, 6379

## 快速开始

### 1. 环境准备

```bash
# 安装 Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 验证安装
docker --version
docker-compose --version
```

### 2. 获取代码

```bash
# 克隆项目（或上传代码包）
git clone <repository-url>
cd ai-assistant

# 或者解压代码包
tar -xzf ai-assistant.tar.gz
cd ai-assistant
```

### 3. 配置环境变量

```bash
# 复制环境配置模板
cp .env.docker .env.docker

# 编辑配置文件
vim .env.docker
```

**重要配置项:**

```bash
# 火山引擎 API Key（必需）
ARK_API_KEY=your_volcengine_api_key_here

# 火山引擎访问密钥（必需）
VOLCENGINE_ACCESS_KEY=your_volcengine_access_key
VOLCENGINE_SECRET_KEY=your_volcengine_secret_key

# TOS 对象存储桶名（必需）
TOS_BUCKET_NAME=your_tos_bucket_name

# MySQL 数据库密码
MYSQL_PASSWORD=your_secure_password

# 其他可选配置...
```

### 4. 部署启动

```bash
# 使用部署脚本（推荐）
./deploy.sh up -d --with-nginx

# 或者直接使用 docker-compose
docker-compose up -d
```

### 5. 访问服务

- **Streamlit 界面**: http://your-server-ip:8501
- **FastAPI 文档**: http://your-server-ip:8000/docs
- **健康检查**: http://your-server-ip:8000/health

### 6. 源码更新部署（已部署用户）

如果您已经完成过初始部署，现在只需要更新源码：

```bash
# 快速更新（推荐）
./deploy.sh build && ./deploy.sh up -d

# 或者使用滚动更新（零停机）
docker-compose up -d --build --force-recreate --no-deps fastapi streamlit
```

更多详细的更新选项请参考 [源码修改后的快速重新打包](#源码修改后的快速重新打包) 章节。

## 详细配置

### 环境变量说明

| 变量名 | 说明 | 默认值 | 必需 |
|--------|------|--------|------|
| `ARK_API_KEY` | 火山引擎 API 密钥 | - | ✅ |
| `VOLCENGINE_ACCESS_KEY` | 火山引擎访问密钥 | - | ✅ |
| `VOLCENGINE_SECRET_KEY` | 火山引擎秘密密钥 | - | ✅ |
| `TOS_BUCKET_NAME` | TOS 存储桶名称 | - | ✅ |
| `MYSQL_PASSWORD` | MySQL 密码 | `ai_password_123` | ❌ |
| `REDIS_PASSWORD` | Redis 密码 | - | ❌ |
| `DEBUG` | 调试模式 | `false` | ❌ |

### 服务端口配置

| 服务 | 内部端口 | 外部端口 | 说明 |
|------|----------|----------|------|
| FastAPI | 8000 | 8000 | API 服务 |
| Streamlit | 8501 | 8501 | Web 界面 |
| MySQL | 3306 | 3306 | 数据库 |
| Redis | 6379 | 6379 | 缓存 |
| Nginx | 80/443 | 80/443 | 反向代理 |

## 部署脚本使用

### 基本命令

```bash
# 构建镜像
./deploy.sh build

# 启动服务（包含构建）
./deploy.sh up -d

# 查看服务状态
./deploy.sh status

# 查看日志
./deploy.sh logs

# 停止服务
./deploy.sh down

# 重启服务
./deploy.sh restart

# 清理资源
./deploy.sh clean
```

### 高级选项

```bash
# 后台启动完整服务（包含 Nginx）
./deploy.sh up -d --with-nginx

# 重新构建镜像（不使用缓存）
./deploy.sh build --no-cache

# 强制停止所有服务
./deploy.sh down --force

# 查看特定服务日志
./deploy.sh logs fastapi
./deploy.sh logs streamlit
```

### 源码修改后的快速重新打包

当您只修改了源码文件（没有修改依赖），可以使用以下高效的重新部署流程：

```bash
# 方式一：利用缓存快速重新构建（推荐）
./deploy.sh build && ./deploy.sh up -d

# 方式二：手动 Docker Compose 操作
docker-compose build --no-cache fastapi streamlit
docker-compose up -d

# 方式三：滚动更新（零停机时间）
docker-compose up -d --build --force-recreate --no-deps fastapi streamlit
```

**各方式说明：**

- **方式一**：使用部署脚本，会自动利用Docker分层缓存，只重新构建包含源码变更的层
- **方式二**：直接使用docker-compose，手动控制构建过程
- **方式三**：适用于生产环境，实现零停机更新，只重新创建应用容器而不影响数据库和Redis

**时间对比：**
- 完整重新构建（--no-cache）：约 5-10 分钟
- 利用缓存重新构建：约 1-2 分钟
- 滚动更新：约 30-60 秒

### 开发环境快速迭代

针对频繁的代码修改和测试场景：

```bash
# 开发模式：挂载源码目录（需要修改 docker-compose.yml）
# 在 docker-compose.yml 中添加源码挂载：
# volumes:
#   - .:/app
#   - ./data:/app/data
#   - ./logs:/app/logs

# 只重启应用容器（保持数据库和缓存运行）
docker-compose restart fastapi streamlit

# 查看实时日志进行调试
./deploy.sh logs -f fastapi
```

### 依赖变更时的完整重新构建

当修改了 `requirements.txt` 或 `requirements-dev.txt` 时：

```bash
# 清理旧镜像并重新构建
./deploy.sh down
docker image rm ai-assistant:latest
./deploy.sh build --no-cache
./deploy.sh up -d
```

### 构建优化建议

1. **利用 .dockerignore**：确保不必要的文件不会影响构建缓存
2. **分层优化**：将变更较少的操作放在 Dockerfile 前面
3. **并行构建**：对于多服务可以并行构建镜像

```bash
# 检查构建缓存使用情况
docker system df

# 清理未使用的构建缓存（谨慎使用）
docker builder prune
```

### 数据备份和恢复

```bash
# 备份数据
./deploy.sh backup

# 恢复数据
./deploy.sh restore backup/20240122_143000
```

## 生产环境配置

### 1. 使用 Nginx 反向代理

```bash
# 启动包含 Nginx 的完整服务
./deploy.sh up -d --with-nginx --prod
```

### 2. SSL/TLS 配置

```bash
# 创建 SSL 证书目录
mkdir -p config/ssl

# 放置证书文件
cp your-cert.pem config/ssl/cert.pem
cp your-key.pem config/ssl/key.pem

# 修改 nginx.conf 取消 SSL 配置注释
vim config/nginx.conf
```

### 3. 防火墙配置

```bash
# CentOS/RHEL
firewall-cmd --permanent --add-port=80/tcp
firewall-cmd --permanent --add-port=443/tcp
firewall-cmd --permanent --add-port=8000/tcp
firewall-cmd --permanent --add-port=8501/tcp
firewall-cmd --reload

# Ubuntu/Debian
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 8000/tcp
ufw allow 8501/tcp
```

### 4. 系统服务配置（可选）

创建 systemd 服务文件：

```bash
sudo tee /etc/systemd/system/ai-assistant.service << EOF
[Unit]
Description=AI Assistant Docker Service
After=docker.service
Requires=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/path/to/ai-assistant
ExecStart=/path/to/ai-assistant/deploy.sh up -d --with-nginx
ExecStop=/path/to/ai-assistant/deploy.sh down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl enable ai-assistant.service
sudo systemctl start ai-assistant.service
```

## 监控和维护

### 健康检查

```bash
# 检查服务状态
./deploy.sh status

# 手动健康检查
curl -f http://localhost:8000/health
curl -f http://localhost:8501/_stcore/health
```

### 日志管理

```bash
# 查看实时日志
./deploy.sh logs -f

# 查看特定时间段日志
docker-compose logs --since="2024-01-22T14:00:00" --until="2024-01-22T15:00:00"

# 日志文件位置
ls logs/
```

### 性能监控

```bash
# 查看资源使用情况
docker stats

# 查看容器详细信息
docker inspect ai-assistant-fastapi
docker inspect ai-assistant-streamlit
```

## 故障排除

### 常见问题

#### 1. 服务启动失败

```bash
# 查看详细错误日志
./deploy.sh logs fastapi
./deploy.sh logs streamlit

# 检查环境变量配置
docker exec ai-assistant-fastapi env | grep -E "(ARK_|VOLCENGINE_|TOS_)"
```

#### 2. 数据库连接失败

```bash
# 检查 MySQL 状态
./deploy.sh logs mysql

# 手动测试连接
docker exec ai-assistant-mysql mysql -u root -p -e "SELECT 1"
```

#### 3. Redis 连接失败

```bash
# 检查 Redis 状态
docker exec ai-assistant-redis redis-cli ping
```

#### 4. 火山引擎 API 调用失败

```bash
# 检查网络连接
docker exec ai-assistant-fastapi curl -I https://ark.cn-beijing.volces.com

# 验证 API Key
docker exec ai-assistant-fastapi env | grep ARK_API_KEY
```

#### 5. 构建缓存问题

```bash
# 检查是否有构建缓存占用过多空间
docker system df

# 清理构建缓存（保留最近使用的）
docker builder prune --filter until=24h

# 强制清理所有构建缓存
docker builder prune -a
```

#### 6. 源码修改后服务没有更新

```bash
# 确认镜像是否重新构建
docker images ai-assistant:latest

# 检查容器创建时间
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.CreatedAt}}"

# 强制重新创建容器
docker-compose up -d --force-recreate fastapi streamlit
```

### 重置和清理

```bash
# 完全重置（删除所有数据）
./deploy.sh down --force
./deploy.sh clean --force
docker system prune -a

# 重新开始
./deploy.sh up -d --no-cache
```

## 升级和更新

### 应用程序升级

```bash
# 拉取最新代码
git pull origin main

# 重新构建和部署
./deploy.sh down
./deploy.sh up -d --no-cache
```

### Docker 镜像更新

```bash
# 拉取最新的基础镜像
docker-compose pull

# 重新构建应用镜像
./deploy.sh build --no-cache

# 重启服务
./deploy.sh restart
```
