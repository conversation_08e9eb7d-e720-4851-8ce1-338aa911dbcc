"""PDF 工具函数测试"""

import os
import sys
import tempfile
from pathlib import Path

import pytest

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)

from app.utils.pdf_utils import directory_to_pdf, images_to_pdf


class TestPdfUtils:
    """PDF 工具函数测试类"""

    def setup_method(self):
        """测试前准备"""
        self.test_image_dir = Path("/Users/<USER>/Downloads/img_tmps")
        self.temp_dir = Path(tempfile.mkdtemp())

    def teardown_method(self):
        """测试后清理"""
        # 清理临时文件
        import shutil

        if self.temp_dir.exists():
            shutil.rmtree(str(self.temp_dir))

    def test_images_to_pdf_basic(self):
        """测试基础图片转 PDF 功能"""
        # 获取测试图片列表
        image_files = list(self.test_image_dir.glob("*.jpg")) + list(self.test_image_dir.glob("*.jpeg"))
        assert len(image_files) > 0, f"测试目录 {self.test_image_dir} 中没有找到图片文件"

        # 转换为字符串路径
        image_paths = [str(f) for f in image_files[:2]]  # 只取前2个图片
        output_path = self.temp_dir / "test_basic.pdf"

        # 执行转换
        result = images_to_pdf(image_paths, str(output_path))

        # 验证结果
        assert result is True, "图片转 PDF 应该成功"
        assert output_path.exists(), "输出的 PDF 文件应该存在"
        assert output_path.stat().st_size > 0, "PDF 文件大小应该大于 0"

        print(f"✓ 基础图片转 PDF 测试成功！输出文件: {output_path}")
        print(f"  - 处理图片数量: {len(image_paths)}")
        print(f"  - PDF 文件大小: {output_path.stat().st_size} 字节")

    def test_directory_to_pdf_with_different_sort_orders(self):
        """测试目录转 PDF 的不同排序方式"""
        # 测试不同的排序方式
        sort_orders = [
            ("name_asc", "按文件名正序"),
            ("name_desc", "按文件名倒序"),
            ("modified_asc", "按修改时间正序"),
            ("modified_desc", "按修改时间倒序"),
        ]

        for sort_order, description in sort_orders:
            output_path = self.temp_dir / f"test_{sort_order}.pdf"

            # 执行转换
            result = directory_to_pdf(str(self.test_image_dir), str(output_path), sort_order=sort_order)

            # 验证结果
            assert result is True, f"{description} 转换应该成功"
            assert output_path.exists(), f"{description} 输出的 PDF 文件应该存在"
            assert output_path.stat().st_size > 0, f"{description} PDF 文件大小应该大于 0"

            print(f"✓ {description} 测试成功！")
            print(f"  - 输出文件: {output_path.name}")
            print(f"  - 文件大小: {output_path.stat().st_size} 字节")
