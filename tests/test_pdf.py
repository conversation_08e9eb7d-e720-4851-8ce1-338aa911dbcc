import json
import os
import sys

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(""))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)

from app.services.report_ocr_handler import get_report_ocr_handler

pdf_handler = get_report_ocr_handler()

# pdf_file_path = "/Users/<USER>/work/ai-assistant/data/test1.pdf"
# pdf_file_path = "/Users/<USER>/Downloads/output_30_37-已压缩.pdf"
pdf_file_path = "https://ai-resouce.tos-cn-beijing.volces.com/health_reports/1752570903_test1.pdf?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKLTMzg4MTM5Yjk3NTQ2NGJmMjlmNWJiYjk4YWQ3OGFmMWM%2F20250716%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250716T033417Z&X-Tos-Expires=3600&X-Tos-SignedHeaders=host&X-Tos-Signature=a0d8cbe88faa4dd14403faf6f2204081cc98f55506bfaeee094e3afb9d654c04"


def test_pdf_parse():
    """测试PDF解析"""
    result = pdf_handler.parse_pdf_url(file_url=pdf_file_path, page_start=1, page_num=30)

    assert result is not None, "PDF解析结果不应该为None"
    print("✓ PDF 解析成功！")
    print(result)

    # markdown, json 分别存储到本地
    with open("markdown.md", "w", encoding="utf-8") as f:
        f.write(result["markdown"])
    with open("json.json", "w", encoding="utf-8") as f:
        f.write(json.dumps(result["detail"], ensure_ascii=False))
