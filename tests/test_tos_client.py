"""TOS 客户端测试用例

测试火山引擎 TOS 对象存储客户端的实际上传功能
"""

import asyncio
import tempfile
from io import BytesIO, StringIO
from pathlib import Path
from unittest.mock import patch

import pytest

from app.client.tos_client import TosClient, get_tos_client
from app.core.config import settings


@pytest.fixture(scope="session")
def validate_tos_config():
    """验证 TOS 配置是否正确"""
    required_keys = [
        # "TOS_ACCESS_KEY",
        # "TOS_SECRET_KEY",
        "TOS_BUCKET_NAME",
    ]

    missing_keys = []
    for key in required_keys:
        if not getattr(settings, key, None):
            missing_keys.append(key)

    if missing_keys:
        pytest.skip(f"跳过 TOS 测试，缺少必要的配置项: {', '.join(missing_keys)}")

    return True


@pytest.fixture(scope="session")
def tos_client(validate_tos_config):
    """提供 TOS 客户端实例"""
    return get_tos_client()


@pytest.fixture(scope="session")
def test_bucket_name():
    """提供测试桶名称"""
    return settings.TOS_BUCKET_NAME


@pytest.fixture
def test_string_content():
    """提供测试字符串内容"""
    return "这是一个测试字符串内容\nHello World!\n中文测试"


@pytest.fixture
def test_bytes_content():
    """提供测试字节内容"""
    return b"\\x00\\x01\\x02\\x03Test bytes content\\xff\\xfe\\xfd"


@pytest.fixture
def test_file_content():
    """创建测试文件并返回路径"""
    with tempfile.NamedTemporaryFile(mode="w", suffix=".txt", delete=False) as f:
        f.write("这是一个测试文件内容\n")
        f.write("Test file content\n")
        f.write("多行测试内容\n")
        temp_path = f.name

    yield Path(temp_path)

    # 清理临时文件
    Path(temp_path).unlink(missing_ok=True)


@pytest.fixture
def test_folder():
    """创建测试文件夹"""
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        # 创建多个测试文件
        (temp_path / "file1.txt").write_text("File 1 content")
        (temp_path / "file2.txt").write_text("File 2 content")

        # 创建子文件夹
        sub_dir = temp_path / "subdir"
        sub_dir.mkdir()
        (sub_dir / "file3.txt").write_text("File 3 content")

        yield temp_path


class TestTosClientSync:
    """同步上传测试"""

    @pytest.mark.integration
    def test_upload_string(self, tos_client, test_bucket_name, test_string_content):
        """测试字符串上传"""
        object_key = "test/string_upload.txt"

        result = tos_client.upload_string(
            content=test_string_content, object_key=object_key, bucket_name=test_bucket_name
        )

        assert result["success"] is True
        assert result["bucket"] == test_bucket_name
        assert result["key"] == object_key
        assert result["size"] == len(test_string_content)
        assert "etag" in result

    @pytest.mark.integration
    def test_upload_bytes(self, tos_client, test_bucket_name, test_bytes_content):
        """测试字节上传"""
        object_key = "test/bytes_upload.bin"

        result = tos_client.upload_bytes(
            content=test_bytes_content, object_key=object_key, bucket_name=test_bucket_name
        )

        assert result["success"] is True
        assert result["bucket"] == test_bucket_name
        assert result["key"] == object_key
        assert result["size"] == len(test_bytes_content)
        assert "etag" in result

    @pytest.mark.integration
    def test_upload_string_stream(self, tos_client, test_bucket_name, test_string_content):
        """测试字符串流上传"""
        object_key = "test/string_stream_upload.txt"
        string_stream = StringIO(test_string_content)

        result = tos_client.upload_stream(
            stream=string_stream, object_key=object_key, bucket_name=test_bucket_name, content_type="text/plain"
        )

        assert result["success"] is True
        assert result["bucket"] == test_bucket_name
        assert result["key"] == object_key
        assert "etag" in result

    @pytest.mark.integration
    def test_upload_bytes_stream(self, tos_client, test_bucket_name, test_bytes_content):
        """测试字节流上传"""
        object_key = "test/bytes_stream_upload.bin"
        bytes_stream = BytesIO(test_bytes_content)

        result = tos_client.upload_stream(
            stream=bytes_stream,
            object_key=object_key,
            bucket_name=test_bucket_name,
            content_type="application/octet-stream",
        )

        assert result["success"] is True
        assert result["bucket"] == test_bucket_name
        assert result["key"] == object_key
        assert "etag" in result

    @pytest.mark.integration
    def test_upload_file(self, tos_client, test_bucket_name, test_file_content):
        """测试文件上传"""
        object_key = "test/file_upload.txt"

        result = tos_client.upload_file(
            file_path=test_file_content, object_key=object_key, bucket_name=test_bucket_name
        )

        assert result["success"] is True
        assert result["bucket"] == test_bucket_name
        assert result["key"] == object_key
        assert result["file_path"] == str(test_file_content)
        assert result["size"] == test_file_content.stat().st_size
        assert "etag" in result

    @pytest.mark.integration
    def test_upload_file_auto_key(self, tos_client, test_bucket_name, test_file_content):
        """测试文件上传（自动生成对象键）"""
        result = tos_client.upload_file(file_path=test_file_content, bucket_name=test_bucket_name)

        assert result["success"] is True
        assert result["bucket"] == test_bucket_name
        assert result["key"] == test_file_content.name  # 应该使用文件名作为对象键
        assert "etag" in result

    @pytest.mark.integration
    def test_upload_file_with_custom_content_type(self, tos_client, test_bucket_name, test_file_content):
        """测试文件上传（自定义内容类型）"""
        object_key = "test/file_with_custom_type.txt"

        result = tos_client.upload_file(
            file_path=test_file_content,
            object_key=object_key,
            bucket_name=test_bucket_name,
            content_type="text/plain; charset=utf-8",
        )

        assert result["success"] is True
        assert result["bucket"] == test_bucket_name
        assert result["key"] == object_key
        assert "etag" in result

    @pytest.mark.integration
    def test_get_upload_url(self, tos_client, test_bucket_name):
        """测试获取上传预签名 URL"""
        object_key = "health_reports/1752570903_test1.pdf"

        # 测试获取上传预签名 URL
        upload_url = tos_client.get_upload_url(object_key=object_key, bucket_name=test_bucket_name, expires=3600)
        print(upload_url)

        # 验证返回的 URL 格式正确
        assert isinstance(upload_url, str)
        assert upload_url.startswith("https://")
        assert test_bucket_name in upload_url
        assert "health_reports/1752570903_test1.pdf" in upload_url

        # 验证 URL 包含签名相关参数
        assert "X-Tos-Signature" in upload_url or "Signature" in upload_url
        assert "X-Tos-Date" in upload_url or "Date" in upload_url or "Expires" in upload_url

        print(f"Generated upload URL: {upload_url}")

    @pytest.mark.integration
    def test_get_object_url_with_different_methods(self, tos_client, test_bucket_name):
        """测试获取不同 HTTP 方法的预签名 URL"""
        import tos

        object_key = "health_reports/1752570903_test1.pdf"

        # 测试 GET 方法（默认）
        get_url = tos_client.get_object_url(object_key=object_key, bucket_name=test_bucket_name)
        assert isinstance(get_url, str)
        assert get_url.startswith("https://")

        # 测试 PUT 方法
        put_url = tos_client.get_object_url(
            object_key=object_key, bucket_name=test_bucket_name, http_method=tos.HttpMethodType.Http_Method_Put
        )
        assert isinstance(put_url, str)
        assert put_url.startswith("https://")

        # 两个 URL 应该不同（因为 HTTP 方法不同）
        assert get_url != put_url

        print(f"GET URL: {get_url}")
        print(f"PUT URL: {put_url}")

    @pytest.mark.integration
    def test_get_download_url_for_existing_file(self, tos_client, test_bucket_name):
        """测试获取已存在文件的下载预签名 URL"""
        object_key = "health_reports/1752570903_test1.pdf"

        # 测试获取下载预签名 URL
        download_url = tos_client.get_download_url(object_key=object_key, bucket_name=test_bucket_name, expires=3600)

        # 验证返回的 URL 格式正确
        assert isinstance(download_url, str)
        assert download_url.startswith("https://")
        assert test_bucket_name in download_url
        assert "health_reports/1752570903_test1.pdf" in download_url

        # 验证 URL 包含签名相关参数
        assert "X-Tos-Signature" in download_url or "Signature" in download_url
        assert "X-Tos-Date" in download_url or "Date" in download_url or "Expires" in download_url

        print(f"Generated download URL: {download_url}")

        # 验证上传 URL 和下载 URL 应该不同
        upload_url = tos_client.get_upload_url(object_key=object_key, bucket_name=test_bucket_name, expires=3600)

        # 两个 URL 应该不同（因为 HTTP 方法不同）
        assert upload_url != download_url

        print(f"Upload URL: {upload_url}")
        print(f"Download URL: {download_url}")

    @pytest.mark.integration
    def test_download_to_file(self, tos_client, test_bucket_name, test_string_content):
        """测试下载到文件"""
        # 先上传一个测试文件
        object_key = "code.json"

        # 测试下载到默认目录
        result = tos_client.download_to_file(object_key=object_key, bucket_name=test_bucket_name)

        assert result["success"] is True
        assert result["bucket"] == test_bucket_name
        assert result["key"] == object_key

    @pytest.mark.integration
    def test_download_to_memory(self, tos_client, test_bucket_name, test_string_content):
        """测试下载到内存"""

        object_key = "1.txt"

        # 测试下载到内存
        result = tos_client.download_to_memory(object_key=object_key, bucket_name=test_bucket_name)
        print(result)

        assert result["success"] is True
        assert result["bucket"] == test_bucket_name
        assert result["key"] == object_key
        assert result["content"].read().decode() == test_string_content


class TestTosClientAsync:
    """异步上传测试"""

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_async_upload_string(self, tos_client, test_bucket_name, test_string_content):
        """测试异步字符串上传"""
        object_key = "test/async_string_upload.txt"

        result = await tos_client.async_upload_string(
            content=test_string_content, object_key=object_key, bucket_name=test_bucket_name
        )

        assert result["success"] is True
        assert result["bucket"] == test_bucket_name
        assert result["key"] == object_key
        assert result["size"] == len(test_string_content)
        assert "etag" in result

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_async_upload_bytes(self, tos_client, test_bucket_name, test_bytes_content):
        """测试异步字节上传"""
        object_key = "test/async_bytes_upload.bin"

        result = await tos_client.async_upload_bytes(
            content=test_bytes_content, object_key=object_key, bucket_name=test_bucket_name
        )

        assert result["success"] is True
        assert result["bucket"] == test_bucket_name
        assert result["key"] == object_key
        assert result["size"] == len(test_bytes_content)
        assert "etag" in result

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_async_upload_stream(self, tos_client, test_bucket_name, test_string_content):
        """测试异步流上传"""
        object_key = "test/async_stream_upload.txt"
        string_stream = StringIO(test_string_content)

        result = await tos_client.async_upload_stream(
            stream=string_stream, object_key=object_key, bucket_name=test_bucket_name, content_type="text/plain"
        )

        assert result["success"] is True
        assert result["bucket"] == test_bucket_name
        assert result["key"] == object_key
        assert "etag" in result

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_async_upload_file(self, tos_client, test_bucket_name, test_file_content):
        """测试异步文件上传"""
        object_key = "test/async_file_upload.txt"

        result = await tos_client.async_upload_file(
            file_path=test_file_content, object_key=object_key, bucket_name=test_bucket_name
        )

        assert result["success"] is True
        assert result["bucket"] == test_bucket_name
        assert result["key"] == object_key
        assert result["file_path"] == str(test_file_content)
        assert result["size"] == test_file_content.stat().st_size
        assert "etag" in result

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_async_upload_folder(self, tos_client, test_bucket_name, test_folder):
        """测试异步文件夹上传"""
        object_prefix = "test/async_folder_upload/"

        result = await tos_client.async_upload_folder(
            folder_path=test_folder, object_prefix=object_prefix, bucket_name=test_bucket_name
        )

        assert result["success"] is True
        assert result["folder_path"] == str(test_folder)
        assert result["total_files"] == 3  # file1.txt, file2.txt, subdir/file3.txt
        assert result["successful_uploads"] == 3
        assert result["failed_uploads"] == 0
        assert len(result["results"]) == 3

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_async_concurrent_uploads(self, tos_client, test_bucket_name, test_string_content):
        """测试异步并发上传"""
        tasks = []
        for i in range(5):
            object_key = f"test/concurrent_upload_{i}.txt"
            task = tos_client.async_upload_string(
                content=f"{test_string_content} - {i}", object_key=object_key, bucket_name=test_bucket_name
            )
            tasks.append(task)

        results = await asyncio.gather(*tasks)

        # 验证所有上传都成功
        assert len(results) == 5
        for i, result in enumerate(results):
            assert result["success"] is True
            assert result["bucket"] == test_bucket_name
            assert result["key"] == f"test/concurrent_upload_{i}.txt"
            assert "etag" in result

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_async_download_to_file(self, tos_client, test_bucket_name, test_string_content):
        """测试异步下载到文件"""
        # 先上传一个测试文件
        object_key = "test/async_download_test.txt"
        await tos_client.async_upload_string(
            content=test_string_content, object_key=object_key, bucket_name=test_bucket_name
        )

        # 测试异步下载到文件
        result = await tos_client.async_download_to_file(object_key=object_key, bucket_name=test_bucket_name)

        assert result["success"] is True
        assert result["bucket"] == test_bucket_name
        assert result["key"] == object_key
        assert Path(result["file_path"]).exists()
        assert Path(result["file_path"]).read_text() == test_string_content

        # 清理下载的文件
        Path(result["file_path"]).unlink()

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_async_download_to_memory(self, tos_client, test_bucket_name, test_string_content):
        """测试异步下载到内存"""
        # 先上传一个测试文件
        object_key = "test/async_download_memory_test.txt"
        await tos_client.async_upload_string(
            content=test_string_content, object_key=object_key, bucket_name=test_bucket_name
        )

        # 测试异步下载到内存
        result = await tos_client.async_download_to_memory(object_key=object_key, bucket_name=test_bucket_name)

        assert result["success"] is True
        assert result["bucket"] == test_bucket_name
        assert result["key"] == object_key
        assert result["content"].read().decode() == test_string_content


class TestTosClientSingleton:
    """单例模式测试"""

    def test_singleton_instance(self):
        """测试单例模式"""
        client1 = TosClient()
        client2 = TosClient()
        client3 = get_tos_client()

        assert client1 is client2
        assert client2 is client3
        assert id(client1) == id(client2) == id(client3)

    def test_get_raw_client(self, tos_client):
        """测试获取原始 TOS 客户端"""
        raw_client = tos_client.get_client()
        assert raw_client is not None
        assert hasattr(raw_client, "put_object")


class TestTosClientErrorHandling:
    """错误处理测试"""

    def test_upload_nonexistent_file(self, tos_client, test_bucket_name):
        """测试上传不存在的文件"""
        with pytest.raises(FileNotFoundError):
            tos_client.upload_file(
                file_path="/nonexistent/file.txt", object_key="test/nonexistent.txt", bucket_name=test_bucket_name
            )

    @pytest.mark.integration
    def test_upload_with_invalid_bucket(self, tos_client, test_string_content):
        """测试使用无效存储桶"""
        with pytest.raises(Exception):  # TOS 会抛出相应的异常
            tos_client.upload_string(
                content=test_string_content,
                object_key="test/invalid_bucket.txt",
                bucket_name="invalid-bucket-name-12345",
            )

    def test_client_not_initialized(self):
        """测试客户端未初始化的情况"""
        with patch("app.core.config.settings.TOS_ACCESS_KEY", None):
            with pytest.raises(ValueError, match="TOS_ACCESS_KEY and TOS_SECRET_KEY must be set"):
                TosClient()

    def test_missing_bucket_name(self):
        """测试缺少存储桶名称"""
        with patch("app.core.config.settings.TOS_BUCKET_NAME", None):
            with pytest.raises(ValueError, match="TOS_BUCKET_NAME must be set"):
                TosClient()

    @pytest.mark.integration
    def test_download_nonexistent_object(self, tos_client, test_bucket_name):
        """测试下载不存在的对象"""
        with pytest.raises(Exception):  # TOS 会抛出相应的异常
            tos_client.download_to_file(object_key="nonexistent/file.txt", bucket_name=test_bucket_name)


# 运行测试的便捷函数
if __name__ == "__main__":
    pytest.main([__file__, "-v"])
