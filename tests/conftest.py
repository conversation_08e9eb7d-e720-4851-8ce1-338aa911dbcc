"""pytest 配置文件

设置测试环境，确保配置正确加载
"""

import os
import sys
from pathlib import Path

import pytest

# 获取项目根目录
PROJECT_ROOT = Path(__file__).parent.parent

# 添加项目根目录到 Python 路径
sys.path.insert(0, str(PROJECT_ROOT))

# 设置工作目录为项目根目录
os.chdir(PROJECT_ROOT)


@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """设置测试环境"""
    # 确保在项目根目录运行测试
    original_cwd = os.getcwd()
    os.chdir(PROJECT_ROOT)

    # 确保 .env 文件存在
    env_file = PROJECT_ROOT / ".env"
    if not env_file.exists():
        pytest.fail(f"环境配置文件 {env_file} 不存在，请先创建配置文件")

    print(f"测试环境设置完成，当前工作目录: {os.getcwd()}")
    print(f"环境文件位置: {env_file}")

    yield

    # 恢复原始工作目录
    os.chdir(original_cwd)


@pytest.fixture(scope="session")
def test_settings():
    """提供测试配置"""
    from app.core.config import settings

    return settings


@pytest.fixture(scope="session")
def validate_config(test_settings):
    """验证配置是否正确加载"""
    required_keys = [
        "VOLCENGINE_ACCESS_KEY",
        "VOLCENGINE_SECRET_KEY",
        "VOLCENGINE_KB_HOST",
    ]

    missing_keys = []
    for key in required_keys:
        if not getattr(test_settings, key, None):
            missing_keys.append(key)

    if missing_keys:
        pytest.fail(f"缺少必要的配置项: {', '.join(missing_keys)}")

    return test_settings
