"""JSON 工具函数测试用例

测试 app.utils.json_utils 模块中的各种 JSON 处理功能：
- safe_json_parse: 安全 JSON 解析
- pretty_json: JSON 美化输出
- validate_json_schema: JSON 模式验证
- merge_json_objects: JSON 对象合并

运行测试：
pytest tests/test_json_utils.py -v -s
"""

import json

import pytest

from app.utils.json_utils import (
    merge_json_objects,
    pretty_json,
    safe_json_parse,
    validate_json_schema,
)


@pytest.fixture
def medical_report_markdown_json():
    """医疗报告的 Markdown 格式 JSON 测试数据"""
    return """```json
{
  "checkSummary": [
    {
      "name": "身高、体重",
      "result": "27.18",
      "unit": "kg/m²",
      "referenceRange": "18.5~23.9",
      "status": "偏高",
      "abnormalFlag": "↑",
      "remark": "BMI超重"
    },
    {
      "name": "妇科普检",
      "result": "柱状上皮异位(中度)",
      "unit": "",
      "referenceRange": "",
      "status": "异常",
      "abnormalFlag": "",
      "remark": "宫颈病变"
    },
    {
      "name": "十二导心电分析",
      "result": "大致正常心电图",
      "unit": "",
      "referenceRange": "",
      "status": "正常",
      "abnormalFlag": "",
      "remark": "窦性心律(HR 68 BPM)"
    },
    {
      "name": "胸部CT",
      "result": "两肺未见活动性病变,纵隔未见肿大淋巴结",
      "unit": "",
      "referenceRange": "",
      "status": "正常",
      "abnormalFlag": "",
      "remark": ""
    },
    {
      "name": "彩超(肝胆胰脾、双肾、子宫附件)",
      "result": "轻度脂肪肝;胆囊壁胆固醇结晶",
      "unit": "",
      "referenceRange": "",
      "status": "异常",
      "abnormalFlag": "",
      "remark": ""
    },
    {
      "name": "彩超(甲状腺)",
      "result": "甲状腺左叶结节,ACR TI-RADS3类;甲状腺弥漫性病变",
      "unit": "",
      "referenceRange": "",
      "status": "异常",
      "abnormalFlag": "",
      "remark": "低度可疑恶性,<5%恶性风险,结节≥2.5cm建议FNA,≥1.5cm随访"
    },
    {
      "name": "彩超(乳腺)",
      "result": "左侧乳腺囊肿,BI-RADS2类;左侧乳腺导管局限性扩张,BI-RADS 2类",
      "unit": "",
      "referenceRange": "",
      "status": "异常",
      "abnormalFlag": "",
      "remark": ""
    },
    {
      "name": "血细胞分析+五分类",
      "result": "11.5",
      "unit": "%",
      "referenceRange": "11.9~14.5",
      "status": "偏低",
      "abnormalFlag": "↓",
      "remark": "红细胞分布宽度-CV值降低"
    },
    {
      "name": "平诊血脂四项[复]",
      "result": "1.20",
      "unit": "mmol/L",
      "referenceRange": "1.29~1.55",
      "status": "偏低",
      "abnormalFlag": "↓",
      "remark": "高密度脂蛋白降低"
    },
    {
      "name": "平诊肝功五项[复]",
      "result": "64.4",
      "unit": "g/L",
      "referenceRange": "65.0~85.0",
      "status": "偏低",
      "abnormalFlag": "↓",
      "remark": "总蛋白降低"
    }
  ],
  "overallResult": {
    "healthAssessment": "存在多项代谢及器官异常,需关注心血管风险及内分泌、乳腺、肝胆系统健康",
    "mainFindings": [
      "高密度脂蛋白偏低",
      "超重",
      "轻度脂肪肝",
      "甲状腺左叶结节甲状腺弥漫性病变",
      "左侧乳腺囊肿左侧乳腺导管局限性扩张",
      "胆囊胆固醇结晶",
      "总蛋白降低"
    ],
    "attentionPoints": [
      "甲状腺左叶结节需定期随访观察",
      "乳腺病变需定期复查",
      "血脂异常与超重需积极干预",
      "肝功能指标轻度异常需关注"
    ],
    "riskLevel": "中等风险"
  },
  "healthAdvice": {
    "dietaryAdvice": [
      "低脂、低糖、低盐饮食",
      "多食新鲜蔬菜、粗粮",
      "控制食量,使每日摄入能量略低于消耗能量",
      "补充优质蛋白",
      "避免过饱、过油饮食"
    ],
    "exerciseAdvice": [
      "适当加强体育锻炼",
      "进行有氧运动或适度活动",
      "坚持体育锻炼,如慢步跑、快步走、骑自行车、上下楼梯、游泳等"
    ]
  },
  "additionalNotes": [
    "定期复查血脂,必要时到心内科门诊就诊",
    "甲状腺结节建议定期到内分泌科随访观察",
    "乳腺病变建议定期复查随访,必要时到乳腺外科就诊",
    "胆囊胆固醇结晶建议定期复查胆囊B超,必要时到肝胆外科门诊就诊",
    "总蛋白降低建议加强营养,定期复查肝功能"
  ]
}
```"""


class TestSafeJsonParse:
    """测试安全 JSON 解析功能"""

    def test_parse_markdown_json_success(self, medical_report_markdown_json):
        """测试解析 Markdown 格式的 JSON 成功"""
        result = safe_json_parse(medical_report_markdown_json)

        # 验证基本结构
        assert isinstance(result, dict)
        assert "checkSummary" in result
        assert "overallResult" in result
        assert "healthAdvice" in result
        assert "additionalNotes" in result

        # 验证检查汇总数据
        check_summary = result["checkSummary"]
        assert isinstance(check_summary, list)
        assert len(check_summary) == 10

        # 验证第一项检查数据
        first_check = check_summary[0]
        assert first_check["name"] == "身高、体重"
        assert first_check["result"] == "27.18"
        assert first_check["unit"] == "kg/m²"
        assert first_check["status"] == "偏高"
        assert first_check["abnormalFlag"] == "↑"

        # 验证整体结果
        overall_result = result["overallResult"]
        assert overall_result["riskLevel"] == "中等风险"
        assert isinstance(overall_result["mainFindings"], list)
        assert len(overall_result["mainFindings"]) == 7

        # 验证健康建议
        health_advice = result["healthAdvice"]
        assert "dietaryAdvice" in health_advice
        assert "exerciseAdvice" in health_advice
        assert isinstance(health_advice["dietaryAdvice"], list)
        assert len(health_advice["dietaryAdvice"]) == 5

    def test_parse_simple_json_string(self):
        """测试解析简单的 JSON 字符串"""
        json_str = '{"name": "测试", "value": 123}'
        result = safe_json_parse(json_str)

        assert result["name"] == "测试"
        assert result["value"] == 123

    def test_parse_json_with_comments(self):
        """测试解析带注释的 JSON"""
        json_with_comments = """
        {
            "name": "测试数据", // 这是名称
            "items": [
                "item1", // 第一项
                "item2"  // 第二项
            ]
            /* 这是多行注释
               包含中文测试 */
        }
        """
        result = safe_json_parse(json_with_comments)

        assert result["name"] == "测试数据"
        assert result["items"] == ["item1", "item2"]

    def test_parse_json_with_trailing_commas(self):
        """测试解析带尾随逗号的 JSON"""
        json_with_trailing_commas = """
        {
            "name": "测试",
            "list": [1, 2, 3,],
            "nested": {
                "key": "value",
            },
        }
        """
        result = safe_json_parse(json_with_trailing_commas)

        assert result["name"] == "测试"
        assert result["list"] == [1, 2, 3]
        assert result["nested"]["key"] == "value"

    def test_parse_json_with_single_quotes(self):
        """测试解析单引号 JSON"""
        json_single_quotes = "{'name': '测试', 'value': '单引号测试'}"
        result = safe_json_parse(json_single_quotes)

        assert result["name"] == "测试"
        assert result["value"] == "单引号测试"

    def test_parse_incomplete_json_auto_fix(self):
        """测试解析不完整的 JSON 并自动修复"""
        incomplete_json = '{"name": "测试", "items": [1, 2, 3'
        result = safe_json_parse(incomplete_json)

        # 如果自动修复成功，应该能获取到数据
        # 如果修复失败，会返回包含 error 的字典
        if "error" not in result:
            assert result["name"] == "测试"
            assert result["items"] == [1, 2, 3]
        else:
            # 修复失败的情况，验证错误结构
            assert "error" in result
            assert "original_text" in result
            assert incomplete_json in result["original_text"]

    def test_parse_multiple_markdown_blocks(self):
        """测试解析多个 Markdown 代码块，应选择第一个"""
        multiple_blocks = """
        一些文本
        ```json
        {"first": "第一个块"}
        ```
        更多文本
        ```json
        {"second": "第二个块"}
        ```
        """
        result = safe_json_parse(multiple_blocks)

        assert result["first"] == "第一个块"
        assert "second" not in result

    def test_parse_completely_invalid_json(self):
        """测试解析完全无效的 JSON"""
        invalid_text = "这完全不是 JSON 数据"
        result = safe_json_parse(invalid_text)

        assert "error" in result
        assert result["original_text"] == invalid_text

    def test_parse_partial_json_extraction(self):
        """测试从文本中提取部分有效的 JSON"""
        text_with_json = """
        前面是一些文本
        {"name": "测试", "value": 123}
        后面也有文本
        """
        result = safe_json_parse(text_with_json)

        assert result["name"] == "测试"
        assert result["value"] == 123


class TestPrettyJson:
    """测试 JSON 美化输出功能"""

    def test_pretty_json_dict(self):
        """测试字典的美化输出"""
        data = {"name": "测试", "items": [1, 2, 3], "nested": {"key": "value"}}
        result = pretty_json(data)

        # 验证是有效的 JSON 字符串
        parsed = json.loads(result)
        assert parsed == data

        # 验证格式化效果（包含换行和缩进）
        assert "\n" in result
        assert "  " in result

    def test_pretty_json_list(self):
        """测试列表的美化输出"""
        data = ["项目1", "项目2", {"嵌套": "对象"}]
        result = pretty_json(data)

        parsed = json.loads(result)
        assert parsed == data

    def test_pretty_json_with_chinese_no_ascii(self):
        """测试包含中文的 JSON（不转义 ASCII）"""
        data = {"中文键": "中文值", "english": "value"}
        result = pretty_json(data, ensure_ascii=False)

        # 直接包含中文字符，不应该被转义
        assert "中文键" in result
        assert "中文值" in result
        assert "\\u" not in result

    def test_pretty_json_custom_indent(self):
        """测试自定义缩进"""
        data = {"level1": {"level2": "value"}}
        result = pretty_json(data, indent=4)

        # 验证使用了 4 空格缩进
        lines = result.split("\n")
        assert any(line.startswith("    ") for line in lines)

    def test_pretty_json_invalid_data(self):
        """测试无效数据的处理"""
        # 不可序列化的对象
        invalid_data = {"function": lambda x: x}
        result = pretty_json(invalid_data)

        # 应该返回字符串表示
        assert isinstance(result, str)


class TestValidateJsonSchema:
    """测试 JSON 模式验证功能"""

    def test_validate_schema_all_required_keys_present(self):
        """测试所有必需键都存在的情况"""
        data = {"checkSummary": [], "overallResult": {}, "healthAdvice": {}, "additionalNotes": []}
        required_keys = ["checkSummary", "overallResult", "healthAdvice"]

        is_valid, missing_keys = validate_json_schema(data, required_keys)

        assert is_valid is True
        assert missing_keys == []

    def test_validate_schema_missing_keys(self):
        """测试缺少必需键的情况"""
        data = {
            "checkSummary": [],
            "overallResult": {},
            # 缺少 healthAdvice 和 additionalNotes
        }
        required_keys = ["checkSummary", "overallResult", "healthAdvice", "additionalNotes"]

        is_valid, missing_keys = validate_json_schema(data, required_keys)

        assert is_valid is False
        assert set(missing_keys) == {"healthAdvice", "additionalNotes"}

    def test_validate_schema_empty_required_keys(self):
        """测试空的必需键列表"""
        data = {"任意": "数据"}
        required_keys = []

        is_valid, missing_keys = validate_json_schema(data, required_keys)

        assert is_valid is True
        assert missing_keys == []

    def test_validate_schema_extra_keys_allowed(self):
        """测试额外键是被允许的"""
        data = {"required1": "值1", "required2": "值2", "extra1": "额外值1", "extra2": "额外值2"}
        required_keys = ["required1", "required2"]

        is_valid, missing_keys = validate_json_schema(data, required_keys)

        assert is_valid is True
        assert missing_keys == []


class TestMergeJsonObjects:
    """测试 JSON 对象合并功能"""

    def test_merge_simple_objects(self):
        """测试简单对象合并"""
        obj1 = {"a": 1, "b": 2}
        obj2 = {"c": 3, "d": 4}

        result = merge_json_objects(obj1, obj2)

        expected = {"a": 1, "b": 2, "c": 3, "d": 4}
        assert result == expected

    def test_merge_overlapping_objects(self):
        """测试有重叠键的对象合并"""
        obj1 = {"a": 1, "b": 2, "c": {"nested1": "值1"}}
        obj2 = {"b": 20, "c": {"nested2": "值2"}, "d": 4}

        result = merge_json_objects(obj1, obj2)

        # b 应该被 obj2 的值覆盖
        # c 应该深度合并
        expected = {"a": 1, "b": 20, "c": {"nested1": "值1", "nested2": "值2"}, "d": 4}
        assert result == expected

    def test_merge_medical_report_sections(self):
        """测试合并医疗报告的不同部分"""
        basic_info = {"patientInfo": {"name": "测试患者", "age": 35}, "checkDate": "2024-01-15"}

        check_results = {
            "checkSummary": [{"name": "血压", "result": "120/80", "status": "正常"}],
            "patientInfo": {"gender": "女"},  # 这会与 basic_info 的 patientInfo 合并
        }

        advice = {"healthAdvice": {"dietaryAdvice": ["均衡饮食"], "exerciseAdvice": ["适度运动"]}}

        result = merge_json_objects(basic_info, check_results, advice)

        # 验证合并结果
        assert result["checkDate"] == "2024-01-15"
        assert len(result["checkSummary"]) == 1
        assert result["checkSummary"][0]["name"] == "血压"

        # 验证深度合并
        patient_info = result["patientInfo"]
        assert patient_info["name"] == "测试患者"
        assert patient_info["age"] == 35
        assert patient_info["gender"] == "女"

        # 验证健康建议
        assert "dietaryAdvice" in result["healthAdvice"]
        assert "exerciseAdvice" in result["healthAdvice"]

    def test_merge_deeply_nested_objects(self):
        """测试深度嵌套对象的合并"""
        obj1 = {"level1": {"level2": {"level3": {"a": 1, "b": 2}}}}

        obj2 = {"level1": {"level2": {"level3": {"c": 3}, "other": "值"}}}

        result = merge_json_objects(obj1, obj2)

        expected = {"level1": {"level2": {"level3": {"a": 1, "b": 2, "c": 3}, "other": "值"}}}
        assert result == expected

    def test_merge_non_dict_values_override(self):
        """测试非字典值的覆盖行为"""
        obj1 = {"key": "原始值"}
        obj2 = {"key": ["新", "列", "表"]}

        result = merge_json_objects(obj1, obj2)

        # 非字典值应该被覆盖，不是合并
        assert result["key"] == ["新", "列", "表"]

    def test_merge_with_non_dict_arguments(self):
        """测试传入非字典参数的处理"""
        obj1 = {"a": 1}
        not_dict = "这不是字典"
        obj2 = {"b": 2}

        result = merge_json_objects(obj1, not_dict, obj2)

        # 非字典参数应该被忽略
        expected = {"a": 1, "b": 2}
        assert result == expected

    def test_merge_empty_objects(self):
        """测试合并空对象"""
        result = merge_json_objects({}, {}, {})
        assert result == {}

        obj = {"key": "value"}
        result = merge_json_objects({}, obj, {})
        assert result == obj


class TestJsonUtilsIntegration:
    """集成测试 - 测试多个功能的组合使用"""

    def test_parse_validate_and_format_workflow(self, medical_report_markdown_json):
        """测试解析-验证-格式化的完整工作流程"""
        # 1. 解析 JSON
        parsed_data = safe_json_parse(medical_report_markdown_json)

        # 2. 验证必需字段
        required_fields = ["checkSummary", "overallResult", "healthAdvice"]
        is_valid, missing = validate_json_schema(parsed_data, required_fields)
        assert is_valid is True

        # 3. 格式化输出
        formatted_json = pretty_json(parsed_data, ensure_ascii=False)

        # 4. 验证格式化后的 JSON 可以重新解析
        reparsed = json.loads(formatted_json)
        assert reparsed == parsed_data

        # 5. 验证格式化包含中文字符
        assert "身高、体重" in formatted_json
        assert "中等风险" in formatted_json

    def test_merge_and_validate_medical_reports(self):
        """测试合并和验证多个医疗报告的数据"""
        # 模拟来自不同来源的医疗数据
        lab_results = {
            "labResults": {
                "bloodTest": {"result": "正常", "date": "2024-01-15"},
                "urineTest": {"result": "正常", "date": "2024-01-15"},
            },
            "patientId": "P001",
        }

        imaging_results = {
            "imagingResults": {
                "xray": {"result": "未见异常", "date": "2024-01-16"},
                "ct": {"result": "未见明显异常", "date": "2024-01-16"},
            },
            "doctorNotes": "建议定期复查",
        }

        patient_info = {
            "patientId": "P001",  # 重复键，应该保持一致
            "personalInfo": {"name": "张三", "age": 45, "gender": "男"},
        }

        # 合并数据
        combined_report = merge_json_objects(lab_results, imaging_results, patient_info)

        # 验证合并结果
        assert combined_report["patientId"] == "P001"
        assert "labResults" in combined_report
        assert "imagingResults" in combined_report
        assert "personalInfo" in combined_report
        assert combined_report["doctorNotes"] == "建议定期复查"

        # 验证必需字段
        required_fields = ["patientId", "personalInfo"]
        is_valid, missing = validate_json_schema(combined_report, required_fields)
        assert is_valid is True

        # 格式化并验证输出
        formatted = pretty_json(combined_report, ensure_ascii=False)
        assert "张三" in formatted
        assert json.loads(formatted) == combined_report


# 运行测试的便捷函数
if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
