import json
from pathlib import Path

from app.core.template_loader import Agent<PERSON><PERSON><PERSON>Config, AgentType, get_template_loader


def test_get_prompt():
    _template_loader = get_template_loader()
    tp: AgentTemplateConfig = _template_loader.get_config(AgentType.TABULAR_PARSER)
    print(tp)

    # 读取 app目录同级的 data/medical_exam_structured.json 作为 reference_interval 的值
    current_dir = Path(__file__).parent.parent.parent  # 从 tests/llm/ 回到项目根目录
    json_file_path = current_dir / "data" / "medical_exam_structured.json"

    with open(json_file_path, "r", encoding="utf-8") as f:
        reference_interval = json.load(f)

    prompt = _template_loader.render_prompt(
        AgentType.TABULAR_PARSER,
        context_vars={"reference_interval": json.dumps(reference_interval, ensure_ascii=False)},
    )
    print(prompt)
