# Python轻量级工作流系统技术文档

## 1. 系统概述

### 1.1 项目背景

本系统是一个基于Python的轻量级工作流引擎，专门用于处理医疗报告的多步骤分析任务。系统采用**Celery + Redis + FastAPI**技术栈，支持任务的串行/并行执行、失败重试、状态追踪等核心功能。

### 1.2 技术架构

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   FastAPI App   │────▶│   MySQL DB      │     │   Redis Queue   │
│  (API + Poller) │     │  (Task Storage) │     │   (Message Bus) │
└────────┬────────┘     └─────────────────┘     └────────▲────────┘
         │                                                 │
         │              ┌─────────────────────────────────┴────────┐
         └─────────────▶│          Celery Workers                  │
                        ├───────────┬───────────┬─────────┬────────┤
                        │OCR Worker │AI Workers │Post     │API     │
                        │(2 proc)   │(4 proc)   │Workers  │Workers │
                        └───────────┴───────────┴─────────┴────────┘
```

### 1.3 核心特性

- **一体化部署**：FastAPI启动时自动管理所有Worker进程
- **任务编排**：支持复杂的串行/并行任务组合
- **自动重试**：内置智能重试机制，支持指数退避
- **实时监控**：任务状态实时更新，支持进度追踪
- **资源隔离**：不同类型任务使用独立队列和Worker池
- **传统部署**：无需Docker，支持传统服务器环境

## 2. 系统架构详解

### 2.1 组件说明

#### 2.1.1 FastAPI应用层
- **API服务**：提供任务提交、状态查询等RESTful接口
- **任务轮询器**：定时扫描数据库，自动调度待处理任务
- **生命周期管理**：管理Worker进程的启动和关闭

#### 2.1.2 Celery任务层
- **任务定义**：定义各类处理任务（OCR、AI分析、翻译等）
- **工作流编排**：使用Celery的group、chain等原语组合任务
- **状态回调**：任务执行后自动更新数据库状态

#### 2.1.3 存储层
- **MySQL**：存储任务元数据、状态信息
- **Redis**：作为消息队列和结果后端

### 2.2 任务流程

```mermaid
graph TD
    A[任务提交] --> B[写入MySQL]
    B --> C[轮询器扫描]
    C --> D[提交到Celery]
    D --> E[OCR处理]
    E --> F{并行处理}
    F --> G[Meta分析]
    F --> H[Tabular分析]
    F --> I[Overall分析]
    F --> J[Image分析]
    F --> K[Diseases分析]
    G & H & I & J & K --> L[翻译处理]
    L --> M[指标转换]
    M --> N[API调用]
    N --> O[向量存储]
    O --> P[任务完成]
```

### 2.3 任务状态机

每个任务在生命周期中会经历以下状态转换：

```
PENDING ──────┐
    │         │
    ▼         │
PROCESSING    │
    │         │
    ├─────────┤
    │         │
    ▼         ▼
COMPLETED  FAILED
```

子任务状态：
- `*_PROCESSING`：子任务执行中
- `*_COMPLETED`：子任务完成
- `*_FAILED`：子任务失败

## 3. 详细设计

### 3.1 数据库设计

#### 3.1.1 主任务表 (report_tasks)

```sql
CREATE TABLE report_tasks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    report_id VARCHAR(100) UNIQUE NOT NULL COMMENT '报告唯一标识',
    report_key VARCHAR(100) NOT NULL COMMENT '报告密钥',
    status VARCHAR(50) DEFAULT 'pending' COMMENT '任务状态',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='报告任务主表';
```

#### 3.1.2 子任务状态表 (task_steps)

```sql
CREATE TABLE task_steps (
    id INT AUTO_INCREMENT PRIMARY KEY,
    report_id VARCHAR(100) NOT NULL,
    step_name VARCHAR(50) NOT NULL COMMENT '步骤名称',
    status VARCHAR(50) NOT NULL COMMENT '步骤状态',
    start_time TIMESTAMP NULL COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    duration INT COMMENT '耗时(秒)',
    result JSON COMMENT '执行结果',
    error_message TEXT COMMENT '错误信息',
    celery_task_id VARCHAR(100) COMMENT 'Celery任务ID',
    
    UNIQUE KEY uk_report_step (report_id, step_name),
    INDEX idx_report_id (report_id),
    FOREIGN KEY (report_id) REFERENCES report_tasks(report_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务步骤状态表';
```

#### 3.1.3 任务结果表 (task_results)

```sql
CREATE TABLE task_results (
    id INT AUTO_INCREMENT PRIMARY KEY,
    report_id VARCHAR(100) NOT NULL,
    result_type VARCHAR(50) NOT NULL COMMENT '结果类型',
    result_data JSON NOT NULL COMMENT '结果数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_report_type (report_id, result_type),
    FOREIGN KEY (report_id) REFERENCES report_tasks(report_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务结果存储表';
```

### 3.2 配置管理

#### 3.2.1 环境变量配置

```bash
# .env 文件
# Redis配置
REDIS_URL=redis://localhost:6379
REDIS_MAX_CONNECTIONS=50

# MySQL配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=workflow_user
MYSQL_PASSWORD=secure_password
MYSQL_DATABASE=workflow_db

# Celery配置
CELERY_WORKER_CONCURRENCY=4
CELERY_TASK_SOFT_TIME_LIMIT=1800
CELERY_TASK_TIME_LIMIT=2400

# 应用配置
APP_ENV=production
LOG_LEVEL=INFO
POLLING_INTERVAL=5
```

### 3.3 任务实现详解

#### 3.3.1 基础任务类

```python
# base_task.py
from celery import Task
from celery.exceptions import MaxRetriesExceededError
import logging
import time
from typing import Any, Dict
import json

logger = logging.getLogger(__name__)

class BaseWorkflowTask(Task):
    """工作流任务基类"""
    
    autoretry_for = (Exception,)
    retry_kwargs = {'max_retries': 3}
    retry_backoff = True
    retry_backoff_max = 600  # 最大退避时间10分钟
    retry_jitter = True      # 添加随机抖动
    
    def before_start(self, task_id, args, kwargs):
        """任务开始前的钩子"""
        report_id = kwargs.get('report_id')
        if report_id:
            self._update_step_status(report_id, self.name, 'processing', task_id)
            logger.info(f"Task {self.name} started for report {report_id}")
    
    def on_success(self, retval, task_id, args, kwargs):
        """任务成功的钩子"""
        report_id = kwargs.get('report_id')
        if report_id:
            self._update_step_status(
                report_id, self.name, 'completed', 
                task_id, result=retval
            )
            logger.info(f"Task {self.name} completed for report {report_id}")
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """任务失败的钩子"""
        report_id = kwargs.get('report_id')
        if report_id:
            self._update_step_status(
                report_id, self.name, 'failed', 
                task_id, error=str(exc)
            )
            logger.error(f"Task {self.name} failed for report {report_id}: {exc}")
    
    def on_retry(self, exc, task_id, args, kwargs, einfo):
        """任务重试的钩子"""
        report_id = kwargs.get('report_id')
        retry_count = self.request.retries
        logger.warning(
            f"Task {self.name} retry {retry_count} for report {report_id}: {exc}"
        )
    
    def _update_step_status(self, report_id: str, step_name: str, 
                          status: str, task_id: str = None, 
                          result: Any = None, error: str = None):
        """更新步骤状态"""
        from database import get_db_connection
        
        with get_db_connection() as conn:
            with conn.cursor() as cursor:
                if status == 'processing':
                    sql = """
                    INSERT INTO task_steps 
                    (report_id, step_name, status, start_time, celery_task_id)
                    VALUES (%s, %s, %s, NOW(), %s)
                    ON DUPLICATE KEY UPDATE 
                    status = VALUES(status),
                    start_time = VALUES(start_time),
                    celery_task_id = VALUES(celery_task_id)
                    """
                    cursor.execute(sql, (report_id, step_name, status, task_id))
                
                elif status in ['completed', 'failed']:
                    sql = """
                    UPDATE task_steps 
                    SET status = %s, 
                        end_time = NOW(),
                        duration = TIMESTAMPDIFF(SECOND, start_time, NOW()),
                        result = %s,
                        error_message = %s
                    WHERE report_id = %s AND step_name = %s
                    """
                    result_json = json.dumps(result) if result else None
                    cursor.execute(sql, (status, result_json, error, 
                                       report_id, step_name))
                
                conn.commit()
```

#### 3.3.2 具体任务实现

```python
# tasks/ocr_task.py
from celery_app import celery_app
from base_task import BaseWorkflowTask
import time
from typing import Dict
import logging

logger = logging.getLogger(__name__)

@celery_app.task(base=BaseWorkflowTask, bind=True, 
                 time_limit=600, soft_time_limit=540)
def ocr_task(self, report_id: str, report_key: str) -> Dict:
    """
    OCR处理任务
    
    Args:
        report_id: 报告ID
        report_key: 报告密钥
        
    Returns:
        Dict: OCR结果，包含提取的文本和页面信息
    """
    try:
        # 1. 获取报告文件
        file_path = get_report_file(report_id, report_key)
        
        # 2. 执行OCR处理
        logger.info(f"Starting OCR for {report_id}")
        
        # 服务不要实现
        
        # 3. 保存OCR结果
        result = {
            'text': 'XXX',
        }
        
        save_task_result(report_id, 'ocr', result)
        
        return result
        
    except Exception as e:
        logger.error(f"OCR processing failed: {e}")
        raise

def get_report_file(report_id: str, report_key: str) -> str:
    """获取报告文件路径"""
    # 服务不要实现
    return f"/data/reports/{report_id}.pdf"

def save_task_result(report_id: str, result_type: str, data: Dict):
    """保存任务结果到数据库"""
    from database import get_db_connection
    import json
    
    with get_db_connection() as conn:
        with conn.cursor() as cursor:
            sql = """
            INSERT INTO task_results (report_id, result_type, result_data)
            VALUES (%s, %s, %s)
            """
            cursor.execute(sql, (report_id, result_type, json.dumps(data)))
            conn.commit()
```

#### 3.3.3 并行任务组实现

```python
# tasks/ai_tasks.py
from celery import group
from celery_app import celery_app
from base_task import BaseWorkflowTask
import logging
from typing import Dict, List

logger = logging.getLogger(__name__)

@celery_app.task(base=BaseWorkflowTask, bind=True)
def meta_analysis_task(self, report_id: str, ocr_text: str) -> Dict:
    """个人信息提取任务"""
    logger.info(f"Extracting meta info for {report_id}")
    
    # 调用AI服务提取个人信息
    result = {
        'name': 'XXX',
        'age': 'XXX',
        'gender': 'XXX',
        'id_number': 'XXX'
    }
    
    save_task_result(report_id, 'meta', result)
    return result

@celery_app.task(base=BaseWorkflowTask, bind=True)
def tabular_analysis_task(self, report_id: str, ocr_text: str) -> Dict:
    """检查项数据提取任务"""
    logger.info(f"Extracting tabular data for {report_id}")
    
    # 调用AI服务提取表格数据
    # 服务不要实现
    
    result = {
        'indicators': 'XXX'
    }
    
    save_task_result(report_id, 'tabular', result)
    return result

@celery_app.task(base=BaseWorkflowTask, bind=True)
def image_analysis_task(self, report_id: str, ocr_result: Dict) -> Dict:
    """影像数据分析任务"""
    logger.info(f"Analyzing images for {report_id}")
    
    # 提取并分析影像数据
    # 服务不要实现
    
    result = {
        'image_count': 1,
        'analyses': 'XXX'
    }
    
    save_task_result(report_id, 'image', result)
    return result

def create_ai_analysis_group(report_id: str, ocr_result: Dict):
    """创建AI分析任务组"""
    ocr_text = ocr_result.get('text', '')
    
    # 创建并行任务组
    job = group(
        meta_analysis_task.s(report_id=report_id, ocr_text=ocr_text),
        tabular_analysis_task.s(report_id=report_id, ocr_text=ocr_text),
        overall_analysis_task.s(report_id=report_id, ocr_text=ocr_text),
        image_analysis_task.s(report_id=report_id, ocr_result=ocr_result),
        diseases_extraction_task.s(report_id=report_id, ocr_text=ocr_text)
    )
    
    return job
```

### 3.4 工作流编排

#### 3.4.1 主工作流实现

```python
# workflow.py
from celery import chain, group, chord
from celery_app import celery_app
from tasks.ocr_task import ocr_task
from tasks.ai_tasks import create_ai_analysis_group
from tasks.post_process import translation_task, indicator_conversion_task
from tasks.storage_tasks import call_api_task, store_vector_task
import logging
from typing import List, Dict

logger = logging.getLogger(__name__)

@celery_app.task(bind=True)
def main_workflow(self, report_id: str, report_key: str):
    """
    主工作流编排
    
    流程：
    1. OCR处理
    2. AI分析（并行）
    3. 后处理（串行）
    4. 存储（并行）, 调用业务接口、存储到向量数据库
    """
    try:
        # 更新主任务状态
        update_main_task_status(report_id, 'processing')
        
        # 构建工作流
        workflow = chain(
            # 步骤1: OCR处理
            ocr_task.s(report_id=report_id, report_key=report_key),
            
            # 步骤2: AI分析（并行）- 使用chord确保所有任务完成
            chord(
                create_ai_analysis_group.s(report_id=report_id),
                # 步骤3: 后处理 - AI分析完成后执行
                chain(
                    translation_task.s(report_id=report_id),
                    indicator_conversion_task.s(report_id=report_id)
                )
            ),
            
            # 步骤4: 存储（并行）
            group(
                call_api_task.s(report_id=report_id),
                store_vector_task.s(report_id=report_id)
            ),
            
            # 步骤5: 完成处理
            finalize_workflow.s(report_id=report_id)
        )
        
        # 执行工作流
        result = workflow.apply_async()
        
        # 保存工作流ID以便追踪
        save_workflow_id(report_id, result.id)
        
        logger.info(f"Workflow started for report {report_id}, workflow_id: {result.id}")
        
    except Exception as e:
        logger.error(f"Workflow failed to start for {report_id}: {e}")
        update_main_task_status(report_id, 'failed', str(e))
        raise

@celery_app.task
def finalize_workflow(results: List[Dict], report_id: str):
    """完成工作流处理"""
    try:
        # 汇总所有结果
        summary = summarize_results(results)
        
        # 更新主任务状态
        update_main_task_status(report_id, 'completed')
        
        # 发送通知
        send_completion_notification(report_id, summary)
        
        logger.info(f"Workflow completed for report {report_id}")
        
    except Exception as e:
        logger.error(f"Failed to finalize workflow for {report_id}: {e}")
        update_main_task_status(report_id, 'failed', str(e))
        raise

def update_main_task_status(report_id: str, status: str, error: str = None):
    """更新主任务状态"""
    from database import get_db_connection
    
    with get_db_connection() as conn:
        with conn.cursor() as cursor:
            if status == 'completed':
                sql = """
                UPDATE report_tasks 
                SET status = %s, completed_at = NOW()
                WHERE report_id = %s
                """
                cursor.execute(sql, (status, report_id))
            else:
                sql = """
                UPDATE report_tasks 
                SET status = %s, error_message = %s, updated_at = NOW()
                WHERE report_id = %s
                """
                cursor.execute(sql, (status, error, report_id))
            
            conn.commit()
```

#### 3.4.2 错误处理和补偿机制

```python
# error_handling.py
from celery_app import celery_app
from celery.exceptions import Retry
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

class WorkflowError(Exception):
    """工作流错误基类"""
    pass

class RetryableError(WorkflowError):
    """可重试的错误"""
    pass

class FatalError(WorkflowError):
    """致命错误，不应重试"""
    pass

@celery_app.task(bind=True, max_retries=3)
def error_handler(self, request, exc, traceback, report_id: str):
    """
    统一错误处理器
    
    根据错误类型决定是否重试或执行补偿操作
    """
    exc_type = type(exc).__name__
    exc_message = str(exc)
    
    logger.error(f"Task {request.task} failed for {report_id}: {exc_type} - {exc_message}")
    
    # 记录错误
    record_error(report_id, request.task, exc_type, exc_message, traceback)
    
    # 根据错误类型处理
    if isinstance(exc, RetryableError):
        # 可重试错误
        retry_count = self.request.retries
        if retry_count < self.max_retries:
            logger.info(f"Retrying task {request.task} for {report_id}, attempt {retry_count + 1}")
            raise self.retry(countdown=60 * (2 ** retry_count))  # 指数退避
        else:
            # 达到最大重试次数，执行补偿
            execute_compensation(report_id, request.task)
            
    elif isinstance(exc, FatalError):
        # 致命错误，直接失败
        logger.error(f"Fatal error in task {request.task} for {report_id}, no retry")
        execute_compensation(report_id, request.task)
        update_main_task_status(report_id, 'failed', exc_message)
        
    else:
        # 其他错误，尝试重试
        if self.request.retries < self.max_retries:
            raise self.retry(exc=exc, countdown=60)
        else:
            execute_compensation(report_id, request.task)

def execute_compensation(report_id: str, failed_task: str):
    """执行补偿操作"""
    logger.info(f"Executing compensation for {report_id}, failed task: {failed_task}")
    
    compensation_map = {
        'ocr_task': compensate_ocr_failure,
        'meta_analysis_task': compensate_ai_failure,
        'call_api_task': compensate_api_failure,
    }
    
    compensation_func = compensation_map.get(failed_task)
    if compensation_func:
        compensation_func(report_id)
    
    # 清理部分完成的数据
    cleanup_partial_data(report_id, failed_task)

def record_error(report_id: str, task_name: str, 
                error_type: str, error_message: str, 
                traceback: str = None):
    """记录错误信息到数据库"""
    from database import get_db_connection
    
    with get_db_connection() as conn:
        with conn.cursor() as cursor:
            sql = """
            INSERT INTO task_errors 
            (report_id, task_name, error_type, error_message, traceback, created_at)
            VALUES (%s, %s, %s, %s, %s, NOW())
            """
            cursor.execute(sql, (report_id, task_name, error_type, 
                               error_message, traceback))
            conn.commit()
```

### 3.5 监控和管理

Flower 监控

## 4 Worker 管理器 (worker_manager.py)
```python
import subprocess
import signal
import sys
import os
import time
import logging
from threading import Thread

logger = logging.getLogger(__name__)

class WorkerManager:
    def __init__(self):
        self.processes = []
        self.running = False
        
    def start_workers(self):
        """启动所有 worker 进程"""
        self.running = True
        
        # 定义不同类型的 worker
        worker_configs = [
            # OCR worker - 较少并发
            {
                'name': 'ocr_worker',
                'queues': 'ocr_queue',
                'concurrency': 2,
                'pool': 'prefork'
            },
            # AI 分析 worker - 中等并发
            {
                'name': 'ai_worker',
                'queues': 'ai_queue',
                'concurrency': 4,
                'pool': 'prefork'
            },
            # 后处理 worker
            {
                'name': 'post_worker',
                'queues': 'post_queue',
                'concurrency': 3,
                'pool': 'prefork'
            },
            # API 调用 worker
            {
                'name': 'api_worker',
                'queues': 'api_queue',
                'concurrency': 2,
                'pool': 'prefork'
            }
        ]
        
        for config in worker_configs:
            self._start_worker(**config)
            
        # 启动 Beat 调度器（如果需要定时任务）
        self._start_beat()
        
        logger.info("All workers started successfully")
        
    def _start_worker(self, name, queues, concurrency, pool):
        """启动单个 worker"""
        cmd = [
            'celery', '-A', 'celery_app', 'worker',
            '--loglevel=info',
            f'--hostname={name}@%h',
            f'--queues={queues}',
            f'--concurrency={concurrency}',
            f'--pool={pool}',
            '--without-gossip',
            '--without-mingle',
            '--without-heartbeat'
        ]
        
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        self.processes.append(process)
        logger.info(f"Started {name} with PID {process.pid}")
        
    def _start_beat(self):
        """启动 Celery Beat（定时任务调度器）"""
        cmd = ['celery', '-A', 'celery_app', 'beat', '--loglevel=info']
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        self.processes.append(process)
        logger.info(f"Started Celery Beat with PID {process.pid}")
        
    def stop_workers(self):
        """停止所有 worker 进程"""
        logger.info("Stopping all workers...")
        self.running = False
        
        for process in self.processes:
            try:
                process.terminate()
                process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                process.kill()
                
        self.processes.clear()
        logger.info("All workers stopped")
        
    def monitor_workers(self):
        """监控 worker 健康状态"""
        while self.running:
            for i, process in enumerate(self.processes):
                if process.poll() is not None:
                    logger.warning(f"Worker {i} died, restarting...")
                    # 重启逻辑
            time.sleep(5)

# 全局 worker 管理器实例
worker_manager = WorkerManager()
```

## 5.FastAPI 主应用 (app.py)

```python
from fastapi import FastAPI, BackgroundTasks
from contextlib import asynccontextmanager
import asyncio
import logging
from worker_manager import worker_manager
from tasks import process_report_workflow
import pymysql
from typing import Dict

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时
    logger.info("Starting application...")
    
    # 启动 Celery workers
    worker_manager.start_workers()
    
    # 启动任务轮询器
    task_poller = TaskPoller()
    asyncio.create_task(task_poller.start_polling())
    
    yield
    
    # 关闭时
    logger.info("Shutting down application...")
    worker_manager.stop_workers()
    task_poller.stop()

app = FastAPI(lifespan=lifespan)

class TaskPoller:
    """任务轮询器"""
    def __init__(self):
        self.running = True
        self.db_connection = None
        
    async def start_polling(self):
        """开始轮询数据库"""
        while self.running:
            try:
                # 查询待处理任务
                tasks = self.fetch_pending_tasks()
                
                for task in tasks:
                    # 提交到 Celery
                    process_report_workflow.delay(
                        report_id=task['report_id'],
                        report_key=task['report_key']
                    )
                    
                    # 更新任务状态为处理中
                    self.update_task_status(task['report_id'], 'processing')
                    
            except Exception as e:
                logger.error(f"Polling error: {e}")
                
            await asyncio.sleep(5)  # 每5秒轮询一次
    
    def fetch_pending_tasks(self) -> list:
        """从数据库获取待处理任务"""
        # 实现数据库查询逻辑
        connection = pymysql.connect(**MYSQL_CONFIG)
        try:
            with connection.cursor() as cursor:
                sql = """
                SELECT report_id, report_key 
                FROM report_tasks 
                WHERE status = 'pending' 
                LIMIT 10
                """
                cursor.execute(sql)
                return cursor.fetchall()
        finally:
            connection.close()
    
    def update_task_status(self, report_id: str, status: str):
        """更新任务状态"""
        # 实现数据库更新逻辑
        pass
        
    def stop(self):
        self.running = False

@app.post("/submit_task")
async def submit_task(report_id: str, report_key: str) -> Dict:
    """提交新任务"""
    # 写入数据库
    connection = pymysql.connect(**MYSQL_CONFIG)
    try:
        with connection.cursor() as cursor:
            sql = """
            INSERT INTO report_tasks (report_id, report_key, status) 
            VALUES (%s, %s, 'pending')
            """
            cursor.execute(sql, (report_id, report_key))
            connection.commit()
    finally:
        connection.close()
    
    return {"status": "submitted", "report_id": report_id}

@app.get("/task_status/{report_id}")
async def get_task_status(report_id: str) -> Dict:
    """查询任务状态"""
    # 实现状态查询逻辑
    return {"report_id": report_id, "status": "processing"}
```