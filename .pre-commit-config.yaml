# ============================================================
# Pre-commit Configuration - 代码格式化
# ============================================================
# 在每次 git commit 前自动运行代码格式化工具
# 确保代码风格一致性

repos:
  # Python 代码格式化
  - repo: https://github.com/psf/black
    rev: 24.2.0
    hooks:
      - id: black
        name: black
        language_version: python3
        args: ["--line-length", "120"]

  # Python 代码检查和自动修复（包含 isort 功能）
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.2.2
    hooks:
      - id: ruff
        name: ruff
        args: ["--fix", "--line-length", "120"]
      - id: ruff-format
        name: ruff-format
        args: ["--line-length", "120"]

  # 基础文件检查
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
        name: trim trailing whitespace
      - id: end-of-file-fixer
        name: fix end of files
      - id: check-merge-conflict
        name: check for merge conflicts
      - id: check-added-large-files
        name: check for added large files
