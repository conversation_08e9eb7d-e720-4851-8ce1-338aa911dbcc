.PHONY: help install install-dev install-all dev test test-unit test-integration lint format type-check quality clean start

help:
	@echo "可用命令："
	@echo "  install      安装生产环境依赖"
	@echo "  install-dev  安装开发环境依赖（包括所有开发工具）"
	@echo "  install-all  安装所有依赖（等同于 install-dev）"
	@echo "  dev          启动开发服务器"
	@echo "  test         运行所有测试"
	@echo "  test-unit    运行单元测试"
	@echo "  test-integration 运行集成测试"
	@echo "  lint         代码检查"
	@echo "  format       格式化代码"
	@echo "  type-check   类型检查"
	@echo "  quality      运行所有质量检查"
	@echo "  clean        清理临时文件"

install:
	pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
	python -m playwright install chromium

install-dev:
	pip install -r requirements-dev.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
	python -m playwright install chromium
	pre-commit install

install-all: install-dev

dev:
	fastapi dev app/main.py

start:
	uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

test:
	pytest -v --cov=app --cov-report=term-missing

test-unit:
	pytest tests/unit/ -v

test-integration:
	pytest tests/integration/ -v

lint:
	ruff check .

format:
	ruff check --fix
	ruff format

type-check:
	mypy .

quality: lint type-check
	@echo "所有质量检查完成"

clean:
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	rm -rf .coverage htmlcov/ .pytest_cache/ .mypy_cache/
